﻿@using BuddyPunch.Web.App.Helpers
@using BuddyPunch.Web.App.ViewModel
@model BuddyPunch.Web.App.ViewModel.JobCodeListViewModel
@{
    ViewBag.Title = "Department Codes";
}

@Html.Partial("_ErrorSuccessPartial")

<!-- BEGIN PAGE HEADER-->
<div class="m-subheader">
    <h3 class="m-subheader__title ">
        Department Codes
    </h3>
</div>
<!-- END PAGE HEADER-->
<!-- BEGIN PAGE CONTENT-->

<div class="alert alert-block alert-info">
    <span>
        Department codes are like job codes.<br />
        You first create them here and then assign departments to employees by visiting the employees profile in Employees -> View.<br />
        When employees go to punch in they can select a department code to punch into.
    </span>
</div>

@if (TempData["SuccessMessage"] != null)
{
    <div class="alert alert-block alert-info">
        <span>
            @TempData["SuccessMessage"]
        </span>
    </div>
}

@using (Html.BeginMetronicPortlet("Settings"))
{
    using (Html.BeginForm("SaveSettings", "JobCode"))
    {
        <div class="form-group m-form__group row align-items-center">
            <div class="col-md-12">
                <div class="m--padding-top-10 m--padding-bottom-10">
                    @Html.MetronicCheckboxFor(m => m.SeparateDepartmentDropdown, "Split Departments into a separate drop down when Locations are used with Time Entries (duration).")
                </div>
                <div class="m--padding-top-10">
                    <input type="submit" value="Save" class="btn btn-primary" />
                </div>
            </div>
        </div>
    }
}

<p>
    <a href="@Url.Action("Create")" class="btn btn-default"><i class="fa fa-plus"></i> Add a New Department Code</a>
</p>

@helper RenderTabHeader()
{
    <li class="nav-item m-tabs__item">
        <a href="#tab_6_0" data-toggle="tab" class="nav-link m-tabs__link active active-jobCodes-tab" role="tab" aria-expanded="false">Active (@Model.JobCodes.Count(e => e.Enabled))</a>
    </li>
    <li class="nav-item m-tabs__item">
        <a href="#tab_6_1" data-toggle="tab" class="nav-link m-tabs__link inactiv-jobCodes-tab" role="tab" aria-expanded="false">Inactive (@Model.JobCodes.Count(e => !e.Enabled))</a>
    </li>
    <li class="nav-item m-tabs__item">
        <a href="#tab_6_2" data-toggle="tab" class="nav-link m-tabs__link all-jobCodes-tab" role="tab" aria-expanded="false">All (@Model.JobCodes.Count())</a>
    </li>
}

@helper RenderJobCodes(IEnumerable<JobCodeViewModel> jobCodes, string id)
{
    <div class="form-group m-form__group row align-items-center">
        <div class="col-md-4">
            <div class="m-input-icon m-input-icon--left">
                <input type="text" class="form-control m-input m-input--solid" placeholder="Search..." id="@(id + "_search")">
                <span class="m-input-icon__icon m-input-icon__icon--left">
                    <span>
                        <i class="la la-search"></i>
                    </span>
                </span>
            </div>
        </div>
        <div class="col-md-8 m--align-right">
            <a href="javascript:void(0);" class="activate-jobCode btn btn-primary hide">Activate</a>
            <a href="javascript:void(0);" class="deactivate-jobCode btn btn-primary hide">Deactivate</a>
        </div>
    </div>
    <table class="table table-striped table-hover table-fixed-header" id="@id">
        <thead>
            <tr>
                <th>Actions</th>
                <th>Name</th>
                <th>Employees Assigned</th>
                <th>Active</th>
                <th>Billable</th>
                <th class="table-select-col no-sort">
                    <input type="checkbox" style="width: 18px; height: 18px" class="activate-jobCode-cb-all" />
                </th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in jobCodes)
            {
                <tr>

                    <td>
                        <a href="@Url.Action("Edit", new {id = item.Id})" class="btn btn-xs btn-sm btn-default"><i class="fa fa-edit"></i> Edit</a>
                    </td>

                    <td>
                        @item.Name
                    </td>

                    <td>@item.EmployeesAssigned</td>
                    <td>@(item.Enabled ? "Yes" : "No")</td>
                    <td>@(item.Billable ? "Yes" : "No")</td>
                    <td class="table-select-col">
                        <input type="checkbox" class="activate-jobCode-cb" style="width: 18px; height: 18px" data-id="@item.Id" />
                    </td>
                </tr>
            }
        </tbody>
    </table>
}

@using (Html.BeginMetronicTabPortlet(RenderTabHeader(), "Department Codes"))
{
    <div class="tab-content">
        <div class="tab-pane active" id="tab_6_0">
            @RenderJobCodes(Model.JobCodes.Where(j => j.Enabled).OrderBy(e => e.Name), "jobcodes_active_table")
        </div>
        <div class="tab-pane" id="tab_6_1">
            @RenderJobCodes(Model.JobCodes.Where(j => !j.Enabled).OrderBy(e => e.Name), "jobcodes_inactive_table")
        </div>
        <div class="tab-pane" id="tab_6_2">
            @RenderJobCodes(Model.JobCodes.OrderBy(e => e.Name), "jobcodes_table")
        </div>
    </div>
}

@section PageHeadSection
{

}
@section PageBeforeEndBodySection
    {
    <script type="text/javascript">
        jQuery(document).ready(function () {

            initActionsDataTable('#jobcodes_table', {
                "order": [[2, "asc"]],
                "columnDefs": [{
                    "targets": 'no-sort',
                    "orderable": false,
                    "order": []
                }]
            });
            initActionsDataTable('#jobcodes_active_table', {
                "order": [[2, "asc"]],
                fixedHeader: {
                    headerOffset: $('.m-header').outerHeight(),
                },
                "columnDefs": [{
                    "targets": 'no-sort',
                    "orderable": false,
                    "order": []
                }]
            });
            initActionsDataTable('#jobcodes_inactive_table', {
                "order": [[2, "asc"]],
                "columnDefs": [{
                    "targets": 'no-sort',
                    "orderable": false,
                    "order": []
                }]
            });

            var selectedArray = [];
            var selectedTab = 1;

            $(document).on('click', '.activate-jobCode-cb', function () {
                var checked = $(this).prop('checked');
                var id = $(this).data('id');

                if (checked) {
                    if (!selectedArray.includes(id)) {
                        selectedArray.push(id);
                    }
                } else {
                    selectedArray = selectedArray.filter(empId => empId !== id);
                }

                updateActionButtonsVisibility();
            });

            $(document).on('click', '.active-jobCodes-tab', function () {
                resetJobCodeSelect(1);
                $('.table-select-col.hide').each(function () {
                    $(this).removeClass('hide');
                });
            });

            $(document).on('click', '.inactiv-jobCodes-tab', function () {
                resetJobCodeSelect(2);
                $('.table-select-col.hide').each(function () {
                    $(this).removeClass('hide');
                });
            });

            $(document).on('click', '.all-jobCodes-tab', function () {
                resetJobCodeSelect(3);
                $('.table-select-col').addClass('hide');
            });

            $(document).on('change', '.activate-jobCode-cb-all', function () {
                var isChecked = $(this).prop('checked');
                var checkboxes = $(this).closest('.tab-pane').find('.activate-jobCode-cb');

                checkboxes.prop('checked', isChecked);

                selectedArray = isChecked
                    ? checkboxes.map(function () {
                        return $(this).data('id');
                    }).get()
                    : [];

                updateActionButtonsVisibility();
            });

            $(document).on('click', '.activate-jobCode', function (e) {
                e.preventDefault();

                if (selectedArray.length === 0) {
                    return;
                }
                $.post("@Url.Action("ActivateAll", "JobCode")", {
                    ids: ArrayToString(selectedArray),
                    activate: true
                }, function () {
                    window.location.href = '@Url.Action("Index", "JobCode")';
                });

                resetJobCodeSelect(selectedTab);
            });

            $(document).on('click', '.deactivate-jobCode', function (e) {
                e.preventDefault();

                if (selectedArray.length === 0) {
                    return;
                }
                $.post("@Url.Action("ActivateAll", "JobCode")", {
                    ids: ArrayToString(selectedArray),
                    activate: false
                }, function () {
                    window.location.href = '@Url.Action("Index", "JobCode")';
                });

                resetJobCodeSelect(selectedTab);
            });

            function updateActionButtonsVisibility() {
                if (selectedArray.length > 0) {
                    if (selectedTab === 1) {
                        $('.deactivate-jobCode').removeClass('hide');
                    } else if (selectedTab === 2) {
                        $('.activate-jobCode').removeClass('hide');
                    }
                } else {
                    if (selectedTab === 1) {
                        $('.deactivate-jobCode').addClass('hide');
                    } else if (selectedTab === 2) {
                        $('.activate-jobCode').addClass('hide');
                    }
                }
            }

            function resetJobCodeSelect(tab) {
                selectedTab = tab;
                selectedArray = [];
                $('.activate-jobCode-cb').each(function () {
                    $(this).prop('checked', false);
                });
                $('.activate-jobCode-cb-all').each(function () {
                    $(this).prop('checked', false);
                });
                if ($('.activate-jobCode').hasClass('hide') == false) {
                    $('.activate-jobCode').addClass('hide');
                }
                if ($('.deactivate-jobCode').hasClass('hide') == false) {
                    $('.deactivate-jobCode').addClass('hide');
                }
            }

            // converts an array of items to a concatenated semicolumn joined string for example: [1, 2, 3] => "1;2;3"
            function ArrayToString(arr) {
                if (arr.length == 0) {
                    return;
                }
                var r = "";
                for (var i = 0; i < arr.length; i++) {
                    r = r + arr[i] + ";";
                }
                r = r.substring(0, r.length - 1);
                return r;
            }
        });
    </script>
}