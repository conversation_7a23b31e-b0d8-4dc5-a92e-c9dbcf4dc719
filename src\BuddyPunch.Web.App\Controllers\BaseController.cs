﻿using BuddyPunch.Model.Enums;
using BuddyPunch.Service;
using BuddyPunch.Web.App.Helpers;
using BuddyPunch.Web.App.Managers;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Security.Claims;
using System.Threading;
using System.Web;
using System.Web.Mvc;
using System.Web.Security;

namespace BuddyPunch.Web.App.Controllers
{
    [NoCache]
    public class BaseController : BaseControllerBase
    {
        private int _userId;
        private Stopwatch _stopwatch = new Stopwatch();

        protected int UserId
        {
            get
            {
                if (_userId <= 0)
                {
                    var claimsIdentity = User.Identity as ClaimsIdentity;
                    if (claimsIdentity != null)
                    {
                        _userId = Convert.ToInt32(claimsIdentity.FindFirst(ClaimTypes.PrimarySid).Value);
                    }
                    else
                    {
                        var membershipUser = Membership.GetUser(User.Identity.Name);
                        if (membershipUser == null)
                        {
                            throw new NullReferenceException("membershipUser");
                        }
                        _userId = Convert.ToInt32(membershipUser.ProviderUserKey);
                    }
                }
                return _userId;
            }
        }

        public void SetSuccessMessageFormat(string format, params object[] args)
        {
            TempData["SuccessMessage"] = string.Format(format, args);
        }

        public void SetErrorMessageFormat(string format, params object[] args)
        {
            TempData["ErrorMessage"] = string.Format(format, args);
        }

        protected JsonResult JsonError(Exception e)
        {
            if (e != null)
            {
                Response.TrySkipIisCustomErrors = true;
                var errorCode = (e as HttpException)?.GetHttpCode();
                Response.StatusCode = errorCode ?? 400;
#if DEBUG
                return Json(new { messages = new[] { e.Message }, isError = true, exception = e.ToString() }, JsonRequestBehavior.AllowGet);
#else
                return JsonError(e.Message);
#endif
            }
            else
            {
                return JsonError("No error info provided");
            }
        }

        protected JsonResult JsonError(string message)
        {
            return JsonError(new[] { message });
        }

        protected JsonResult JsonError(IEnumerable<string> messages)
        {
            Response.TrySkipIisCustomErrors = true;
            Response.StatusCode = 400;
            return Json(new { messages, isError = true }, JsonRequestBehavior.AllowGet);
        }

        protected JsonResult JsonSuccess(string message)
        {
            return Json(new { messages = new[] { message } }, JsonRequestBehavior.AllowGet);
        }

        protected bool IsJsonRequest()
        {
            return Request.AcceptTypes != null && Request.AcceptTypes.Contains("application/json");
        }

        protected override void OnActionExecuting(ActionExecutingContext context)
        {
            _stopwatch.Restart();

            var isAnonymous = context.ActionDescriptor.GetCustomAttributes(inherit: true)
                .Any(a => a.GetType().Equals(typeof(AllowAnonymousAttribute)));

            var loggedInUserViewModel = SessionManager.Profile.LoggedInUserViewModel;
            if (!isAnonymous && loggedInUserViewModel != null
                             && !(context.Controller is MasterController || context.Controller is ErrorController)
                             && context.ActionDescriptor.ActionName != "KeepAlive")
            {
                // Add auth info to context
                context.HttpContext.Items["ContextUserId"] = loggedInUserViewModel.Id;
                context.HttpContext.Items["ContextAccountId"] = loggedInUserViewModel.AccountId;

                if (!context.HttpContext.Request.IsAjaxRequest()){

                    // Redirect unpaid users to the billing area
                    var stripeCustomer = loggedInUserViewModel.Account?.StripeCustomer;
                    if (!loggedInUserViewModel.IsConfigurator && stripeCustomer?.SubscriptionStatus != null &&
                        ShouldLockOut(stripeCustomer.Payments, stripeCustomer.SubscriptionStatus))
                    {
                        var allowHandleBilling = loggedInUserViewModel.Account.AccountTypeEnum != AccountTypeEnum.ResellerClient
                                              || loggedInUserViewModel.IsResellerAdmin;

                        if (context.Controller is AccountController
                            && new[]
                                {
                                    "Cancel", "PurchasePlan", "ViewPlan", "ChangePaymentMethod", "RetryPayment",
                                    "ChangePlan", "CustomerPortal", "LogOff"
                                }
                                .Contains(context.ActionDescriptor.ActionName)
                            && allowHandleBilling)
                        {
                            // allow to continue to handle billing
                        }
                        else if (context.Controller is StripeController && allowHandleBilling)
                        {
                            // allow to continue to handle billing
                        }
                        else if (context.Controller is EmployeeController
                                 && new[]
                                     {
                                         nameof(EmployeeController.Index), nameof(EmployeeController.View),
                                         nameof(EmployeeController.Edit), nameof(EmployeeController.Activate),
                                         nameof(EmployeeController.DeleteEmployee)
                                     }
                                     .Contains(context.ActionDescriptor.ActionName))
                        {
                            // allow to continue to deactivate employee
                        }
                        else
                        {
                            // Double check db for updates
                            var securityService = Resolver.GetService<ISecurityService>();
                            var user = securityService.GetUser(UserId, readOnly: true);
                            var accountStripeCustomer = user.Account.AccountStripeCustomer;

                            if (accountStripeCustomer?.SubscriptionStatus != null &&
                                ShouldLockOut(accountStripeCustomer.Payments, accountStripeCustomer.SubscriptionStatus))
                            {
                                if (allowHandleBilling)
                                {
                                    TempData["NoSubscriptionRedirectToPurchase"] = true;
                                    context.Result = RedirectToAction("ViewPlan", "Account");
                                    return;
                                }
                                else
                                {
                                    TempData["ErrorMessage"] = "Your subscription has expired.";
                                    context.Result = RedirectToAction("Logon", "Account");
                                    return;
                                }
                            }
                        }
                    }
                }
            }

            base.OnActionExecuting(context);
        }

        protected bool ShouldLockOut(int? previousPayments, string subscriptionStatus)
        {
            if (subscriptionStatus.ToLower().Equals(Stripe.SubscriptionStatuses.Unpaid,
                            StringComparison.InvariantCultureIgnoreCase))
            {
                return true;
            }
            if (previousPayments.GetValueOrDefault() <= 0 && subscriptionStatus.ToLower().Equals(Stripe.SubscriptionStatuses.PastDue,
                            StringComparison.InvariantCultureIgnoreCase))
            {
                return true;
            }
            return false;
        }

        protected override void OnActionExecuted(ActionExecutedContext filterContext)
        {
            base.OnActionExecuted(filterContext);

            _stopwatch.Stop();
            if (_stopwatch.ElapsedMilliseconds >= 1000)
            {
                Debug.WriteLine($"Long running request ({_stopwatch.ElapsedMilliseconds} ms) - {filterContext.HttpContext.Request.Url}", 
                    "Performance");
            }
        }

        public bool LoggedInWithMFA()
        {
            var mfaClaim = (User.Identity as ClaimsIdentity)?.FindFirst(CustomClaimType.MFA);
            return string.Equals(mfaClaim?.Value, true.ToString(), StringComparison.InvariantCultureIgnoreCase);
        }

        public bool IsMetronic9Enabled
        {
            get
            {
                var theme = Request.QueryString["theme"];
                return (!string.IsNullOrEmpty(theme) && theme.ToLower() == "metronic");
            }

        }
    }


}