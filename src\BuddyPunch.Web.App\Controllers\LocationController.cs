﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web.Mvc;
using AutoMapper;
using BuddyPunch.Api.Models;
using BuddyPunch.Api.Models.Api;
using BuddyPunch.Service;
using BuddyPunch.Web.App.Mappers;
using BuddyPunch.Web.App.ViewModel;
using BuddyPunch.Web.App.Helpers;
using BuddyPunch.Model.Enums;
using LocationViewModel = BuddyPunch.Web.App.ViewModel.LocationViewModel;
using System.Linq.Expressions;
using BuddyPunch.Model;
using BuddyPunch.Web.App.Dto;
using BuddyPunch.Web.App.Exceptions;
using BuddyPunch.Web.App.Models;
using System.Web;

namespace BuddyPunch.Web.App.Controllers
{
    [AuthorizeRole(Role = RoleEnum.Administrator)]
    public class LocationController : AuthenticatedController
    {
        private readonly ILocationService _locationService;
        private readonly IJobCodeService _jobCodeService;
        private readonly IStaffService _staffService;
        private readonly ILocationApi _locationApiClient;

        public LocationController(ILocationService locationService, ISecurityService securityService, IJobCodeService jobCodeService,
            IStaffService staffService, ILocationApi locationApiClient)
            : base(securityService)
        {
            _locationService = locationService;
            _jobCodeService = jobCodeService;
            _staffService = staffService;
            _locationApiClient = locationApiClient;
        }

        //
        // GET: /Location/

        public ActionResult Index()
        {

            var theme = Request.QueryString["theme"];
            if (string.IsNullOrEmpty(theme) || theme.ToLower() != "metronic")
            {
                var currentUrl = Request.Url.AbsolutePath;
                var query = HttpUtility.ParseQueryString(Request.Url.Query);
                query["theme"] = "metronic";
                var newUrl = currentUrl + "?" + query.ToString();
                return Redirect(newUrl);
            }
            var viewModel = new LocationListViewModel();
            viewModel.Locations = _locationService.GetAllForList(LoggedInUser.Account).Select(l => LocationMapper.MapFrom(l));
            return View(viewModel);
        }


        // GET: /Location/Create

        public ActionResult Create()
        {
            var theme = Request.QueryString["theme"];
            if (string.IsNullOrEmpty(theme) || theme.ToLower() != "metronic")
            {
                var currentUrl = Request.Url.AbsolutePath;
                var query = HttpUtility.ParseQueryString(Request.Url.Query);
                query["theme"] = "metronic";
                var newUrl = currentUrl + "?" + query.ToString();
                return Redirect(newUrl);
            }
            var viewModel = new LocationViewModel();
            viewModel.Enabled = true;
            viewModel.LocationStaffIds = new List<int>();

            viewModel.Employees = LoadEmployees();
            viewModel.LocationJobCodeIds = new List<int>();
            viewModel.JobCodes = _jobCodeService.GetAll(LoggedInUser.Account).Select(j => JobCodeMapper.MapFrom(j)).Where(j => j.Enabled).OrderBy(j => j.Name);

            return View(viewModel);
        }

        //
        // POST: /Location/Create

        [HttpPost]
        public async Task<ActionResult> Create(LocationViewModel viewModel)
        {
            if (ModelState.IsValid)
            {
                var newLocation = await _locationApiClient.CreateAsync(Mapper.Map<LocationBaseViewModel>(viewModel));

                await _locationApiClient.AssignEmployeesAsync(newLocation.Id, new AssignEmployeesViewModel(){EmployeeIds = viewModel.LocationStaffIds});
                await _locationApiClient.AssignJobCodesAsync(newLocation.Id, new AssignJobCodesViewModel(){JobCodeIds = viewModel.LocationJobCodeIds});

                return RedirectToAction("Index");
            }
            else
            {
                // Refill the list so that the employee selection is full again
                viewModel.Employees = LoadEmployees();
                viewModel.JobCodes = _jobCodeService.GetAll(LoggedInUser.Account).Select(j => JobCodeMapper.MapFrom(j)).Where(j => j.Enabled).OrderBy(j => j.Name);
            }

            return View(viewModel);
        }

        //
        // GET: /Location/Edit/5

        public async Task<ActionResult> Edit(int id = 0)
        {
            var theme = Request.QueryString["theme"];
            if (string.IsNullOrEmpty(theme) || theme.ToLower() != "metronic")
            {
                var currentUrl = Request.Url.AbsolutePath;
                var query = HttpUtility.ParseQueryString(Request.Url.Query);
                query["theme"] = "metronic";
                var newUrl = currentUrl + "?" + query.ToString();
                return Redirect(newUrl);
            }
            var locationIncludes = new Expression<Func<Location, object>>[]
            {
                l => l.JobCodes,
                l => l.Geofences,
                l => l.Shifts,
                l => l.StaffLocations.Select(p => p.Staff),
                l => l.StaffLocations.Select(s => s.Staff.StaffLocations),
                l => l.StaffLocations.Select(s => s.Staff.StaffGeofences),
                l => l.StaffLocations.Select(s => s.Staff.Shifts),                
                l => l.Account.Geofences
            };
            var location = _locationService.GetById(LoggedInUser.Account, id, locationIncludes, readOnly: true);        
            if (location == null)
            {
                return HttpNotFound();
            }

            var apiModel = await _locationApiClient.GetAsync(id);

            var viewModel = Mapper.Map<LocationViewModel>(apiModel);

            viewModel.LocationJobCodeIds = location.JobCodes.Select(j => j.Id);
            viewModel.JobCodes = _jobCodeService.GetAll(LoggedInUser.Account).Select(j => JobCodeMapper.MapFrom(j)).Where(j => j.Enabled).OrderBy(j => j.Name);

            viewModel.LocationStaffIds= location.StaffLocations.Select(j => j.StaffId);

            if (location.Account.Geofences.Any(g => g.Active))
            {
                viewModel.LocationGeofenceStaffIds = location.StaffLocations
                    .Where(s => s.Staff.GetLocationsFromGeofences().Contains(location)).Select(j => j.StaffId);

                viewModel.LocationGeofenceJobCodeIds = location.Geofences
                    .Where(x => x.Active && x.AutoAssignEnabled && x.JobCodeId.HasValue).Select(x => x.JobCodeId.Value);
            }

            viewModel.LocationShiftsStaffIds = location.StaffLocations
                .Where(s => s.Staff.GetLocationsFromCurrentAndFutureShifts().Contains(location)).Select(j => j.StaffId);

            viewModel.Employees = LoadEmployees();

            var canDisable = _locationService.CanDisable(location.Id, out var disableErrors);
            viewModel.CanDisable = canDisable;
            viewModel.CanDelete = canDisable && _locationService.CanDelete(location.Id);
            viewModel.DisableErrors = disableErrors;

            return View(viewModel);
        }

        private IEnumerable<EmployeeViewModel> LoadEmployees()
        {
            var includes = new Expression<Func<Staff, object>>[]
            {
                s => s.User,
                s => s.User.Roles,
                s => s.User.UserPin,
                s => s.PayType
            };

            return _staffService.GetAll(LoggedInUser, includes, readOnly: true)
                .Select(EmployeeMapper.MapFrom)
                .Where(c => c.IsActive)
                .OrderBy(c => c.FullName)
                .ToList();
        }

        //
        // POST: /Location/Edit/5

        [HttpPost]
        public async Task<ActionResult> Edit(LocationViewModel viewModel)
        {
            var location = _locationService.GetById(LoggedInUser.Account, viewModel.Id);
            if (location == null)
            {
                return HttpNotFound();
            }

            if (ModelState.IsValid)
            {
                await _locationApiClient.UpdateAsync(viewModel.Id, Mapper.Map<LocationBaseViewModel>(viewModel));

                await _locationApiClient.RemoveJobCodesAsync(viewModel.Id,
                    new AssignJobCodesViewModel() {JobCodeIds = location.JobCodes.Select(j => j.Id)});
                await _locationApiClient.AssignJobCodesAsync(viewModel.Id,
                    new AssignJobCodesViewModel() {JobCodeIds = viewModel.LocationJobCodeIds});

                var includes = new Expression<Func<Staff, object>>[]
                {
                    s => s.StaffLocations.Select(sl => sl.Location),
                    s => s.StaffGeofences.Select(sl => sl.Geofence),
                    s => s.Shifts,
                };

                var staffMembers = _staffService.GetAll(LoggedInUser, includes).ToList();

                //remove active employees
                var employeesToRemove = new List<int>();
                foreach (var staff in staffMembers.Where(s => s.IsActive && !s.GetLocationsFromGeofences().Concat(s.GetLocationsFromCurrentAndFutureShifts())
                    .Contains(location)))
                {
                    employeesToRemove.Add(staff.Id);
                }
                await _locationApiClient.RemoveEmployeesAsync(viewModel.Id,
                    new AssignEmployeesViewModel() {EmployeeIds = employeesToRemove});


                //re-add any active employees
                await _locationApiClient.AssignEmployeesAsync(viewModel.Id,
                    new AssignEmployeesViewModel() {EmployeeIds = viewModel.LocationStaffIds});

                return RedirectToAction("Index");
            }
            else
            {
                // Refill the list so that the employee selection is full again
                viewModel.Employees = LoadEmployees();
                viewModel.JobCodes = _jobCodeService.GetAll(LoggedInUser.Account).Select(j => JobCodeMapper.MapFrom(j)).Where(j => j.Enabled).OrderBy(j => j.Name);
            }

            return View(viewModel);
        }

        [HttpPost]
        public async Task<ActionResult> Clone(int id)
        {
            var apiModel =  await _locationApiClient.CloneAsync(id);
            SetSuccessMessageFormat("Location has been cloned successfully.  Update the name of your new location.");
            return RedirectToAction("Edit", new { id = apiModel.Id });
        }

        [HttpPost]
        public async Task<ActionResult> Delete(int id)
        {
            await _locationApiClient.DeleteAsync(id);
            return RedirectToAction("Index");
        }

        [HttpPost]
        public async Task<ActionResult> ActivateAll(string ids, bool activate)
        {
            if (string.IsNullOrWhiteSpace(ids))
            {
                return RedirectToAction("Index");
            }

            var idsArray = ids.Split(new[] { ';' }, StringSplitOptions.RemoveEmptyEntries);
            if (idsArray.Length == 0)
            {
                return RedirectToAction("Index");
            }

            var requests = new List<ActivateDeactivateRequest>();
            foreach (var id in idsArray)
            {
                requests.Add(new ActivateDeactivateRequest { Id = int.Parse(id), IsActive = activate });
            }

            var result = await ActivateDeactivateAll(requests);
            if (result.IsSuccess == false)
            {
                SetErrorMessageFormat(result.Message);
            }
            else if (string.IsNullOrWhiteSpace(result.Message) == false)
            {
                TempData["SuccessMessage"] = result.Message;
            }

            return RedirectToAction("Index");
        }

        private async Task<ActivateDeactivateResult> ActivateDeactivateAll(ICollection<ActivateDeactivateRequest> inputs)
        {
            if (inputs.Count == 0)
            {
                return new ActivateDeactivateResult { IsSuccess = false, Message = "No locations selected." };
            }

            bool isSingleLocation = inputs.Count == 1;

            var locationIds = inputs.Select(x => x.Id);
            var allLocations = await _locationService.GetManyAsync(w=> locationIds.Contains(w.Id));
            var errors = new List<string>();

            foreach (var input in inputs)
            {
                var location = allLocations.FirstOrDefault(w=> w.Id==input.Id) ?? throw new NotFoundException(nameof(Location), input.Id);
                if (_locationService.CanDisable(location.Id, out var disableErrors) == false)
                {
                    errors.AddRange(disableErrors);
                    continue;
                }

                try
                {
                    location.Enabled = input.IsActive;
                    location.UpdatedAt = DateTime.UtcNow;

                    await _locationService.SaveAsync();

                    if (isSingleLocation)
                    {
                        return new ActivateDeactivateResult
                        {
                            IsSuccess = true,
                            RedirectToId = input.Id,
                            Message = location.Enabled ? $"{location.Name} has been activated." : $"{location.Name} has been deactivated."
                        };
                    }
                }
                catch (BuddyPunchWebException ex)
                {
                    return new ActivateDeactivateResult { IsSuccess = false, Message = ex.Message, RedirectToId = isSingleLocation ? input.Id : 0 };
                }
            }
            string message = inputs.First().IsActive ? "Selected locations have been activated." : "Selected locations have been deactivated.";
            return errors.Count == 0 
                ? new ActivateDeactivateResult { IsSuccess = true, Message = message }
                : new ActivateDeactivateResult { IsSuccess = false, Message = string.Join(Environment.NewLine, errors) };
        }

    }
}