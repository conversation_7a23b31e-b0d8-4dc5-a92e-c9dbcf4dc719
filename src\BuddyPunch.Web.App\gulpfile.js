﻿/// <binding ProjectOpened='default' />
var gulp = require('gulp');
var concat = require('gulp-concat');
var rimraf = require('rimraf');
var runSequence = require('gulp4-run-sequence').use(gulp);
var sourcemaps = require('gulp-sourcemaps');
var urlAdjuster = require('gulp-css-url-adjuster');
const sass = require('gulp-sass')(require('node-sass'));
var merge = require('merge2');
var gutil = require('gulp-util');
var gulpif = require('gulp-if');
var minifyCSS = require('gulp-minify-css');
var minifyJS = require('gulp-uglify');
var filesExist = require('files-exist');

var args = Object.assign({ prod: false }, gutil.env);

config = {
  sourcemaps: true,
  minify: false,
};

if (args.prod !== false) {
  config.sourcemaps = false;
  config.minify = true;
}

var sourceDir = 'src';

var PATHS = {
  destination: 'dist',
  javascriptIE8: [
    'src/scriptsIE8/**/*.js',
    'node_modules/blob-polyfill/Blob.js',
  ],
  javascript: [
    'src/theme/vendors/base/vendors.bundle.js',
    'src/theme/demo/demo5/base/scripts.bundle.js',

    /* Datatable export */
    'node_modules/tableexport.jquery.plugin/libs/FileSaver/FileSaver.min.js',
    'node_modules/xlsx/dist/xlsx.core.min.js',
    'node_modules/jspdf/dist/jspdf.min.js',
    'node_modules/jspdf-autotable/dist/jspdf.plugin.autotable.js',
    'node_modules/tableexport.jquery.plugin/tableExport.min.js',
    'node_modules/nouislider/dist/nouislider.min.js',

    /* Other */
    'node_modules/fancybox/dist/js/jquery.fancybox.pack.js',
    'node_modules/bootstrap-datepicker/dist/js/bootstrap-datepicker.min.js',
    'node_modules/jquery-validation/dist/jquery.validate.js',
    'node_modules/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js',

    'src/plugins/countdown/jquery.countdown.js',

    'node_modules/vue/dist/vue.min.js',
  ],
  metronic9Js: [
    /* Metronic 9 JS files - separate bundle */
    'src/metronic9/core.bundle.js',
    'src/metronic9/general.js',
    'src/metronic9/ktui/ktui.min.js',
  ],
  appJavascript: ['src/scripts/**/*.js'],
  javascriptToCopy: [
    'src/theme/vendors/custom/fullcalendar/fullcalendar.bundle.js',
    'src/theme/vendors/custom/datatables/datatables.bundle.js',
    'src/plugins/decoder.min.js',
    'src/plugins/jsqrcode/webqr.js',
    'src/plugins/jsqrcode/llqrcode.js',
    'src/plugins/markerclusterer.js',
    'node_modules/smartbanner.js/dist/smartbanner.min.js',
    'node_modules/v-mask/dist/v-mask.min.js',
    'Scripts/text-mask-addons/createNumberMask.js',
  ],
  css: [
    'src/theme/vendors/base/vendors.bundle.css',
    'src/theme/vendors/custom/fullcalendar/fullcalendar.bundle.css',
    'src/theme/vendors/custom/datatables/datatables.bundle.css',
    'src/theme/vendors/custom/datatables/datatables.custom.css',
    'src/theme/vendors/custom/fonts/fonts.custom.css',
    'src/theme/demo/demo5/base/style.bundle.css',
    'node_modules/fancybox/dist/css/jquery.fancybox.css',
    'src/plugins/countdown/jquery.countdown.css',
    'node_modules/smartbanner.js/dist/smartbanner.min.css',
    'node_modules/nouislider/dist/nouislider.min.css',
  ],
  metronic9Css: [
    'src/metronic9/tailwind.styles.css',
    'src/metronic9/styles.bundle.css',
  ],
  scss: ['src/scss/custom.scss'],
  appCss: [
    'src/css/custom.css',
    'src/css/elementsModal.css',
    'src/css/profile.css',
    'src/css/error.css',
    'src/css/facelogin.css',
    'src/css/qrscanner.css',
  ],
  appScss: ['src/scss/custom.scss'],
  scssWatch: ['src/scss/**/*.scss'],
  fonts: [
    'src/theme/vendors/base/fonts/**/*.{otf,ttf,woff,woff2,eof,svg}',
    'src/metronic9/fonts/**/*.{otf,ttf,woff,woff2,eof,svg}'
  ],
  images: [
    'src/images/**/*.{png,jpg,jpeg,gif,ico}',
    'node_modules/fancybox/dist/img/**/*.{png,jpg, jpeg,gif}',
  ],
};

gulp.task('clean', function (done) {
  rimraf(PATHS.destination, done);
});

gulp.task('pack-js', function () {
  return gulp
    .src(filesExist(PATHS.javascript))
    .pipe(gulpif(config.sourcemaps, sourcemaps.init()))
    .pipe(concat('bundle-20241107.js'))
    .pipe(gulpif(config.minify, minifyJS()))
    .on('error', errorHandler)
    .pipe(gulpif(config.sourcemaps, sourcemaps.write()))
    .pipe(gulp.dest(PATHS.destination))
    .on('error', errorHandler);
});

gulp.task('pack-app-js', function () {
  return gulp
    .src(filesExist(PATHS.appJavascript))
    .pipe(gulpif(config.sourcemaps, sourcemaps.init()))
    .pipe(concat('app-bundle.js'))
    .pipe(gulpif(config.minify, minifyJS()))
    .on('error', errorHandler)
    .pipe(gulpif(config.sourcemaps, sourcemaps.write()))
    .pipe(gulp.dest(PATHS.destination))
    .on('error', errorHandler);
});

gulp.task('debug-pack-js', function () {
  return gulp
    .src(filesExist(PATHS.javascript))
    .pipe(gulpif(config.sourcemaps, sourcemaps.init()))
    .on('error', errorHandler)
    .pipe(minifyJS())
    .on('error', errorHandler)
    .pipe(gulpif(config.sourcemaps, sourcemaps.write()))
    .on('error', errorHandler);
});

gulp.task('debug-pack-app-js', function () {
  return gulp
    .src(filesExist(PATHS.appJavascript))
    .pipe(gulpif(config.sourcemaps, sourcemaps.init()))
    .on('error', errorHandler)
    .pipe(minifyJS())
    .on('error', errorHandler)
    .pipe(gulpif(config.sourcemaps, sourcemaps.write()))
    .on('error', errorHandler);
});

function errorHandler(err) {
  gutil.log(gutil.colors.red('[Error]'), err.toString());
  process.exit(1);
}

gulp.task('pack-js-for-ie8', function () {
  return gulp
    .src(filesExist(PATHS.javascriptIE8))
    .pipe(gulpif(config.sourcemaps, sourcemaps.init()))
    .pipe(concat('bundleIE8.js'))
    .pipe(gulpif(config.minify, minifyJS()))
    .pipe(gulpif(config.sourcemaps, sourcemaps.write()))
    .pipe(gulp.dest(PATHS.destination))
    .on('error', errorHandler);
});

gulp.task('pack-css', function () {
  return merge(
    gulp
      .src(filesExist(PATHS.css))
      .pipe(gulpif(config.sourcemaps, sourcemaps.init()))
  )
    .pipe(
      urlAdjuster({
        replace: ['../img', '/dist/images'],
      })
    )
    .pipe(concat('bundle-20241107.css'))
    .pipe(gulpif(config.minify, minifyCSS()))
    .pipe(gulpif(config.sourcemaps, sourcemaps.write()))
    .pipe(gulp.dest(PATHS.destination))
    .on('error', errorHandler);
});

gulp.task('pack-app-css', function () {
  return merge(
    gulp
      .src(filesExist(PATHS.appCss))
      .pipe(gulpif(config.sourcemaps, sourcemaps.init())),
    gulp
      .src(PATHS.appScss)
      .pipe(sass({ sourcemap: config.sourcemaps }))
      .pipe(gulpif(config.sourcemaps, sourcemaps.write()))
  )
    .pipe(
      urlAdjuster({
        replace: ['../img', '/dist/images'],
      })
    )
    .pipe(concat('app-bundle.css'))
    .pipe(gulpif(config.minify, minifyCSS()))
    .pipe(gulpif(config.sourcemaps, sourcemaps.write()))
    .pipe(gulp.dest(PATHS.destination))
    .on('error', errorHandler);
});

gulp.task('pack-metronic9-js', function () {
  return gulp
    .src(filesExist(PATHS.metronic9Js))
    .pipe(gulpif(config.sourcemaps, sourcemaps.init()))
    .pipe(concat('metronic9-bundle.js'))
    .pipe(gulpif(config.minify, minifyJS()))
    .pipe(gulpif(config.sourcemaps, sourcemaps.write()))
    .pipe(gulp.dest(PATHS.destination))
    .on('error', errorHandler);
});

gulp.task('pack-metronic9-css', function () {
  return gulp
    .src(filesExist(PATHS.metronic9Css))
    .pipe(gulpif(config.sourcemaps, sourcemaps.init()))
    .pipe(concat('metronic9-bundle.css'))
    .pipe(gulpif(config.sourcemaps, sourcemaps.write()))
    .pipe(gulp.dest(PATHS.destination))
    .on('error', errorHandler);
});


gulp.task('copy-fonts', function () {
  return gulp
    .src(PATHS.fonts, { encoding: false, removeBOM: false })
    .pipe(gulp.dest(PATHS.destination + '/fonts'))
    .on('error', errorHandler);
});

gulp.task('copy-images', function () {
  return gulp
    .src(PATHS.images, { encoding: false, removeBOM: false })
    .pipe(gulp.dest(PATHS.destination + '/images'))
    .on('error', errorHandler);
});

gulp.task('copy-js', function () {
  return gulp
    .src(PATHS.javascriptToCopy)
    .pipe(gulp.dest(PATHS.destination + '/additionalScripts'))
    .on('error', errorHandler);
});

gulp.task('watch', function (done) {
  gulp.watch(PATHS.javascript, { interval: 750 }, gulp.series('pack-js'));
  gulp.watch(
    PATHS.appJavascript,
    { interval: 750 },
    gulp.series('pack-app-js')
  );
  gulp.watch(
    PATHS.javascriptIE8,
    { interval: 750 },
    gulp.series('pack-js-for-ie8')
  );
  gulp.watch(PATHS.css, { interval: 750 }, gulp.series('pack-css'));
  gulp.watch(PATHS.appCss, { interval: 750 }, gulp.series('pack-app-css'));
  gulp.watch(
    PATHS.scssWatch,
    { interval: 750 },
    gulp.series('pack-css', 'pack-app-css')
  );
  gulp.watch(PATHS.images, { interval: 750 }, gulp.series('copy-images'));
  gulp.watch(PATHS.fonts, { interval: 750 }, gulp.series('copy-fonts'));
  gulp.watch(PATHS.javascriptToCopy, { interval: 750 }, gulp.series('copy-js'));
  gulp.watch(PATHS.metronic9Js, { interval: 750 }, gulp.series('pack-metronic9-js'));
  gulp.watch(PATHS.metronic9Css, { interval: 750 }, gulp.series('pack-metronic9-css'));

  done();
});

gulp.task('build', function (done) {
  runSequence(
    'clean',
    'pack-js',
    'pack-app-js',
    'pack-js-for-ie8',
    'pack-css',
    'pack-app-css',
    'pack-metronic9-js',
    'copy-fonts',
    'copy-images',
    'pack-metronic9-css',
    'copy-js',
    function (err) {
      if (err) {
        var exitCode = 2;
        console.log('[ERROR] gulp build task failed', err);
        console.log(
          '[FAIL] gulp build task failed - exiting with code ' + exitCode
        );
        return process.exit(exitCode);
      } else {
        return done();
      }
    }
  );
});

gulp.task('default', function (done) {
  runSequence('build', 'watch', function (err) {
    if (err) {
      var exitCode = 2;
      console.log('[ERROR] gulp build task failed', err);
      console.log(
        '[FAIL] gulp build task failed - exiting with code ' + exitCode
      );
      return process.exit(exitCode);
    } else {
      return done();
    }
  });
});
