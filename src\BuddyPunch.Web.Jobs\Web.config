﻿<?xml version="1.0" encoding="utf-8"?>
<!--
  For more information on how to configure your ASP.NET application, please visit
  http://go.microsoft.com/fwlink/?LinkId=152368
  -->
<configuration>
  <configSections>
    <sectionGroup name="intuit">
      <section name="ipp" type="Intuit.Ipp.Utility.IppConfigurationSection, Intuit.Ipp.Utility" allowDefinition="Everywhere" allowLocation="true" />
    </sectionGroup>
    <sectionGroup name="dotNetOpenAuth" type="DotNetOpenAuth.Configuration.DotNetOpenAuthSection, DotNetOpenAuth.Core">
      <section name="messaging" type="DotNetOpenAuth.Configuration.MessagingElement, DotNetOpenAuth.Core" requirePermission="false" allowLocation="true" />
      <section name="reporting" type="DotNetOpenAuth.Configuration.ReportingElement, DotNetOpenAuth.Core" requirePermission="false" allowLocation="true" />
      <section name="openid" type="DotNetOpenAuth.Configuration.OpenIdElement, DotNetOpenAuth.OpenId" requirePermission="false" allowLocation="true" />
    </sectionGroup>
    <section name="RaygunSettings" type="Mindscape.Raygun4Net.RaygunSettings, Mindscape.Raygun4Net" />
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
  </configSections>
  <RaygunSettings apikey="q/Jsl+HrpA5agHGA35aE2Q==" />
  <connectionStrings>
    <add name="MembershipProviderConnectionString" connectionString="Data Source=localhost;Initial Catalog=BuddyPunch;User Id=BuddyPunch2;Password=********;MultipleActiveResultSets=True" providerName="System.Data.SqlClient" />
    <add name="BuddyPunchContext" connectionString="metadata=res://*/BuddyPunch.csdl|res://*/BuddyPunch.ssdl|res://*/BuddyPunch.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=localhost;Initial Catalog=BuddyPunch;User Id=BuddyPunch2;Password=********;multipleactiveresultsets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
  </connectionStrings>
  <appSettings>
    <add key="Environment" value="Dev" />
    <add key="webpages:Version" value="*******" />
    <add key="PreserveLoginUrl" value="true" />
    <add key="ClientValidationEnabled" value="true" />
    <add key="UnobtrusiveJavaScriptEnabled" value="true" />
    <add key="AccessKeyId" value="********************" />
    <add key="SecretAccessKeyId" value="24qkc5qN1sCxfRzXkDJeFpzqp8JbHnvBbEkRumO6" />
    <add key="AzureStorageAccountName" value="buddypunchappqa" />
    <add key="StorageConnectionString" value="DefaultEndpointsProtocol=https;AccountName=buddypunchappqa;AccountKey=****************************************************************************************" />
    <add key="RouteDebugger:Enabled" value="false" />
    <add key="StripeApiKey" value="sk_test_5oASvWoWzBTCQYTg3cSldcI7" />
    <add key="StripeApiPublishableKey" value="pk_test_gJ4DU5RoVewiW7trjI1pYfOU" />
    <add key="RecordUserActivityEnabled" value="false" />
    <add key="NexmoApiKey" value="1610e4bc" />
    <add key="NexmoSecret" value="f85be969" />
    <add key="EnableQrCodeSettings" value="true" />
    <add key="EnableInOutDetailReport" value="true" />
    <add key="NexmoApiKey" value="1610e4bc" />
    <add key="NexmoSecret" value="f85be969" />
    <!-- Constants -->
    <add key="Url_Request_Token" value="/get_request_token" />
    <add key="Url_Access_Token" value="/get_access_token" />
    <add key="Intuit_OAuth_BaseUrl" value="https://oauth.intuit.com/oauth/v1" />
    <add key="Intuit_Workplace_AuthorizeUrl" value="https://workplace.intuit.com/Connect/Begin" />
    <add key="BlueDot_AppMenuUrl" value="https://workplace.intuit.com/api/v1/Account/AppMenu" />
    <add key="DisconnectUrl" value="https://appcenter.intuit.com/api/v1/Connection/Disconnect" />
    <add key="ReconnectUrl" value="https://appcenter.intuit.com/api/v1/Connection/Reconnect" />
    <add key="oauth_callback_url" value="/OauthResponse" />
    <add key="menuProxy" value="MenuProxy" />
    <add key="grantUrl" value="OauthGrant" />
    <add key="qbo_base_url" value="https://qbo.intuit.com/qbo1/rest/user/v2/" />
    <!--<add key="qbo_base_url" value="https://sandbox-quickbooks.api.intuit.com/v3" />-->
    <!-- Enter the Application Name by replacing YourAppName -->
    <add key="openid_identifier" value="https://openid.intuit.com/Identity-Buddy Punch Employee Time Clock" />
    <!-- Enter Application Token Value -->
    <add key="applicationToken" value="b2847fffb9faeb4139bb487b855e0edbdc5c" />
    <!-- Enter Consumer Key Value for the specified application -->
    <add key="consumerKey" value="qyprdkJfRdmP8F5hADII7sHvDoMLyQ" />
    <!-- Enter Consumer Secret Value for the specified application -->
    <add key="consumerSecret" value="5sPgTZRjFH8Zkh2YbTjVCynG0kPyjy9GNPcXNEjt" />
    <!--encryption key.Provide alphanumeric string as your security key for encryption/decryption here-->
    <add key="securityKey" value="tyurptz" />
    <add key="NexmoApiKey" value="1610e4bc" />
    <add key="NexmoSecret" value="f85be969" />
    <add key="EnableQboOpenId" value="true" />
    <add key="GoogleClientId" value="906251104038-ok0fghatgdqpvn7ltprramm2m8oikdij.apps.googleusercontent.com" />
    <add key="GoogleClientSecret" value="X_meXn26RMiv2HaMZorKOBH9" />
    <add key="EventGridTopicEndpoint" value="https://buddypunch-qa-shift.northcentralus-1.eventgrid.azure.net/api/events" />
    <add key="EventGridTopicKey" value="+y2NyCozC6z55kJC8rJ1HbR8CVcRbD4lX9TTjVbBxbA=" />
    <add key="OneSignalAppId" value="************************************" />
    <add key="OneSignalRestApiKey" value="os_v2_app_zz4e2ywcdrhdhbdcuooc23hklsgj7g25pijepimtshumgpouc7k6pzanpbnsld2kco7vs6h4yzv23cr7nf4nxh5ml2i3xkd4xjtsddi" />
    <add key="OneSignalUrl" value="https://onesignal.com/api/v1/notifications" />
    <add key="OvertimeRulesHelper.AllocateHours.RetryAttempts" value="3" />
    <add key="OvertimeRulesHelper.AllocateHours.PauseBetweenFailures" value="2" />
    <add key="LemlistApiKey" value="********************************" />
    <add key="LemlistTrialCampaignId" value="cam_ZS7B4gtNWyYGCvsSe" />
    <add key="LemlistExpiredCampaignId" value="cam_jgqbcQkEQmZkjeTEb" />
    <add key="CheckApiKey" value="36987257b08e6d29256ed6821393baf7bd0e5f01" />
    <add key="CheckApiBase" value="https://sandbox.checkhq.com" />
    <add key="IntercomAppId" value="y9a0fwwv" />
    <add key="IntercomAppKey" value="************************************************************" />
    <add key="IntercomAppSecretKey" value="oN5EHGdG8Sh3zT2Xfzu3Fg0RlsxG6JkBnhUHn-yU" />
    <add key="HubSpotAccessKey" value="--KeyVaultSecret--" />
    <add key="HangfireDashboardPassword" value="<EMAIL>--replace-with-pass" />
    <!--<add key="owin:AutomaticAppStartup" value="false" />-->
  </appSettings>
  <!--
    For a description of web.config changes for .NET 4.5 see http://go.microsoft.com/fwlink/?LinkId=235367.

    The following attributes can be set on the <httpRuntime> tag.
      <system.Web>
        <httpRuntime targetFramework="4.5" />
      </system.Web>
  -->
  <system.web>
    <httpRuntime targetFramework="4.7.2" enableVersionHeader="false" />
    <customErrors mode="RemoteOnly" defaultRedirect="Error/Index" />
    <compilation debug="true" targetFramework="4.7.2">
      <assemblies>
        <add assembly="netstandard, Version=*******, Culture=neutral,              PublicKeyToken=cc7b13ffcd2ddd51" />
      </assemblies>
    </compilation>
    <authentication mode="None" />
    <!--<authorization>
      <deny users="?" />
    </authorization>-->
    <!--<membership defaultProvider="TableMembershipProvider">
      <providers>
        <clear />
        <add name="TableMembershipProvider" type="BuddyPunch.Web.App.Providers.TableMembershipProvider, BuddyPunch.Web.App" connectionStringName="MembershipProviderConnectionString" minRequiredPasswordLength="6" minRequiredNonAlphanumericCharacters="0" passwordStrengthRegularExpression="" applicationName="BuddyPunch" userKeyType="IntNonIdentity" />
      </providers>
    </membership>-->
    <!--<roleManager enabled="true" defaultProvider="TableRowProvider">
      <providers>
        <clear />
        <add name="TableRowProvider" type="Altairis.Web.Security.TableRoleProvider, Altairis.Web.Security" connectionStringName="BuddyPunchContext" />
      </providers>
    </roleManager>-->
    <!--<membership>
      <providers>
        <clear/>
        <add name="AspNetSqlMembershipProvider" type="System.Web.Security.SqlMembershipProvider" connectionStringName="BuddyPunchContext"
             enablePasswordRetrieval="false" enablePasswordReset="true" requiresQuestionAndAnswer="false" requiresUniqueEmail="false"
             maxInvalidPasswordAttempts="5" minRequiredPasswordLength="6" minRequiredNonalphanumericCharacters="0" passwordAttemptWindow="10"
             applicationName="/" />
      </providers>
    </membership>

    <profile>
      <providers>
        <clear/>
        <add name="AspNetSqlProfileProvider" type="System.Web.Profile.SqlProfileProvider" connectionStringName="BuddyPunchContext" applicationName="/" />
      </providers>
    </profile>

    <roleManager enabled="false">
      <providers>
        <clear/>
        <add name="AspNetSqlRoleProvider" type="System.Web.Security.SqlRoleProvider" connectionStringName="ApplicationServices" applicationName="/" />
        <add name="AspNetWindowsTokenRoleProvider" type="System.Web.Security.WindowsTokenRoleProvider" applicationName="/" />
      </providers>
    </roleManager>-->
    <pages controlRenderingCompatibilityVersion="4.0">
      <namespaces>
        <add namespace="System.Web.Helpers" />
        <add namespace="System.Web.Mvc" />
        <add namespace="System.Web.Mvc.Ajax" />
        <add namespace="System.Web.Mvc.Html" />
        <add namespace="System.Web.Routing" />
        <add namespace="System.Web.WebPages" />
        <!--<add namespace="MvcPaging" />-->
      </namespaces>
    </pages>
    <httpModules>
      <add name="RaygunErrorModule" type="Mindscape.Raygun4Net.RaygunHttpModule" />
    </httpModules>
  </system.web>
  <system.webServer>
    <staticContent>
      <remove fileExtension=".svg" />
      <remove fileExtension=".eot" />
      <remove fileExtension=".woff" />
      <mimeMap fileExtension=".svg" mimeType="image/svg+xml" />
      <mimeMap fileExtension=".eot" mimeType="application/vnd.ms-fontobject" />
      <mimeMap fileExtension=".woff" mimeType="application/font-woff" />
    </staticContent>
    <validation validateIntegratedModeConfiguration="false" />
    <modules runAllManagedModulesForAllRequests="true">
      <add name="RaygunErrorModule" type="Mindscape.Raygun4Net.RaygunHttpModule" />
    </modules>
    <rewrite>
      <rules>
        <rule name="fingerprint">
          <match url="([\S]+)(/v-[0-9]+/)([\S]+)" />
          <action type="Rewrite" url="{R:1}/{R:3}" />
        </rule>
      </rules>
      <outboundRules rewriteBeforeCache="true">
        <rule name="Remove X-Powered-By HTTP response header">
          <match serverVariable="RESPONSE_X-Powered-By" pattern=".+" />
          <action type="Rewrite" value="" />
        </rule>
      </outboundRules>
    </rewrite>
    <security>
      <requestFiltering removeServerHeader="true" />
    </security>
    <httpProtocol>
      <customHeaders>
        <remove name="X-Powered-By" />
        <add name="Arr-Disable-Session-Affinity" value="True" />
      </customHeaders>
    </httpProtocol>
  </system.webServer>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="EntityFramework" publicKeyToken="b77a5c561934e089" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Practices.ServiceLocation" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1.3.0.0" newVersion="1.3.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.2.0" newVersion="4.2.2.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security.OAuth" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.0.1.0" newVersion="3.0.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.0.1.0" newVersion="3.0.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security.Cookies" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.0.1.0" newVersion="3.0.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1*******" newVersion="1*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1.5.2.14234" newVersion="1.5.2.14234" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Http" publicKeyToken="31BF3856AD364E35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.3.0" newVersion="5.2.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Http.Formatting" publicKeyToken="31BF3856AD364E35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.3.0" newVersion="5.2.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Http" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.7.0" newVersion="5.2.7.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Http.Formatting" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.7.0" newVersion="5.2.7.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Data.OData" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.8.4.0" newVersion="5.8.4.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Spatial" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.0.2.0" newVersion="5.0.2.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.DependencyInjection.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.1.1.0" newVersion="2.1.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Options" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.1.1.0" newVersion="2.1.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Primitives" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="RestSharp" publicKeyToken="598062e77f915f75" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-106.15.0.0" newVersion="106.15.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.ValueTuple" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Threading.Tasks.Extensions" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.0.1" newVersion="4.2.0.1" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Security.Cryptography.ProtectedData" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.5.0" newVersion="4.0.5.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Security.AccessControl" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Text.Encodings.Web" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Text.Json" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.1.2" newVersion="4.0.1.2" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Diagnostics.DiagnosticSource" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.1" newVersion="6.0.0.1" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Azure.Core" publicKeyToken="92742159e12e44c8" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1.42.0.0" newVersion="1.42.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Bcl.AsyncInterfaces" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-8.0.0.0" newVersion="8.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Identity.Client" publicKeyToken="0a613f4dd989e8ae" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.61.3.0" newVersion="4.61.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Azure.Identity" publicKeyToken="92742159e12e44c8" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1.12.0.0" newVersion="1.12.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-5.3.0.0" newVersion="5.3.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Unity.Abstractions" publicKeyToken="489b6accfaf20ef0" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
    </assemblyBinding>
    <!-- This prevents the Windows Event Log from frequently logging that HMAC1 is being used (when the other party needs it). -->
    <legacyHMACWarning enabled="0" />
  </runtime>
  <system.net>
    <mailSettings>
      <!-- Method#2: Dump emails to a local directory -->
      <smtp from="<EMAIL>" deliveryMethod="SpecifiedPickupDirectory">
        <network host="localhost" />
        <specifiedPickupDirectory pickupDirectoryLocation="c:\temp\" />
      </smtp>
    </mailSettings>
    <defaultProxy enabled="true" />
    <settings>
      <!-- This setting causes .NET to check certificate revocation lists (CRL) 
           before trusting HTTPS certificates.  But this setting tends to not 
           be allowed in shared hosting environments. -->
      <!--<servicePointManager checkCertificateRevocationList="true"/>-->
    </settings>
  </system.net>
  <entityFramework>
    <defaultConnectionFactory type="System.Data.Entity.Infrastructure.SqlConnectionFactory, EntityFramework">
      <parameters>
        <parameter value="Data Source=.\SQLEXPRESS; Integrated Security=True; MultipleActiveResultSets=True" />
      </parameters>
    </defaultConnectionFactory>
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
    </providers>
  </entityFramework>
  <dotNetOpenAuth>
    <messaging>
      <untrustedWebRequest>
        <whitelistHosts>
          <!-- Uncomment to enable communication with localhost (should generally not activate in production!) -->
          <!--<add name="localhost" />-->
        </whitelistHosts>
      </untrustedWebRequest>
    </messaging>
    <!-- Allow DotNetOpenAuth to publish usage statistics to library authors to improve the library. -->
    <reporting enabled="true" />
    <!-- This is an optional configuration section where aspects of dotnetopenauth can be customized. -->
    <!-- For a complete set of configuration options see http://www.dotnetopenauth.net/developers/code-snippets/configuration-options/ -->
    <openid>
      <relyingParty>
        <security requireSsl="false">
          <!-- Uncomment the trustedProviders tag if your relying party should only accept positive assertions from a closed set of OpenID Providers. -->
          <!--<trustedProviders rejectAssertionsFromUntrustedProviders="true">
            <add endpoint="https://www.google.com/accounts/o8/ud" />
          </trustedProviders>-->
        </security>
        <behaviors>
          <!-- The following OPTIONAL behavior allows RPs to use SREG only, but be compatible
               with OPs that use Attribute Exchange (in various formats). -->
          <add type="DotNetOpenAuth.OpenId.RelyingParty.Behaviors.AXFetchAsSregTransform, DotNetOpenAuth.OpenId.RelyingParty" />
        </behaviors>
      </relyingParty>
    </openid>
  </dotNetOpenAuth>
  <uri>
    <!-- The uri section is necessary to turn on .NET 3.5 support for IDN (international domain names),
         which is necessary for OpenID urls with unicode characters in the domain/host name.
         It is also required to put the Uri class into RFC 3986 escaping mode, which OpenID and OAuth require. -->
    <idn enabled="All" />
    <iriParsing enabled="true" />
  </uri>
  <system.diagnostics>
    <switches>
      <add name="IPPTraceSwitch" value="4" />
    </switches>
    <trace autoflush="true" indentsize="2">
      <listeners>
        <add name="IdsTracer" type="System.Diagnostics.TextWriterTraceListener,              System,              Version=4.0.30319.1,              Culture=neutral,              PublicKeyToken=b77a5c561934e089" initializeData="C:\\Repository\\BuddyPunch\\SDKTrace.log" />
      </listeners>
    </trace>
  </system.diagnostics>
  <intuit>
    <ipp>
      <logger>
        <!-- To enable/disable Request and Response log-->
        <requestLog enableRequestResponseLogging="true" />
      </logger>
      <service>
        <!-- Baseurl will be configured to point to any environment other than Production (default)-->
        <baseUrl qbo="https://sandbox-quickbooks.api.intuit.com/" />
      </service>
    </ipp>
  </intuit>
  <system.codedom>
    <compilers>
      <compiler language="c#;cs;csharp" extension=".cs" warningLevel="4" compilerOptions="/langversion:default /nowarn:1659;1699;1701;612;618" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.CSharpCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
      <compiler language="vb;vbs;visualbasic;vbscript" extension=".vb" warningLevel="4" compilerOptions="/langversion:default /nowarn:41008,40000,40008 /define:_MYTYPE=\&quot;Web\&quot; /optionInfer+" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.VBCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
    </compilers>
  </system.codedom>
</configuration>