﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using BuddyPunch.Service;
using BuddyPunch.Web.App.Mappers;
using BuddyPunch.Web.App.ViewModel;
using BuddyPunch.Web.App.Helpers;
using BuddyPunch.Model.Enums;
using BuddyPunch.Service.Helpers.HubSpot;
using System.Threading.Tasks;
using BuddyPunch.Api.Models.Api;
using BuddyPunch.Model.Interfaces;
using BuddyPunch.Web.App.Models;
using BuddyPunch.Web.App.Exceptions;

namespace BuddyPunch.Web.App.Controllers
{
    [AuthorizeRole(Role = RoleEnum.Administrator)]
    public class PositionController : AuthenticatedController
    {
        private readonly IPositionService _positionService;
        private readonly IStaffService _staffService;
        private readonly IPositionApi _positionApiClient;
        private readonly IConfigurationProvider _configurationProvider;

        public PositionController(IPositionService positionService, 
            ISecurityService securityService, 
            IStaffService staffService, 
            IPositionApi positionApiClient,
            IConfigurationProvider configurationProvider)
            : base(securityService)
        {
            _positionService = positionService;
            _staffService = staffService;
            _positionApiClient = positionApiClient;
            _configurationProvider = configurationProvider;
        }

        //
        // GET: /Position/

        public ActionResult Index()
        {
            var viewModel = new PositionListViewModel();
            viewModel.Positions = _positionService.GetAll(LoggedInUser.Account).Select(j => PositionMapper.MapFrom(j, true));
            return View(viewModel);
        }


        // GET: /Position/Create

        public ActionResult Create()
        {
            var viewModel = new PositionViewModel();
            viewModel.Enabled = true;
            viewModel.PositionStaffIds = new List<int>();
            viewModel.Employees = _staffService.GetAll(LoggedInUser)
                .Select(EmployeeMapper.MapFrom)
                .Where(c => c.IsActive)
                .OrderBy(c => c.FullName)
                .ToList();
            return View(viewModel);
        }

        //
        // POST: /Position/Create

        [HttpPost]
        public ActionResult Create(PositionViewModel viewModel)
        {
            if (ModelState.IsValid)
            {
                var position = new Model.Position();
                position.Name = viewModel.Name;
                position.Enabled = viewModel.Enabled;
                position.Account = LoggedInUser.Account;
                position.AccountId = LoggedInUser.AccountId;
                position.CreatedAt = DateTime.UtcNow;
                position.UpdatedAt = DateTime.UtcNow;


                var staffMembers = _staffService.GetAll(LoggedInUser).ToList();
                foreach (var staffId in viewModel.PositionStaffIds)
                {
                    position.AddStaff(staffMembers.Single(j => j.Id == staffId));
                }
                _positionService.Create(position);

                HubSpotHelper.TrackContactEvent(LoggedInUser, _staffService, new HubSpotLegacyEventModel()
                {
                    EventId = HubSpotLegacyEventModel.PositionAdded
                }, _configurationProvider.IsProduction);
                HubSpotHelper.TrackContactEvent(LoggedInUser, _staffService, new HubSpotEventModel()
                {
                    EventName = HubSpotEventModel.PositionAdded
                }, _configurationProvider.IsProduction);

                return RedirectToAction("Index");
            }
            else
            {
                // Refill the list so that the employee selection is full again
                viewModel.Employees = _staffService.GetAll(LoggedInUser)
                                          .Select(EmployeeMapper.MapFrom)
                                          .Where(c => c.IsActive)
                                          .OrderBy(c => c.FullName)
                                          .ToList();
            }

            return View(viewModel);
        }

        //
        // GET: /Position/Edit/5

        public ActionResult Edit(int id = 0)
        {
            var position = _positionService.GetById(LoggedInUser.Account, id);
            if (position == null)
            {
                return HttpNotFound();
            }

            var viewModel = PositionMapper.MapFrom(position);

            viewModel.PositionStaffIds = position.StaffPositions.Select(j => j.StaffId);
            viewModel.PositionShiftsStaffIds = position.StaffPositions.Where(x => x.Staff.GetPositionsFromCurrentAndFutureShifts()
                .Contains(position)).Select(j => j.StaffId);

            viewModel.Employees = _staffService.GetAll(LoggedInUser)
                .Select(EmployeeMapper.MapFrom)
                .Where(c => c.IsActive)
                .OrderBy(c => c.FullName)
                .ToList();

            var canDisable = _positionService.CanDisable(position.Id, out var disableErrors);
            viewModel.CanDisable = canDisable;
            viewModel.CanDelete = canDisable && _positionService.CanDelete(position.Id);
            viewModel.DisableErrors = disableErrors;

            return View(viewModel);
        }

        //
        // POST: /Position/Edit/5

        [HttpPost]
        public ActionResult Edit(PositionViewModel viewModel)
        {
            var position = _positionService.GetById(LoggedInUser.Account, viewModel.Id);
            if (position == null)
            {
                return HttpNotFound();
            }

            if (ModelState.IsValid)
            {
                position.Name = viewModel.Name;
                position.Enabled = viewModel.Enabled;
                position.UpdatedAt = DateTime.UtcNow;

                var staffMembers = _staffService.GetAll(LoggedInUser).ToList();

                //remove active employees
                foreach (var staff in staffMembers.Where(s => s.IsActive && !s.GetPositionsFromCurrentAndFutureShifts()
                    .Contains(position)))
                {
                    var staffToRemove = position.StaffPositions.FirstOrDefault(p => p.StaffId == staff.Id);

                    if (staffToRemove != null)
                        position.StaffPositions.Remove(staffToRemove);
                }

                //re-add any active employees
                foreach (var staffId in viewModel.PositionStaffIds)
                {
                    position.AddStaff(staffMembers.Single(j => j.Id == staffId));
                }

                _positionService.Save();
                return RedirectToAction("Index");
            }
            else
            {
                // Refill the list so that the employee selection is full again
                viewModel.Employees = _staffService.GetAll(LoggedInUser)
                    .Select(EmployeeMapper.MapFrom)
                    .Where(c => c.IsActive)
                    .OrderBy(c => c.FullName)
                    .ToList();
            }

            return View(viewModel);
        }

        [HttpPost]
        public async Task<ActionResult> Clone(int id)
        {
            var apiModel = await _positionApiClient.CloneAsync(id);
            SetSuccessMessageFormat("Position has been cloned successfully.  Update the name of your new position.");
            return RedirectToAction("Edit", new { id = apiModel.Id });
        }

        [HttpPost]
        public async Task<ActionResult> Delete(int id)
        {
            await _positionApiClient.DeleteAsync(id);
            return RedirectToAction("Index");
        }

        [HttpPost]
        public async Task<ActionResult> ActivateAll(string ids, bool activate)
        {
            if (string.IsNullOrWhiteSpace(ids))
            {
                return RedirectToAction("Index");
            }

            var idsArray = ids.Split(new[] { ';' }, StringSplitOptions.RemoveEmptyEntries);
            if (idsArray.Length == 0)
            {
                return RedirectToAction("Index");
            }

            var requests = new List<ActivateDeactivateRequest>();
            foreach (var id in idsArray)
            {
                requests.Add(new ActivateDeactivateRequest { Id = int.Parse(id), IsActive = activate });
            }

            var result = await ActivateDeactivateAll(requests);
            if (result.IsSuccess == false)
            {
                SetErrorMessageFormat(result.Message);
            }
            else if (string.IsNullOrWhiteSpace(result.Message) == false)
            {
                TempData["SuccessMessage"] = result.Message;
            }

            return RedirectToAction("Index");
        }

        private async Task<ActivateDeactivateResult> ActivateDeactivateAll(ICollection<ActivateDeactivateRequest> inputs)
        {
            if (inputs.Count == 0)
            {
                return new ActivateDeactivateResult { IsSuccess = false, Message = "No locations selected." };
            }

            bool isSingleLocation = inputs.Count == 1;

            var locationIds = inputs.Select(x => x.Id);
            var allLocations = await _positionService.GetManyAsync(w => locationIds.Contains(w.Id));
            var errors = new List<string>();

            foreach (var input in inputs)
            {
                var location = allLocations.FirstOrDefault(w => w.Id == input.Id) ?? throw new NotFoundException("Department Code", input.Id);
                if (_positionService.CanDisable(location.Id, out var disableErrors) == false)
                {
                    errors.AddRange(disableErrors);
                    continue;
                }

                try
                {
                    location.Enabled = input.IsActive;
                    location.UpdatedAt = DateTime.UtcNow;

                    await _positionService.SaveAsync();

                    if (isSingleLocation)
                    {
                        return new ActivateDeactivateResult
                        {
                            IsSuccess = true,
                            RedirectToId = input.Id,
                            Message = location.Enabled ? $"{location.Name} has been activated." : $"{location.Name} has been deactivated."
                        };
                    }
                }
                catch (BuddyPunchWebException ex)
                {
                    return new ActivateDeactivateResult { IsSuccess = false, Message = ex.Message, RedirectToId = isSingleLocation ? input.Id : 0 };
                }
            }
            string message = inputs.First().IsActive ? "Selected locations have been activated." : "Selected locations have been deactivated.";
            return errors.Count == 0
                ? new ActivateDeactivateResult { IsSuccess = true, Message = message }
                : new ActivateDeactivateResult { IsSuccess = false, Message = string.Join(Environment.NewLine, errors) };
        }

    }
}