﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Net.Mail;
using System.Threading.Tasks;
using System.Web.Mvc;
using BuddyPunch.Model;
using BuddyPunch.Model.Enums;
using BuddyPunch.Model.Interfaces;
using BuddyPunch.Service;
using BuddyPunch.Service.Interfaces;
using BuddyPunch.Web.App.Helpers;
using BuddyPunch.Web.App.Managers;
using BuddyPunch.Web.App.Mappers;
using BuddyPunch.Web.App.ViewModel;
using Mindscape.Raygun4Net;
using Stripe;

namespace BuddyPunch.Web.App.Controllers
{
    public class HomeController : BaseController
    {
        private User _user;
        readonly ISecurityService _securityService;
        private readonly ITimesheetService _timesheetService;
        private readonly IPunchService _punchService;
        private readonly IStaffService _staffService;
        private readonly IPtoRequestService _ptoRequestService;
        private readonly IShiftService _shiftService;
        private readonly IPayPeriodService _payperiodService;

        public HomeController(ISecurityService securityService, ITimesheetService timesheetService, IPunchService punchService, IStaffService staffService, 
            IPtoRequestService ptoRequestService, IShiftService shiftService, IPayPeriodService payperiodService)
        {
            _securityService = securityService;
            _timesheetService = timesheetService;
            _punchService = punchService;
            _staffService = staffService;
            _ptoRequestService = ptoRequestService;
            _shiftService = shiftService;
            _payperiodService = payperiodService;
        }

        protected User LoggedInUser
        {
            get
            {
                if (_user == null)
                {
                    _user = _securityService.GetUser(UserId, readOnly: true);
                }
                return _user;
            }
        }
        public UserViewModel LoggedInUserViewModel
        {
            get
            {
                if (SessionManager.Profile.LoggedInUserViewModel == null ||
                    SessionManager.Profile.LoggedInUserViewModel.Account == null)
                {
                    SessionManager.Profile.LoggedInUserViewModel = UserMapper.MapFrom(LoggedInUser);
                }

                return SessionManager.Profile.LoggedInUserViewModel;
            }
        }

        public async Task<ActionResult> Index()
        {
            var viewModel = new DashboardViewModel();
            var user = _securityService.GetUser(UserId, new Expression<Func<User, object>>[]
            {
                u => u.Person.Account.AccountStripeCustomer,
                u => u.Person.Account.TimesheetApprovalSetting,
                u => u.Person.Account.PaymentPlan,
                u => u.Person.Account.PayPeriodSchedule,
                u => u.Person.Account.GpsSetting,
                u => u.Person.Account.ScheduleSetting,
                u => u.Person.Account.Check_Company,
                u => u.Person.Account.Features,
                u => u.Person.Account.AddonPlans.Select(a => a.AddonFeature),
                u => u.Roles
            }, readOnly: true);
            viewModel.User = LoggedInUserViewModel;
            var account = user.Account;

            if (user.IsConfigurator && SessionManager.Profile.LoggedInUserViewModel.Account.PayrollMigrationOn)
            {
                viewModel.InfoMessageHeader = "Payroll Data Migration is On";
                viewModel.InfoMessage = string.Format(
                        "During payroll data migration only configurators can access payroll.");
                viewModel.InfoMessageNavigationUrl = Url.Action("Company", "Payroll");
            }

            TempData["HasConsent"] = user.Person.ConsentDateTime != null;

            if (user.IsAccountOwner)
            {
                if (account.AccountStripeCustomer != null)
                {
                    viewModel.SubscriptionStatus = account.AccountStripeCustomer.SubscriptionStatus;
                    try
                    {
                        var stripeCustomerService = new CustomerService();
                        var stripeCustomer = stripeCustomerService.Get(account.AccountStripeCustomer.StripeCustomerId);
                        
                        if (stripeCustomer.Delinquent == true)
                        {
                            viewModel.ErrorMessageHeader = "Invoice Due";
                            viewModel.ErrorMessage =
                                "We could not process your payment.  Click here to update your billing information to keep your account active.";
                            viewModel.ErrorMessageNavigationUrl = Url.Action("ViewPlan", "Account");
                        }                         
                    }
                    catch 
                    {
                        
                            //ignore
                                              
                    }

                    if (string.IsNullOrEmpty(viewModel.ErrorMessageHeader))
                    {
                        if (account.AccountStripeCustomer.PlanName != null &&
                            !account.AccountStripeCustomer.PlanName.ToLower().Contains("free"))
                        {
                            if (account.AccountStripeCustomer.SubscriptionStatus != null &&
                                !account.AccountStripeCustomer.SubscriptionStatus.Equals("trialing",
                                    StringComparison.CurrentCultureIgnoreCase))
                            {
                                if (account.PaymentPlan.MaxUsers > 0)
                                {
                                    int activeEmployeeCount;
                                    if (SessionManager.Profile.FooterViewModel != null)
                                    {
                                        activeEmployeeCount = SessionManager.Profile.FooterViewModel.ActiveEmployeeCount;
                                        if (activeEmployeeCount > account.PaymentPlan.MaxUsers)
                                        {
                                            // check if they are still over the account limit
                                            activeEmployeeCount = _staffService.GetAll(user.Id, true).Count();
                                            SessionManager.Profile.FooterViewModel.ActiveEmployeeCount =
                                                activeEmployeeCount;
                                        }
                                    }
                                    else
                                    {
                                        activeEmployeeCount = _staffService.GetAll(user.Id, true).Count();
                                    }

                                    if (activeEmployeeCount > account.PaymentPlan.MaxUsers)
                                    {
                                        var overLimit = activeEmployeeCount - account.PaymentPlan.MaxUsers;

                                        viewModel.ErrorMessageHeader = "Over your employee limit";
                                        if (overLimit == 1)
                                        {
                                            viewModel.ErrorMessage =
                                                "You are over your plan limit by 1 employee.  Please make 1 employee inactive or click here to upgrade your plan.";
                                        }
                                        else
                                        {
                                            viewModel.ErrorMessage = string.Format(
                                                "You are over your plan limit by {0} employees.  Please make {0} employees inactive or click here to upgrade your plan.",
                                                overLimit);
                                        }
                                        viewModel.ErrorMessageNavigationUrl = Url.Action("ViewPlan", "Account");

                                        try
                                        {
                                            var mailMessage = new MailMessage();
                                            mailMessage.To.Add("<EMAIL>");
                                            mailMessage.Subject =
                                                $"{account.Name}(Id: {account.Id}) is {overLimit} employees over the limit. {activeEmployeeCount} employees with a limit of {account.PaymentPlan.MaxUsers}";
                                            mailMessage.Body = $"{LoggedInUser.UserName} logged in";

                                            //client.Send(mailMessage);
                                            MailHelper.Client.SendEmail(mailMessage);
                                        }
                                        catch (Exception)
                                        {
                                            //swallow
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            if (user.IsAdministrator && LoggedInUserViewModel.Account.PayrollEnabled)
            {
                CheckPayrollSubmitDue(viewModel, account);
            }

            if (user.IsAccountOwner || user.IsTimesheetManager)
            {
                var timeSheets = _timesheetService.GetPunchesPendingApproval(user);
                viewModel.TimesheetsPendingApproval = timeSheets.Select(TimesheetMapper.MapWithPendingCount).ToList();

                var submittedTimesheets = _timesheetService.GetSubmittedTimesheetsPendingApproval(user);
                viewModel.SubmittedTimesheetsPendingApproval = submittedTimesheets.Select(t => TimesheetMapper.MapFrom(t, t.PayPeriod, t.Staff));

                viewModel.PTORequestsPendingApproval = GetPTOPendingApproval(user);

                if (SessionManager.Profile.IsSchedulingEnabled)
                {
                    var staff = _staffService.GetById(user, user.Id, GetStaffIncludes(account));

                    viewModel.CurrentShifts = await GetCurrentShifts(staff);
                    var scheduleTimeZoneName = TimeZoneHelper.GetTimeZoneInfo(staff).DisplayName;
                    var scheduleTimeZoneShortName = scheduleTimeZoneName.Substring(scheduleTimeZoneName.IndexOf(")", StringComparison.Ordinal)+1).Trim();
                    viewModel.ScheduleTimeZone = scheduleTimeZoneShortName;
                }
                
                var currentlyPunchedIn = new List<StaffCurrentActivity>();
                var currentlyPunchedOut = new List<StaffCurrentActivity>();                               

                var activity = _punchService.CurrentActivity(user, includeTimeSheetManager:true);
                foreach (var employeeCurrentActivity in activity)
                {
                    var accountScheduleSetting = LoggedInUser.Account.ScheduleSetting ?? new ScheduleSetting()
                    {
                        MinutesAfterShiftEndsThreshold = 0,
                        MinutesAfterShiftStartsThreshold = 0,
                        MinutesBeforeShiftEndsThreshold = 0,
                        MinutesBeforeShiftStartsThreshold = 0
                    };
                    if (employeeCurrentActivity.Status == PunchedInOutStatus.PunchedIn || employeeCurrentActivity.Status == PunchedInOutStatus.OnBreak)
                    {
                        // if on unpaid break, we use the time break started to calculate the current shift time
                        var timeUsedToCalculateShiftTime =
                            (employeeCurrentActivity.Status == PunchedInOutStatus.OnBreak && employeeCurrentActivity.Break.IsUnpaid && employeeCurrentActivity.Break.StartDateTimeUtc.HasValue)
                            ? employeeCurrentActivity.Break.StartDateTimeUtc.Value
                            : DateTime.UtcNow;
                        var staffCurrentActivity = new StaffCurrentActivity
                        {
                            Staff = employeeCurrentActivity.Staff,
                            LastPunchDate =
                                employeeCurrentActivity.LastPunchDateUtc.HasValue
                                    ? (DateTime?) TimeZoneInfo.ConvertTime(
                                        DateTime.SpecifyKind(employeeCurrentActivity.LastPunchDateUtc.Value,
                                            DateTimeKind.Utc),
                                        employeeCurrentActivity.TimeZone)
                                    : null,
                            TimeSpanInShift = employeeCurrentActivity.LastPunchDateUtc.HasValue
                                ? timeUsedToCalculateShiftTime - employeeCurrentActivity.LastPunchDateUtc.Value - employeeCurrentActivity.UnpaidBreakDuration.GetValueOrDefault()
                                : new TimeSpan(),
                            JobCodeName = employeeCurrentActivity.JobCodeName,
                            LocationName = employeeCurrentActivity.LocationName,
                            PositionName = employeeCurrentActivity.PositionName,
                            TimeSheetId = employeeCurrentActivity.TimeSheetId,
                            PunchInImageUrl = employeeCurrentActivity.PunchInImageUrl,
                            Latitude = employeeCurrentActivity.Latitude,
                            Longitude = employeeCurrentActivity.Longitude,
                            Accuracy = employeeCurrentActivity.Accuracy,
                            LastLocationDate = employeeCurrentActivity.LastLocationDate.HasValue ? (DateTime?)TimeZoneInfo.ConvertTime(
                                DateTime.SpecifyKind(employeeCurrentActivity.LastLocationDate.Value,
                                    DateTimeKind.Utc),
                                employeeCurrentActivity.TimeZone) : null,
                            TimeSheetPunchId = employeeCurrentActivity.LastTimeSheetPunchId,
                            ShowMultiplePunchGeolocationsMap = employeeCurrentActivity.PunchHasMultipleGeolocations && account.IsAlwaysOnGPSFeatureOn,
                            LastPunchNotes = employeeCurrentActivity.LastPunchNotes,
                            LastPunchShiftStartDateTime = employeeCurrentActivity.LastPunchShiftStartDateTimeUtc.HasValue ? (DateTime?)TimeZoneInfo.ConvertTime(
                                DateTime.SpecifyKind(employeeCurrentActivity.LastPunchShiftStartDateTimeUtc.Value,
                                    DateTimeKind.Utc),
                                employeeCurrentActivity.TimeZone) : null,
                            LastPunchShiftEndDateTime = employeeCurrentActivity.LastPunchShiftEndDateTimeUtc.HasValue ? (DateTime?)TimeZoneInfo.ConvertTime(
                                DateTime.SpecifyKind(employeeCurrentActivity.LastPunchShiftEndDateTimeUtc.Value,
                                    DateTimeKind.Utc),
                                employeeCurrentActivity.TimeZone) : null,
                            ScheduledShiftDuration = employeeCurrentActivity.ScheduledShiftDuration,
                            LastPunchShiftStartedDateTime = employeeCurrentActivity.LastPunchShiftStartedDateTimeUtc.HasValue ? (DateTime?)TimeZoneInfo.ConvertTime(
                                DateTime.SpecifyKind(employeeCurrentActivity.LastPunchShiftStartedDateTimeUtc.Value,
                                    DateTimeKind.Utc),
                                employeeCurrentActivity.TimeZone) : null,
                            LastPunchShiftEndedDateTime = employeeCurrentActivity.LastPunchShiftEndedDateTimeUtc.HasValue ? (DateTime?)TimeZoneInfo.ConvertTime(
                                DateTime.SpecifyKind(employeeCurrentActivity.LastPunchShiftEndedDateTimeUtc.Value,
                                    DateTimeKind.Utc),
                                employeeCurrentActivity.TimeZone) : null,
                            AccountPTOEarningCodeName = employeeCurrentActivity.AccountPTOEarningCodeName
                        };

                        if(staffCurrentActivity.LastLocationDate.HasValue)
                        {
                            DateTime localNow = TimeZoneInfo.ConvertTime(DateTime.UtcNow, employeeCurrentActivity.TimeZone);
                            staffCurrentActivity.LastLocationDateFromNow = localNow - staffCurrentActivity.LastLocationDate.Value;
                        }

                        if (staffCurrentActivity.LastPunchShiftStartDateTime.HasValue)
                        {
                            staffCurrentActivity.LastPunchShiftStatusBadge =
                                TimeSheetPunchShiftViewModel.GetEarlyLateStatusGhostly(
                                    staffCurrentActivity.LastPunchShiftStartDateTime.Value,
                                    staffCurrentActivity.LastPunchShiftStartedDateTime,
                                    accountScheduleSetting.MinutesBeforeShiftStartsThreshold,
                                    accountScheduleSetting.MinutesAfterShiftStartsThreshold);
                        }

                        if (employeeCurrentActivity.Status == PunchedInOutStatus.OnBreak)
                        {
                            staffCurrentActivity.OnBreak = true;
                            staffCurrentActivity.Break = new StaffCurrentBreak()
                            {
                                Id = employeeCurrentActivity.Break.Id,
                                Name = employeeCurrentActivity.Break.Name,
                                IsUnpaid = employeeCurrentActivity.Break.IsUnpaid,
                                StartDateTime = employeeCurrentActivity.Break.StartDateTimeUtc.HasValue
                                    ? (DateTime?)TimeZoneInfo.ConvertTime(
                                        DateTime.SpecifyKind(employeeCurrentActivity.Break.StartDateTimeUtc.Value, DateTimeKind.Utc),
                                        employeeCurrentActivity.TimeZone)
                                    : null,
                                Duration = employeeCurrentActivity.Break.StartDateTimeUtc.HasValue
                                    ? DateTime.UtcNow - employeeCurrentActivity.Break.StartDateTimeUtc.Value
                                    : new TimeSpan(),
                                StartHasGeo = employeeCurrentActivity.Break.StartHasGeo,
                                StartImageUrl = employeeCurrentActivity.Break.StartImageUrl,
                                Notes = employeeCurrentActivity.Break.Notes
                            };
                        }

                        currentlyPunchedIn.Add(staffCurrentActivity);
                    }
                    else
                    {
                        var staffCurrentActivity = new StaffCurrentActivity
                        {
                            Staff = employeeCurrentActivity.Staff,
                            LastPunchDate =
                                employeeCurrentActivity.LastPunchDateUtc.HasValue
                                    ? (DateTime?) TimeZoneInfo.ConvertTime(
                                        DateTime.SpecifyKind(employeeCurrentActivity.LastPunchDateUtc.Value,
                                            DateTimeKind.Utc),
                                        employeeCurrentActivity.TimeZone)
                                    : null,
                            JobCodeName = employeeCurrentActivity.JobCodeName,
                            LocationName = employeeCurrentActivity.LocationName,
                            PositionName = employeeCurrentActivity.PositionName,
                            TimeSheetId = employeeCurrentActivity.TimeSheetId,
                            PunchOutImageUrl = employeeCurrentActivity.PunchOutImageUrl,
                            Latitude = employeeCurrentActivity.Latitude,
                            Longitude = employeeCurrentActivity.Longitude,
                            Accuracy = employeeCurrentActivity.Accuracy,
                            LastLocationDate = employeeCurrentActivity.LastLocationDate.HasValue ? (DateTime?)TimeZoneInfo.ConvertTime(
                                DateTime.SpecifyKind(employeeCurrentActivity.LastLocationDate.Value,
                                    DateTimeKind.Utc),
                                employeeCurrentActivity.TimeZone) : null,
                            TimeSheetPunchId = employeeCurrentActivity.LastTimeSheetPunchId,
                            ShowMultiplePunchGeolocationsMap = employeeCurrentActivity.PunchHasMultipleGeolocations && account.IsAlwaysOnGPSFeatureOn,
                            LastPunchNotes = employeeCurrentActivity.LastPunchNotes,
                            LastPunchShiftStartDateTime = employeeCurrentActivity.LastPunchShiftStartDateTimeUtc.HasValue ? (DateTime?)TimeZoneInfo.ConvertTime(
                                DateTime.SpecifyKind(employeeCurrentActivity.LastPunchShiftStartDateTimeUtc.Value,
                                    DateTimeKind.Utc),
                                employeeCurrentActivity.TimeZone) : null,
                            LastPunchShiftEndDateTime = employeeCurrentActivity.LastPunchShiftEndDateTimeUtc.HasValue ? (DateTime?)TimeZoneInfo.ConvertTime(
                                DateTime.SpecifyKind(employeeCurrentActivity.LastPunchShiftEndDateTimeUtc.Value,
                                    DateTimeKind.Utc),
                                employeeCurrentActivity.TimeZone) : null,
                            ScheduledShiftDuration = employeeCurrentActivity.ScheduledShiftDuration,
                            LastPunchShiftStartedDateTime = employeeCurrentActivity.LastPunchShiftStartedDateTimeUtc.HasValue ? (DateTime?)TimeZoneInfo.ConvertTime(
                                DateTime.SpecifyKind(employeeCurrentActivity.LastPunchShiftStartedDateTimeUtc.Value,
                                    DateTimeKind.Utc),
                                employeeCurrentActivity.TimeZone) : null,
                            LastPunchShiftEndedDateTime = employeeCurrentActivity.LastPunchShiftEndedDateTimeUtc.HasValue ? (DateTime?)TimeZoneInfo.ConvertTime(
                                DateTime.SpecifyKind(employeeCurrentActivity.LastPunchShiftEndedDateTimeUtc.Value,
                                    DateTimeKind.Utc),
                                employeeCurrentActivity.TimeZone) : null,
                            AccountPTOEarningCodeName = employeeCurrentActivity.AccountPTOEarningCodeName
                        };

                        if (staffCurrentActivity.LastPunchShiftEndDateTime.HasValue)
                        {
                            staffCurrentActivity.LastPunchShiftStatusBadge =
                                TimeSheetPunchShiftViewModel.GetEarlyLateStatusGhostly(
                                    staffCurrentActivity.LastPunchShiftEndDateTime.Value,
                                    staffCurrentActivity.LastPunchShiftEndedDateTime,
                                    accountScheduleSetting.MinutesBeforeShiftEndsThreshold,
                                    accountScheduleSetting.MinutesAfterShiftEndsThreshold);
                        }
                        currentlyPunchedOut.Add(staffCurrentActivity);
                    }
                }

                viewModel.CurrentlyPunchedIn = currentlyPunchedIn.OrderBy(p => p.LocationName).ThenBy(p => p.JobCodeName).ThenBy(p => p.LastPunchDate);

                viewModel.CurrentlyPunchedOut = currentlyPunchedOut.OrderByDescending(p => p.LastPunchDate);
                viewModel.IsMapLinkVisible = account.GpsSetting != null && account.GpsSetting.GpsEnabled;
                viewModel.IsGpsDuringShiftsEnabled = account.GpsSetting != null && account.GpsSetting.GpsEnabled && account.GpsSetting?.GpsEnabledDuringShift == true && account.IsAlwaysOnGPSFeatureOn
                                                     && account.IsAlwaysOnGPSFeatureEligible;
                viewModel.IsGpsDuringShiftsEnabledForAnyEmployee = viewModel.IsGpsDuringShiftsEnabled && 
                    activity.Any(a => (a.Status == PunchedInOutStatus.PunchedIn || a.Status == PunchedInOutStatus.OnBreak) && a.Staff.GpsTrackingDuringShift == true); 
                viewModel.IsGpsViewAvailable = account.GpsSetting != null && account.GpsSetting.GpsEnabled
                                               && account.IsAlwaysOnGPSFeatureEligible;
                viewModel.IsAlwaysOnGPSIncluded = account.IsAlwaysOnGPSFeatureOn;
                viewModel.ShowProfilePictures = account.ShowProfilePictures;
                viewModel.SchedulingEnabled = SessionManager.Profile.IsSchedulingEnabled;
                viewModel.PTOEnabled = LoggedInUserViewModel.Account.PTOEnabled;
                viewModel.TimesheetApprovalEnabled =
                    LoggedInUserViewModel.Account.TimesheetApprovalEnabled;
                viewModel.ShowWizardIncompleteModal = !(account.PayPeriodSchedule ?? new PayPeriodSchedule{ InitialSetupComplete = false}).InitialSetupComplete;

                if (TempData["IsLogin"] != null)
                {
                    var isLogin = false;
                    bool.TryParse(TempData["IsLogin"].ToString(), out isLogin);
                    viewModel.IsLogin = isLogin;
                }
                if (!viewModel.IsLogin)
                {
                    if (Request.UrlReferrer != null && Request.UrlReferrer.AbsolutePath.ToLower() == Url.Action("Logon", "Account").ToLower())
                    {
                        viewModel.IsLogin = true;
                    }
                }

                return View(viewModel);
            }

            return RedirectToAction("Punch", "Employee");
        }

        private List<Expression<Func<Staff, object>>> GetStaffIncludes(Model.Account account)
        {
            List<Expression<Func<Staff, object>>> includes = new List<Expression<Func<Staff, object>>>
            {
                s => s.Account,
                s => s.Account.ScheduleSetting,
                s => s.User
            };

            if ((SchedulePermissionEnum?)account.ScheduleSetting?.SchedulePermission == SchedulePermissionEnum.Filtered)
            {
                includes.AddRange(new List<Expression<Func<Staff, object>>>
                    {
                            s => s.StaffLocations,
                            s => s.StaffJobCodes,
                            s => s.StaffPositions
                    });
            }

            return includes;
        }

        private void CheckPayrollSubmitDue(DashboardViewModel viewModel, Model.Account account)
        {
            if (account.Id == Model.Constants.BuddyPunchDemo || account.Id == Model.Constants.KevinBuddyPunchDemo_DemoEnv)
            {
                // don't show this for the account we use for customer demo's
                return;
            }
            if (string.IsNullOrEmpty(viewModel.ErrorMessageHeader))
            {
                var payDate = _payperiodService.TryGetPayperiodDueToday(account);
                if (payDate != null)
                {
                    var payrollPayDateDayOfWeek = payDate.Value.DayOfWeek.ToString();
                    var payrollPayDate = payDate.Value.ToString("d", account.CultureInfo);
                    var approvalDeadline = new DateTime(DateTime.Now.Date.Year, DateTime.Now.Month, DateTime.Now.Day, 15, 0, 0, 0);
                    var approvalDeadlineUTC = TimeZoneInfo.ConvertTime(approvalDeadline, TimeZoneInfo.FindSystemTimeZoneById("Central Standard Time"), TimeZoneInfo.Utc);
                    var approvalDeadlineLocal = TimeZoneInfo.ConvertTimeFromUtc(approvalDeadlineUTC, TimeZoneInfo.FindSystemTimeZoneById(account.TimeZoneId));
                    var payrollDueTime = approvalDeadlineLocal.ToString("t");

                    viewModel.ErrorMessageHeader = "Payroll Submission Due";
                    viewModel.ErrorMessage =
                        $"**** URGENT **** URGENT **** " +
                        $"We have NOT RECEIVED payroll data for your {payrollPayDateDayOfWeek}, {payrollPayDate} payday. " +
                        $"APPROVED PAYROLL DATA MUST BE RECEIVED BY TODAY, by " +
                        $"{payrollDueTime}.";
                    viewModel.ErrorMessageNavigationUrl = Url.Action("PreparePayroll", "Payroll");
                }
            }
        }

        private async Task<IEnumerable<DashboardViewModel.DashboardShiftViewModel>> GetCurrentShifts(Staff staff)
        {
            var localDateTime = TimeZoneHelper.ConvertToLocalDateTime(DateTime.UtcNow, staff);
            var shifts =
                await _shiftService.GetShiftsAsync(staff, localDateTime.Date, localDateTime.Date.AddDays(1));
            return shifts.Where(s => s.Published).Select(s => new DashboardViewModel.DashboardShiftViewModel()
            {
                Staff = EmployeeMapper.MapToEmployeeModelFromStaff( s.Staff, s.Staff.User),
                ShiftStartDateTime = TimeZoneHelper.ConvertToLocalDateTime( s.StartTimeUtc, staff),
                ShiftEndDateTime = TimeZoneHelper.ConvertToLocalDateTime(s.EndTimeUtc, staff),
                LocationName = s.Location?.Name,
                JobCodeName = s.JobCode?.Name,
                PositionName = s.Position?.Name,
                Duration = s.GetDuration()
            });
        }

        private IEnumerable<PTORequestPendingViewModel> GetPTOPendingApproval(User user)
        {
            var pendingRequests = _ptoRequestService.GetPendingRequests(user).Where(p => p.Staff.IsActive).Select(p => new 
            {
                Id = p.StaffId,
                FirstName = p.Staff.FirstName,
                LastName = p.Staff.LastName,
                ProfileMiniImageUrl = p.Staff.ProfileMiniImageUrl,
                RequestDate = TimeZoneHelper.ConvertToLocalDateTime(p.RequestDate, TimeZoneHelper.GetTimeZoneInfo(p.Staff))
            });
            var pendingPTOPunches = _punchService.GetPendingPTO(user)
                .Where(punch => punch.Timesheet.Staff.IsActive).Select(p => new 
                {
                    Id = p.Timesheet.StaffId,
                    FirstName = p.Timesheet.Staff.FirstName,
                    LastName = p.Timesheet.Staff.LastName,
                    ProfileMiniImageUrl = p.Timesheet.Staff.ProfileMiniImageUrl,
                    RequestDate = TimeZoneHelper.ConvertToLocalDateTime( p.PunchInDateTime, TimeZoneHelper.GetTimeZoneInfo(p.Timesheet.Staff))
                })
                .ToList();

            return pendingRequests.Union(pendingPTOPunches).GroupBy(p =>
                new
                {
                    p.Id,
                    p.FirstName,
                    p.LastName,
                    p.ProfileMiniImageUrl                   
                }).Select(g => new PTORequestPendingViewModel
            {
                PendingCount = g.Count(),
                StartDate = g.Min(p => p.RequestDate),
                Staff = new Model.Punch.EmployeeModel{
                    Id = g.Key.Id,
                    FirstName = g.Key.FirstName,
                    LastName = g.Key.LastName,
                    ProfileMiniImageUrl = g.Key.ProfileMiniImageUrl
                }
            });
        }

        public ActionResult About()
        {
            return View();
        }

        public ActionResult PayrollFeeSchedule()
        {
            return View();
        }

        [AllowAnonymous]
        public ActionResult QrScan()
        {
            return View("QrCodeScanner");
        }

        [AllowAnonymous]
        public ActionResult QrScan2()
        {
            return View("QrCodeScanner2");
        }
    }
}
