﻿@using BuddyPunch.Model.Enums
@using BuddyPunch.Web.App.Helpers
@model BuddyPunch.Web.App.ViewModel.AccountTimeEntryOptionsModel
@{
    ViewBag.Title = "Time Entry Options";
}

@Html.Partial("_ErrorSuccessPartial")

<!-- BEGIN PAGE CONTENT-->
@using (Html.BeginMetronicPortlet("Time Entry Options"))
{

    using (Html.BeginForm("TimeEntryOptions", "Account", FormMethod.Post, new { @class = "" }))
    {
        <div class="form-body">
            <div class="row">
                <div class="col-md-12">
                    @Html.AntiForgeryToken()
                    @Html.Partial("_ValidationSummary", ViewData.ModelState)

                    <div class="form-group">
                        <label class="control-label">Default Time Entry Mode</label>
                        @{
                            var timeEntryModes = new[]
                            {
                                new SelectListItem
                                {
                                    Text = "Punches Only (start and end times)",
                                    Selected = Model.DefaultTimeEntryMode == (int)TimeEntryModeEnum.PunchesOnly,
                                    Value = ((int)TimeEntryModeEnum.PunchesOnly).ToString()
                                },
                                new SelectListItem
                                {
                                    Text = "Time Entry Only",
                                    Selected = Model.DefaultTimeEntryMode == (int)TimeEntryModeEnum.TimeEntryOnly,
                                    Value = ((int)TimeEntryModeEnum.TimeEntryOnly).ToString()
                                },
                                new SelectListItem
                                {
                                    Text = "Punches and Time Entry",
                                    Selected = Model.DefaultTimeEntryMode == (int)TimeEntryModeEnum.PunchesAndTimeEntry,
                                    Value = ((int)TimeEntryModeEnum.PunchesAndTimeEntry).ToString()
                                },
                            };
                        }
                        @Html.DropDownListFor(model => model.DefaultTimeEntryMode, timeEntryModes, new { @class = "form-control input-large" })
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <br />
                    <h5>Per-employee settings</h5>
                    <br />

                    <table id="employees_table" class="table table-striped table-hover table-fixed-header">
                        <thead>
                            <tr>
                                <th>Employee Name</th>
                                <th style="width: 25%">
                                    <label class="m-radio">
                                        <input type="radio" class="group-checkable" name="groupcheck" data-set="#employees_table tr td:nth-child(2) input" autocomplete="off" />
                                        <span></span>
                                        Punches Only (start and end times)
                                    </label>

                                </th>
                                <th style="width: 25%">
                                    <label class="m-radio">
                                        <input type="radio" class="group-checkable" name="groupcheck" data-set="#employees_table tr td:nth-child(3) input" autocomplete="off" />
                                        <span></span>
                                        Time Entry Only (duration)
                                    </label>
                                    <label>
                                    </label>
                                </th>
                                <th style="width: 25%">
                                    <label class="m-radio">
                                        <input type="radio" class="group-checkable" name="groupcheck" data-set="#employees_table tr td:nth-child(4) input" autocomplete="off" />
                                        <span></span>
                                        Punches &amp; Time Entry
                                    </label>
                                    <label>
                                    </label>

                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @for (int i = 0; i < Model.EmployeeTimeEntryModes.Count; i++)
                            {
                                <tr>
                                    <td>
                                        @Model.EmployeeTimeEntryModes[i].FullName
                                        @Html.HiddenFor(m => m.EmployeeTimeEntryModes[i].Id)
                                    </td>
                                    <td>
                                        <label class="m-radio">
                                            @Html.RadioButtonFor(m => m.EmployeeTimeEntryModes[i].TimeEntryModeId, ((int)TimeEntryModeEnum.PunchesOnly).ToString(),
                                                Model.EmployeeTimeEntryModes[i].TimeEntryModeId == (int)TimeEntryModeEnum.PunchesOnly ? new { @checked = "checked" } : null)

                                            <span></span>
                                            &nbsp;
                                        </label>
                                    </td>
                                    <td>
                                        <label class="m-radio">
                                            @Html.RadioButtonFor(m => m.EmployeeTimeEntryModes[i].TimeEntryModeId, ((int)TimeEntryModeEnum.TimeEntryOnly).ToString(),
                                                Model.EmployeeTimeEntryModes[i].TimeEntryModeId == (int)TimeEntryModeEnum.TimeEntryOnly ? new { @checked = "checked" } : null)

                                            <span></span>
                                            &nbsp;
                                        </label>
                                    </td>
                                    <td>
                                        <label class="m-radio">
                                            @Html.RadioButtonFor(m => m.EmployeeTimeEntryModes[i].TimeEntryModeId, ((int)TimeEntryModeEnum.PunchesAndTimeEntry).ToString(),
                                                Model.EmployeeTimeEntryModes[i].TimeEntryModeId == (int)TimeEntryModeEnum.PunchesAndTimeEntry ? new { @checked = "checked" } : null)

                                            <span></span>
                                            &nbsp;
                                        </label>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="form-actions">
                <button type="submit" class="btn btn-primary"><i class="icon-ok"></i> Save</button>
                @Html.ActionLink("Cancel", "Index", "Home", null, new { @class = "btn btn-default" })
            </div>
        </div>
    }

}

@section PageBeforeEndBodySection
{
    <script>
        jQuery(document).ready(function () {
            initDataTable('#employees_table', {
                bInfo: false,
                paging: false,
            });

            $('.group-checkable').change(function () {
                var set = jQuery(this).attr("data-set");
                var checked = jQuery(this).is(":checked");

                var bodyInputs = $('#employees_table tbody input:radio');
                //console.log(bodyInputs, $(bodyInputs).length);
                bodyInputs.prop("checked", false);

                //console.log(set, $(set).length);
                jQuery(set).each(function () {
                    if (checked) {
                        $(this).prop("checked", true);
                    } else {
                        $(this).prop("checked", false);
                    }
                });
            });

        });
    </script>
}