﻿@using BuddyPunch.Web.App.Helpers
@model BuddyPunch.Web.App.ViewModel.PositionViewModel
@{
    ViewBag.Title = "Edit Position";
}
<!-- BEGIN PAGE CONTENT-->

@helper PortletCaption()
{
    <div class="m-portlet__head-caption">
        <div class="m-portlet__head-title">
            <h3 class="m-portlet__head-text">
                Edit Position
            </h3>
        </div>
    </div>
    <div class="m-portlet__head-tools">
        <div class="edit-actions">
            <form method="post" action="@Url.Action("Clone")">
                @Html.Hidden("id", Model.Id)
                <button class="btn btn-secondary cloneBtn" type="submit">Clone</button>
            </form>
            @if (Model.CanDelete)
            {
                <form method="post" action="@Url.Action("Delete")">
                    @Html.Hidden("id", Model.Id)
                    <button id="deletePosition" class="btn btn-danger" type="submit">Delete</button>
                </form>
            }
        </div>
    </div>
}

@Html.Partial("_ErrorSuccessPartial")

@using (Html.BeginMetronicPortlet(PortletCaption()))
{
    using (Html.BeginForm())
    {
        <div class="form-body">
            @Html.AntiForgeryToken()
            @Html.Partial("_ValidationSummary", ViewData.ModelState)

            @Html.HiddenFor(m => m.Id)
            <div class="form-group">
                @Html.LabelFor(model => model.Name, new { @class = "control-label" })
                @Html.TextBoxFor(model => model.Name, new { @class = "form-control input-medium" })
                @Html.ValidationMessageFor(model => model.Name)
            </div>

            <div class="form-group">
                <label class="m-checkbox">
                    <input id="Enabled"
                           type="checkbox"
                           name="Enabled"
                           value="true"
                           @(Model.Enabled ? "checked" : "")
                           @(!Model.CanDisable ? "disabled" : "") />
                    <span></span>
                    Active
                    @if (!Model.CanDisable)
                    {
                        List<string> linkedEntities = new List<string>();

                        if (Model.DisableErrors.Any(x => x.Contains("geofence")))
                        {
                            linkedEntities.Add("geofence(s)");
                        }

                        if (Model.DisableErrors.Any(x => x.Contains("shift")))
                        {
                            linkedEntities.Add("current or future shift(s)");
                        }

                        <p class="font-italic d-inline" style="color: #575962">(used by @(string.Join(" and ", linkedEntities)))</p>
                        <input id="Enabled"
                               type="hidden"
                               name="Enabled"
                               value="true" />
                    }
                </label>
            </div>

            <div class="row">
                <div class="col-md-6">
                    @if (Model.Employees.Any())
                    {
                        <br />
                        <h5>Select the employees assigned to this Position. An employee may be assigned multiple positions.</h5>
                        <table class="table table-striped table-hover" id="employees_table">
                            <thead>
                                <tr>
                                    <th>
                                        <label class="m-checkbox">
                                            <input type="checkbox" class="group-checkable" data-set="#employees_table .checkboxes" />
                                            <span></span>
                                            All
                                        </label>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>

                                @foreach (var staff in Model.Employees)
                                {
                                    var autoAssignedEntities = new List<string>();

                                    if (Model.PositionShiftsStaffIds.Contains(staff.Id))
                                    {
                                        autoAssignedEntities.Add("current or future shift(s)");
                                    }

                                    <tr>
                                        <td>
                                            <label class="m-checkbox">
                                                <input class="checkboxes"
                                                       id="staff@(staff.Id)"
                                                       type="checkbox"
                                                       name="PositionStaffIds"
                                                       value="@staff.Id"
                                                       @(Model.PositionStaffIds.Contains(staff.Id) ? "checked" : "")
                                                       @(autoAssignedEntities.Count > 0 ? "disabled" : "") />

                                                @if (autoAssignedEntities.Count > 0)
                                                {
                                                    <input id="staff@(staff.Id)"
                                                           type="hidden"
                                                           name="PositionStaffIds"
                                                           value="@staff.Id" />
                                                }
                                                <span></span>
                                                @staff.FullName
                                                @if (autoAssignedEntities.Count > 0)
                                                {
                                                    @: <p class="font-italic d-inline" style="color: #575962">(used by @(string.Join(" and ", autoAssignedEntities)))</p>
                                                }
                                            </label>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    }
                </div>
            </div>
            <div class="form-actions">
                <input type="submit" value="Save" class="btn btn-primary" />
                @Html.ActionLink("Cancel", "Index", null, new { @class = "btn btn-default" })
            </div>
        </div>
    }

}

@section PageHeadSection
{
}

@section PageBeforeEndBodySection
{
    <script>
        jQuery(document).ready(function () {
            $("#employees_table").setupSelectAllTable();
            $('.cloneBtn').click(function (event) {
                event.preventDefault();
                swal({
                    title: "Clone this location?",
                    html: "A cloned position will be created with the same employees assigned.",
                    type: "question",
                    showCancelButton: !0,
                    confirmButtonText: "Yes, I understand"
                }).then(function (e) {
                    if (e.value) {
                        $(event.target).parents('form').submit();
                    }
                });
            });

            $('#deletePosition').click(function (event) {
                event.preventDefault();
                swal({
                    title: "Are you sure?",
                    html: "You won't be able to revert this!",
                    type: "warning",
                    showCancelButton: !0,
                    confirmButtonText: "Yes, I understand"
                }).then(function (e) {
                    if (e.value) {
                        $(event.target).parents('form').submit();
                    }
                });
            });
        });
    </script>
}