﻿@using BuddyPunch.Web.App.Helpers
@model BuddyPunch.Web.App.ViewModel.PositionViewModel
@{
    ViewBag.Title = "Add a New Position";
}
<!-- BEGIN PAGE CONTENT-->

@using (Html.BeginMetronicPortlet("Add a New Position"))
{
	

    using (Html.BeginForm())
     {
         <div class="form-body">
             @Html.AntiForgeryToken()
             @Html.Partial("_ValidationSummary", ViewData.ModelState)
             <div class="form-group">
                 @Html.LabelFor(model => model.Name, new { @class="control-label"})
                 @Html.TextBoxFor(model => model.Name, new { @class = "form-control input-medium" })
                 @Html.ValidationMessageFor(model => model.Name)
             </div>          

             <div class="form-group">
                 @Html.MetronicCheckboxFor(model => model.Enabled, "Active")
             </div>
             <div class="row">
                 <div class="col-md-6">
                     @if (Model.Employees != null && Model.Employees.Any())
                     {
                         <br/>
                         <h5>Select the employees assigned to this Position. An employee may be assigned multiple positions.</h5>
                         <table class="table table-striped table-hover" id="employees_table">
                             <thead>
                             <tr>
                                 <th>
                                     <label class="m-checkbox">
                                         <input type="checkbox" class="group-checkable" data-set="#employees_table .checkboxes"/>
                                         <span></span>
                                         All
                                     </label>
                                 </th>
                             </tr>
                             </thead>
                             <tbody>

                             @foreach (var staff in Model.Employees)
                             {
                                 <tr>
                                     <td>
                                         <label class="m-checkbox">
                                             <input class="checkboxes"
                                                    id="staff@(staff.Id)"
                                                    type="checkbox"
                                                    name="PositionStaffIds"
                                                    value="@staff.Id"
                                                    @(Model.PositionStaffIds.Contains(staff.Id) ? "checked" : "") />
                                             <span></span>
                                             @staff.FullName
                                         </label>
                                     </td>
                                 </tr>
                             }
                             </tbody>
                         </table>
                     }
                 </div>
             </div>
             <div class="form-actions">
                 <input type="submit" value="Save" class="btn btn-primary" />
                 @Html.ActionLink("Cancel", "Index", null, new {@class="btn btn-default"})
             </div>
         </div>
     }         
}

@section PageHeadSection
{
}

@section PageBeforeEndBodySection
{
<script>
        jQuery(document).ready(function() {
            // initiate layout and plugins
        });
    </script>

<script>
        jQuery(document).ready(function () {
                $("#employees_table").setupSelectAllTable();
            }
        );
</script>
}