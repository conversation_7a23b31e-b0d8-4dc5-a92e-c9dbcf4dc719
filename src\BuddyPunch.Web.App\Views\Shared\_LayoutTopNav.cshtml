@using BuddyPunch.Web.App
@using BuddyPunch.Web.App.Helpers
@using BuddyPunch.Web.App.ViewModel
@model BuddyPunch.Web.App.ViewModel.TopNavViewModel
@{
    bool alwaysOnGPSEnabled = Model.SideBarViewModel.IsAlwaysOnGPSFeatureEligible && Model.SideBarViewModel.IsAlwaysOnGPSFeatureOn;
}

@helper TopMenuItem(string url, string icon, string content, string target = "")
{
    <li class="m-nav__item">
        <a href="@url" class="m-nav__link" target="@target">
            <span class="m-nav__link-text"><i class="@icon"></i> @content</span>
        </a>
    </li>
}

@helper SubMenuItem(string content, string action, string controller, HtmlString badge = null, string icon = null)
{
    var urlHelper = new UrlHelper(this.ViewContext.RequestContext);
    var url = urlHelper.Action(action, controller);

    @: @SubMenuItem(url, content, icon: icon, badge: null)
}

@helper SubMenuItem(string url, string content, HtmlString badge = null, string icon = null)
{
    <li class="m-menu__item " aria-haspopup="true">
        <a href="@url" class="m-menu__link ">
            <span class="m-menu__link-title">
                <span class="m-menu__link-wrap">
                    <span class="m-menu__link-text d-flex align-items--baseline">
                        @if (!string.IsNullOrWhiteSpace(icon))
                        {
                            <i class="@icon" style="min-width:23px;"></i>
                        }
                        <div>@content</div>
                    </span>
                    @if (badge != null)
                    {
                        <span class="m-menu__link-badge">@badge</span>
                    }
                </span>
            </span>
        </a>
    </li>
}

@helper SubMenuItemPayrollDocuments(string content)
{
    <li class="m-menu__item  m-menu__item--submenu"
        aria-haspopup="true"
        m-menu-submenu='click'
        m-menu-submenu-toggle="hover"
        data-menu-submenu-toggle="click"
        data-redirect="true">
        <a href="javascript:;" class="m-menu__link m-menu__toggle">
            <span class="m-menu__link-text">@content</span>
            <i class="m-menu__hor-arrow la la-angle-right"></i>
            <i class="m-menu__ver-arrow la la-angle-right"></i>
        </a>
        <div class="m-menu__submenu m-menu__submenu--right">
            <span class="m-menu__arrow"></span>
            <ul class="m-menu__subnav">
                <li class="m-menu__item" aria-haspopup="true" data-redirect="true">
                    <a href="@Url.Action("FilingAuthorizationDocuments", "Payroll")" class="m-menu__link">
                        <span class="m-menu__link-text">
                            Filing Authorizations
                        </span>
                    </a>
                </li>
                <li class="m-menu__item" aria-haspopup="true" data-redirect="true">
                    <a href="@Url.Action("CompanyTaxDocuments", "Payroll")" class="m-menu__link">
                        <span class="m-menu__link-text">
                            Tax Documents
                        </span>
                    </a>
                </li>
            </ul>
        </div>
    </li>
}

@helper SubMenuItemPayrollReports(string content)
{
    <li class="m-menu__item  m-menu__item--submenu"
        aria-haspopup="true"
        m-menu-submenu='click'
        m-menu-submenu-toggle="hover"
        data-menu-submenu-toggle="click"
        data-redirect="true">
        <a href="javascript:;" class="m-menu__link m-menu__toggle">
            <span class="m-menu__link-text">@content</span>
            <i class="m-menu__hor-arrow la la-angle-right"></i>
            <i class="m-menu__ver-arrow la la-angle-right"></i>
        </a>
        <div class="m-menu__submenu m-menu__submenu--right">
            <span class="m-menu__arrow"></span>
            <ul class="m-menu__subnav">
                <li class="m-menu__item" aria-haspopup="true" data-redirect="true">
                    <a href="@Url.Action("PayrollSummary", "Payroll")" class="m-menu__link">
                        <span class="m-menu__link-text">
                            Payroll Summary
                        </span>
                    </a>
                </li>
                <li class="m-menu__item" aria-haspopup="true" data-redirect="true">
                    <a href="@Url.Action("PayrollJournal", "Payroll")" class="m-menu__link">
                        <span class="m-menu__link-text">
                            Payroll Journal
                        </span>
                    </a>
                </li>
                <li class="m-menu__item" aria-haspopup="true" data-redirect="true">
                    <a href="@Url.Action("PayrollCashRequirementReport", "Payroll")"
                       class="m-menu__link">
                        <span class="m-menu__link-text">
                            Payroll Cash Requirement
                        </span>
                    </a>
                </li>
                <li class="m-menu__item" aria-haspopup="true" data-redirect="true">
                    <a href="@Url.Action("ContractorPayments", "Payroll")" class="m-menu__link">
                        <span class="m-menu__link-text">
                            Contractor Payments
                        </span>
                    </a>
                </li>
                <li class="m-menu__item" aria-haspopup="true" data-redirect="true">
                    <a href="@Url.Action("W2Preview", "Payroll")" class="m-menu__link">
                        <span class="m-menu__link-text">
                            W2 Preview
                        </span>
                    </a>
                </li>
                <li class="m-menu__item" aria-haspopup="true" data-redirect="true">
                    <a href="@Url.Action("W4ExemptStatus", "Payroll")" class="m-menu__link">
                        <span class="m-menu__link-text">
                            W4 Exempt Status
                        </span>
                    </a>
                </li>
            </ul>
        </div>
    </li>
}


@helper SubMenuItemReportBy(string content)
{
    <li class="m-menu__item  m-menu__item--submenu"
        aria-haspopup="true"
        m-menu-submenu='click'
        m-menu-submenu-toggle="hover"
        data-menu-submenu-toggle="click"
        data-redirect="true">
        <a href="javascript:;" class="m-menu__link m-menu__toggle">
            <span class="m-menu__link-text">@content</span>
            <i class="m-menu__hor-arrow la la-angle-right"></i>
            <i class="m-menu__ver-arrow la la-angle-right"></i>
        </a>
        <div class="m-menu__submenu m-menu__submenu--right">
            <span class="m-menu__arrow"></span>
            <ul class="m-menu__subnav">
                <li class="m-menu__item" aria-haspopup="true" data-redirect="true">
                    <a href="@Url.Action("By", "Report", new {initialGroup = (int)ReportByGroup.Location})"
                       class="m-menu__link">
                        <span class="m-menu__link-text">
                            Location
                        </span>
                    </a>
                </li>
                <li class="m-menu__item" aria-haspopup="true" data-redirect="true">
                    <a href="@Url.Action("By", "Report", new {initialGroup = (int)ReportByGroup.Department})"
                       class="m-menu__link">
                        <span class="m-menu__link-text">
                            Department
                        </span>
                    </a>
                </li>
                @if (Model.SideBarViewModel.IsScheduleVisible)
                {
                    <li class="m-menu__item" aria-haspopup="true" data-redirect="true">
                        <a href="@Url.Action("By", "Report", new {initialGroup = (int) ReportByGroup.Position})"
                           class="m-menu__link">
                            <span class="m-menu__link-text">
                                Position
                            </span>
                        </a>
                    </li>
                }
                @if (Model.SideBarViewModel.PTOEnabled)
                {
                    <li class="m-menu__item" aria-haspopup="true" data-redirect="true">
                        <a href="@Url.Action("By", "Report", new {initialGroup = 4, IsTimeOffOnly = true})" class="m-menu__link">
                            <span class="m-menu__link-text">
                                Time Off
                            </span>
                        </a>
                    </li>
                }
            </ul>
        </div>
    </li>
}


@helper SubMenuItemScheduling(string content)
{
    <li class="m-menu__item  m-menu__item--submenu"
        aria-haspopup="true"
        m-menu-submenu='click'
        m-menu-submenu-toggle="hover"
        data-menu-submenu-toggle="click"
        data-redirect="true">
        <a href="javascript:;" class="m-menu__link m-menu__toggle">
            <span class="m-menu__link-text">@content</span>
            <i class="m-menu__hor-arrow la la-angle-right"></i>
            <i class="m-menu__ver-arrow la la-angle-right"></i>
        </a>
        <div class="m-menu__submenu m-menu__submenu--right">
            <span class="m-menu__arrow"></span>
            <ul class="m-menu__subnav">
                <li class="m-menu__item" aria-haspopup="true" data-redirect="true">
                    <a href="@Url.Action("EarlyLate", "Report")"
                       class="m-menu__link">
                        <span class="m-menu__link-text">
                            Early / Late
                        </span>
                    </a>
                </li>
                <li class="m-menu__item" aria-haspopup="true" data-redirect="true">
                    <a href="@Url.Action("Absence", "Report")"
                       class="m-menu__link">
                        <span class="m-menu__link-text">
                            Absence
                        </span>
                    </a>
                </li>
            </ul>
        </div>
    </li>
}

@helper EmptyMenuItem()
{
    <li class="m-menu__item">
        <h3 class="m-menu__heading m-menu__toggle">
            <span class="m-menu__link-text"></span>
        </h3>
    </li>
}

@helper SubMenuHeader(string title, bool hideOnDesktop = false)
{
    <h3 class="m-menu__heading m-menu__toggle @(hideOnDesktop ? "m--hidden-desktop" : string.Empty)">
        <span class="m-menu__link-text">@title</span>
        <i class="m-menu__ver-arrow la la-angle-right"></i>
    </h3>
}

@helper MainMenuItem(string content, bool isActive, List<HelperResult> subMenuItems, string url = null, HtmlString badge = null)
{
    @: @MainMenuItem(content, isActive, new[] { subMenuItems }, url, badge)
}
@helper MainMenuItem(string content, bool isActive, List<HelperResult>[] subMenuItems, string url = null, HtmlString badge = null, bool showNewLabel = false)
{
    var hasSubmenu = subMenuItems != null && subMenuItems.Length > 0;

    <li class="m-menu__item @(hasSubmenu ? "m-menu__item--submenu" : "")
    @(isActive ? "m-menu__item--active" : "") m-menu__item--rel"
        @(hasSubmenu ? "m-menu-submenu-toggle=click" : "")
        aria-haspopup="true">
        <a href="@url" class="m-menu__link @(hasSubmenu ? "m-menu__toggle" : "")">
            @if (badge != null)
            {
                <span class="m-menu__link-title">
                    <span class="m-menu__link-wrap">
                        <span class="m-menu__link-text">@content</span>
                        <span class="m-menu__link-badge">
                            @badge
                        </span>
                    </span>
                </span>
            }
            else
            {
                <span class="m-menu__item-here"></span>
                <span class="m-menu__link-text">
                    @content
                    @if (showNewLabel)
                    {
                        <span class="new-item-label">new</span>
                    }
                </span>
            }
            @if (hasSubmenu)
            {
                <i class="m-menu__hor-arrow la la-angle-down"></i>
                <i class="m-menu__ver-arrow la la-angle-right"></i>
            }
        </a>
        @if (hasSubmenu)
        {
            <div class="m-menu__submenu @(subMenuItems.Length > 1?"m-menu__submenu--fixed" : "m-menu__submenu--classic") m-menu__submenu--left"
                 style="width: @(subMenuItems.Length * 300)px"
                 data-default-width="@(subMenuItems.Length * 300)px">
                <span class="m-menu__arrow m-menu__arrow--adjust"
                      style="left: 58.8px;"></span>
                @if (subMenuItems.Length > 1)
                {
                    <div class="m-menu__subnav">
                        <ul class="m-menu__content">
                            @foreach (var submenuColumn in subMenuItems)
                            {
                                <li class="m-menu__item">
                                    @if (subMenuItems.Length > 1)
                                    {
                                        var submenuHeader = submenuColumn.First();
                                        @Html.Raw(submenuHeader.ToString())
                                    }
                                    <ul class="m-menu__inner">
                                        @foreach (var submenu in submenuColumn.Skip(1))
                                        {
                                            @Html.Raw(submenu.ToString())
                                        }
                                    </ul>
                                </li>
                            }
                        </ul>
                    </div>
                }
                else
                {
                    <ul class="m-menu__subnav">
                        @foreach (var submenu in subMenuItems[0])
                        {
                            @Html.Raw(submenu.ToString())
                        }
                    </ul>
                }
            </div>
        }
    </li>
}

<header class="m-grid__item m-header" data-minimize="minimize" data-minimize-offset="200" data-minimize-mobile-offset="200">
    <div class="m-header__top">
        <div class="m-container m-container--fluid m-container--full-height m-page__container">
            <div class="m-stack m-stack--ver m-stack--desktop">
                <!-- begin::Brand -->
                <div class="m-stack__item m-brand">
                    <div class="m-stack m-stack--ver m-stack--general m-stack--inline">
                        <div class="m-stack__item m-stack__item--middle m-brand__logo">
                            <a href="/" class="m-brand__logo-wrapper">
                                <img alt="Buddy Punch" class="base-logo" src="@Url.Cdn("~/Content/images/buddypunch_logo.svg")">
                            </a>
                        </div>
                        <div class="m-stack__item m-stack__item--middle m-brand__tools">
                            <!-- begin::Responsive Header Menu Toggler-->
                            <a id="m_aside_header_menu_mobile_toggle" href="javascript:;" class="m-brand__icon m-brand__toggler m--visible-tablet-and-mobile-inline-block">
                                <span></span>
                            </a>
                            <!-- end::Responsive Header Menu Toggler-->
                            <!-- begin::Topbar Toggler-->
                            <a id="m_aside_header_topbar_mobile_toggle" href="javascript:;" class="m-brand__icon m--visible-tablet-and-mobile-inline-block">
                                <i class="flaticon-more"></i>
                            </a>
                            <!--end::Topbar Toggler-->
                        </div>
                    </div>
                </div>
                <!-- end::Brand -->
                <!-- begin::Topbar -->
                <div class="m-stack__item m-stack__item--fluid m-header-head" id="m_header_nav">
                    <div id="m_header_topbar" class="m-topbar  m-stack m-stack--ver m-stack--general">
                        @if (Model.SideBarViewModel.IsEmployeesVisible)
                        {
                            <div class="m-stack__item m-stack__item--middle m-dropdown m-dropdown--arrow m-dropdown--large m-dropdown--mobile-full-width m-dropdown--align-right m-dropdown--skin-light m-header-search m-header-search--expandable m-header-search--skin-light" id="bp_quicksearch" m-quicksearch-mode="default">
                                <!--BEGIN: Search Form -->
                                <form class="m-header-search__form">
                                    <div class="m-header-search__wrapper">
                                        <span class="m-header-search__icon-search" id="m_quicksearch_search">
                                            <i class="la la-search"></i>
                                        </span>
                                        <span class="m-header-search__input-wrapper">
                                            <input autocomplete="off"
                                                   type="text"
                                                   name="q"
                                                   class="m-header-search__input"
                                                   value=""
                                                   placeholder="Search Employees"
                                                   id="m_quicksearch_input">
                                        </span>
                                        <span class="m-header-search__icon-close" id="m_quicksearch_close">
                                            <i class="la la-remove"></i>
                                        </span>
                                        <span class="m-header-search__icon-cancel" id="m_quicksearch_cancel">
                                            <i class="la la-remove"></i>
                                        </span>
                                    </div>
                                </form>
                                <!--END: Search Form -->
                                <!--BEGIN: Search Results -->
                                <div class="m-dropdown__wrapper">
                                    <div class="m-dropdown__arrow m-dropdown__arrow--center"></div>
                                    <div class="m-dropdown__inner">
                                        <div class="m-dropdown__body">
                                            <div class="m-dropdown__scrollable m-scrollable" data-scrollable="true" data-max-height="300" data-mobile-max-height="200">
                                                <div class="m-dropdown__content m-list-search m-list-search--skin-light"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!--BEGIN: END Results -->
                            </div>
                        }
                        <div class="m-stack__item m-topbar__nav-wrapper">
                            <ul class="m-topbar__nav m-nav m-nav--inline text-nowrap d-flex align-items-center">
                                <li class="support-dropdown m-dropdown m-dropdown--inline m-dropdown--arrow m-dropdown--align-left m-dropdown--align-push" m-dropdown-toggle="click" aria-expanded="true">
                                    <a href="#" class="dropdown-toggle m-dropdown__toggle btn m-btn  m-btn--icon">
                                        <span>Support</span>
                                    </a>
                                    <div class="m-dropdown__wrapper">
                                        <span class="m-dropdown__arrow m-dropdown__arrow--left m-dropdown__arrow--adjust" style="right: auto; left: 65.0315px;"></span>
                                        <div class="m-dropdown__inner">
                                            <div class="m-dropdown__body">
                                                <div class="m-dropdown__content">
                                                    <ul class="m-nav">
                                                        @if (!ViewBag.IsMobileBrowser)
                                                        {
                                                            if (Model.SideBarViewModel.IsAdministrator)
                                                            {
                                                                @TopMenuItem("https://docs.buddypunch.com/en/collections/9641667-account-setup", "icon-info", "Getting Started", "_blank")
                                                            }
                                                            else if (Model.SideBarViewModel.IsDashboardVisible)
                                                            {
                                                                @TopMenuItem("https://docs.buddypunch.com/en/collections/9641744-manager-help", "icon-info", "Getting Started", "_blank")
                                                            }
                                                            else
                                                            {
                                                                @TopMenuItem("https://docs.buddypunch.com/en/collections/9641750-employee-help", "icon-info", "Getting Started", "_blank")
                                                            }
                                                        }

                                                        @TopMenuItem("https://docs.buddypunch.com/", "icon-question", "Knowledge Base", "_blank")
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li class="m-dropdown m-dropdown--inline m-dropdown--arrow m-dropdown--align-right m-dropdown--align-push" m-dropdown-toggle="click">
                                    <a href="#" class="m-dropdown__toggle dropdown-toggle m-dropdown__toggle btn m-btn  m-btn--icon d-flex align-items-center">
                                        <span class="username-dropdown">@Model.SideBarViewModel.UserViewModel.FirstName @Model.SideBarViewModel.UserViewModel.LastName</span>
                                        <span class="m-topbar__userpic m--padding-left-10">
                                            @if (Model.SideBarViewModel.UserViewModel.ProfileMiniImageUrl != null)
                                            {
                                                <img alt="" id="logged-in-user-mini-picture" class="profile-mini-picture" width="30" height="30" src="@Model.SideBarViewModel.UserViewModel.ProfileMiniImageUrl" data-default-src="@Url.Cdn("/dist/images/bp_user.png")">
                                            }
                                            else
                                            {
                                                <img style="display: none" alt="" id="logged-in-user-mini-picture" class="img-circle" src="@Url.Cdn("/dist/images/bp_user.png")" data-default-src="@Url.Cdn("/dist/images/bp_user.png")">
                                            }
                                        </span>
                                    </a>
                                    <div class="m-dropdown__wrapper">
                                        <span class="m-dropdown__arrow m-dropdown__arrow--right m-dropdown__arrow--adjust"></span>
                                        <div class="m-dropdown__inner">
                                            <div class="m-dropdown__body">
                                                <div class="m-dropdown__content">
                                                    <ul class="m-nav m-nav--skin-light">
                                                        @TopMenuItem(@Url.Action("Index", "Profile"), "icon-user", "My Profile")
                                                        @if (Model.SideBarViewModel.IsSettingsVisible && Model.SideBarViewModel.IsBillingVisible)
                                                        {
                                                            @TopMenuItem(@Url.Action("ViewPlan", "Account"), "", "Billing")
                                                        }

                                                        @TopMenuItem(@Url.Action("LogOff", "Account"), "icon-key", "Log Out")
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <!-- end::Topbar -->
            </div>
        </div>
    </div>
    <div class="m-header__bottom">

        <div class="m-container m-container--fluid m-container--full-height m-page__container">
            <div class="m-stack m-stack--ver m-stack--desktop">
                <!-- begin::Horizontal Menu -->
                <div class="m-stack__item m-stack__item--middle m-stack__item--fluid">

                    <button class="m-aside-header-menu-mobile-close  m-aside-header-menu-mobile-close--skin-light " id="m_aside_header_menu_mobile_close_btn"><i class="la la-close"></i></button>

                    <div id="m_header_menu" class="m-header-menu m-aside-header-menu-mobile m-aside-header-menu-mobile--offcanvas  m-header-menu--skin-dark m-header-menu--submenu-skin-light m-aside-header-menu-mobile--skin-light m-aside-header-menu-mobile--submenu-skin-light ">
                        <ul class="m-menu__nav  m-menu__nav--submenu-arrow ">
                            @if (Model.SideBarViewModel.IsSideBarVisible)
                            {
                                if (Model.SideBarViewModel.IsDashboardVisible)
                                {
                                    @MainMenuItem("Dashboard", Model.SideBarViewModel.IsDashboardActive, new List<HelperResult>[0], "/")
                                }
                                else
                                {
                                    @MainMenuItem("Time Entry", Model.SideBarViewModel.IsPunchInOutSectionActive,
                                        new List<HelperResult>[] { }
                                        , Url.Action("Punch", "Employee"))
                                }
                                // Determine if we need submenu items
                                bool needsTimeCardsSubmenu = Model.SideBarViewModel.TimesheetApprovalEnabled && 
                                                    Model.SideBarViewModel.IsDashboardVisible;
                     
                                if (needsTimeCardsSubmenu)
                                {
                                    var subMenuItems = new List<HelperResult> { 
                                        SubMenuItem(Url.Action("TimeCards", "Report"), "View All")
                                    };
    
                                    subMenuItems.Add(SubMenuItem(
                                        Url.Action("PendingApproval", "TimeCard"),
                                        $"Pending ({Model.SideBarViewModel.PendingApprovalCount})"
                                    ));
    
                                    @MainMenuItem("Time Cards", Model.SideBarViewModel.IsTimeCardsSectionActive, subMenuItems, "javascript:;")
                                }
                                else
                                {
                                    @MainMenuItem("Time Cards", Model.SideBarViewModel.IsTimeCardsSectionActive, 
                                        new List<HelperResult>[0], Url.Action("TimeCards", "Report"))
                                }

                                if (Model.SideBarViewModel.PTOEnabled)
                                {
                                    var subMenuItems = new List<HelperResult> { SubMenuItem(Url.Action("PTOCalendar", "PTO"), "Time Off Calendar") };
                                    if (!Model.SideBarViewModel.IsAdministrator && Model.SideBarViewModel.PTOAccrualRulesAllowed)
                                    {
                                        subMenuItems.Add(SubMenuItem(Url.Action("EmployeePTOSummary", "Employee"), "Time Off Summary"));
                                    }

                                    if (subMenuItems.Count > 1)
                                    {
                                        @MainMenuItem("Time Off", Model.SideBarViewModel.IsTimeOffActive, subMenuItems, "javascript:;")
                                    }
                                    else
                                    {
                                        @MainMenuItem("Time Off", Model.SideBarViewModel.IsTimeOffActive, Array.Empty<List<HelperResult>>(), Url.Action("PTOCalendar", "PTO"));
                                    }
                                }

                                if (Model.SideBarViewModel.IsScheduleVisible)
                                {
                                    if (Model.SideBarViewModel.IsAvailabilityVisible || Model.SideBarViewModel.IsShiftRequestVisible)
                                    {
                                        HtmlString scheduleBadge = null;
                                        var subMenuItems = new List<HelperResult>{
                                            SubMenuItem(Url.Action("Index", "Schedule"), "View Schedule"),
                                        };
                                        if (Model.SideBarViewModel.IsShiftRequestVisible)
                                        {
                                            if (Model.SideBarViewModel.ShiftTradePendingActionCount > 0)
                                            {
                                                scheduleBadge = new HtmlString(string.Format("<span class=\"m-badge m-badge--warning\">{0}</span>", Model.SideBarViewModel.ShiftTradePendingActionCount));
                                                subMenuItems.Add(SubMenuItem(Url.Action("Index", "ShiftTrade"), "Shift Requests", scheduleBadge));
                                            }
                                            else
                                            {
                                                subMenuItems.Add(SubMenuItem(Url.Action("Index", "ShiftTrade"), "Shift Requests"));
                                            }
                                        }
                                        if (Model.SideBarViewModel.IsAvailabilityVisible)
                                        {
                                            subMenuItems.Add(SubMenuItem(Url.Action("Availability", "Schedule"), "Availability"));
                                        }

                                        @MainMenuItem("Schedule", Model.SideBarViewModel.IsScheduleSectionActive, subMenuItems, "javascript:;", scheduleBadge)
                                    }
                                    else
                                    {
                                        @MainMenuItem("Schedule", Model.SideBarViewModel.IsScheduleSectionActive, new List<HelperResult>[0], Url.Action("Index", "Schedule"))
                                    }
                                }
                                if (Model.SideBarViewModel.IsEmployeesVisible)
                                {
                                    @MainMenuItem("Employees", Model.SideBarViewModel.IsEmployeesSectionActive, new List<HelperResult>[0], Url.Action("Index", "Employee"))
                                }

                                if (Model.SideBarViewModel.IsPayrollVisible)
                                {
                                    if (Model.SideBarViewModel.IsAdministrator)
                                    {
                                        var payrollItems = new List<HelperResult>();

                                        payrollItems.Add(
                                            SubMenuItem(Url.Action("Company", "Payroll"), "Company Details"));
                                        payrollItems.Add(
                                            SubMenuItem(Url.Action("PeopleList", "Payroll"), "People"));

                                        if (Model.SideBarViewModel.IsPayrollFeatureOn)
                                        {
                                            payrollItems.Add(
                                                SubMenuItem(Url.Action("Payrolls", "Payroll"), "View Payrolls"));
                                            payrollItems.Add(
                                                SubMenuItem(Url.Action("TaxDeposits", "Payroll"), "Tax Deposits"));
                                            payrollItems.Add(
                                                                SubMenuItemPayrollDocuments("Documents"));
                                            payrollItems.Add(
                                                SubMenuItemPayrollReports("Reports"));
                                            payrollItems.Add(SubMenuItem(Url.Action("PreparePayroll", "Payroll"), "Run Payroll"));
                                        }

                                        @MainMenuItem("Payroll", Model.SideBarViewModel.IsPayrollSectionActive,
                                            payrollItems
                                            , "javascript:;")
                                    }
                                    else if (Model.SideBarViewModel.IsPaystubsVisible)
                                    {
                                        @MainMenuItem("Paystubs", Model.SideBarViewModel.IsPayrollSectionActive, new List<HelperResult>[0], Url.Action("Paystubs", "MyPayroll"))
                                    }
                                }
                                if (Model.SideBarViewModel.IsOnboardingVisible)
                                {
                                    @MainMenuItem("Payroll Setup", Model.SideBarViewModel.IsOnboardingSectionActive, new List<HelperResult>[0], Url.Action("Onboard", "MyPayroll"))
                                }
                                if (Model.SideBarViewModel.IsReportsVisible)
                                {
                                    var reports = new List<HelperResult>();

                                    reports.Add(SubMenuItemReportBy("Hours Report By"));
                                    reports.Add(SubMenuItem(Url.Action("SummaryHours", "Report"), "Hours Summary"));
                                    reports.Add(SubMenuItem(Url.Action("DailyHours", "Report"), "Daily Hours"));
                                    reports.Add(SubMenuItem(Url.Action("InOutActivity", "Report"), "In/Out Activity"));
                                    reports.Add(SubMenuItem(Url.Action("PTOSummary", "Report"), "PTO Summary"));

                                    if (Model.SideBarViewModel.EnableInOutDetailReport)
                                    {
                                        reports.Add(SubMenuItem(Url.Action("InOutDetailReport", "Report"), "Employee Detail"));
                                    }

                                    reports.Add(SubMenuItem(Url.Action("TimeCardReport", "Report"), "Time Card Report"));
                                    reports.Add(SubMenuItem(Url.Action("DeleteReport", "Report"), "Deleted Time Report"));
                                    reports.Add(SubMenuItem(Url.Action("PayrollExport", "Report"), "Payroll Export"));

                                    if (Model.SideBarViewModel.IsScheduleVisible)
                                    {
                                        reports.Add(SubMenuItemScheduling("Scheduling"));
                                    }

                                    reports.Add(SubMenuItem(Url.Action("EmployeeErrorLog", "Report"), "Employee Error Log"));

                                    if (Model.SideBarViewModel.AccountId == Constants.Veeva_AccountId)
                                    {
                                        reports.Add(SubMenuItem(Url.Action("Veeva", "Report"), "Veeva Custom"));
                                    }

                                    if (alwaysOnGPSEnabled)
                                    {
                                        reports.Add(SubMenuItem(Url.Action("GPSActivity", "Report"), "GPS Activity"));
                                    }

                                    if (Model.SideBarViewModel.IsCustomReportsVisible)
                                    {
                                        reports.Add(SubMenuItem(Url.Action("CustomReports", "Report"), "Custom Reports"));
                                    }

                                    @MainMenuItem("Reports", Model.SideBarViewModel.IsReportsSectionActive, reports, "javascript:;")
                                }

                                if (Model.SideBarViewModel.IsSettingsVisible)
                                {
                                    var settings = new List<HelperResult>();
                                    settings.Add(SubMenuHeader("Basic", true));
                                    settings.Add(SubMenuItem("Account Settings", "Edit", "Account"));
                                    settings.Add(SubMenuItem("Administrators", "AdminList", "Account"));
                                    if (Model.SideBarViewModel.IsBillingVisible)
                                    {
                                        settings.Add(SubMenuItem("Billing", "ViewPlan", "Account"));
                                    }
                                    settings.Add(SubMenuItem("Integrations", "Index", "Integration"));
                                    settings.Add(SubMenuItem("Pay Periods", "Index", "PayPeriod"));

                                    if (Model.SideBarViewModel.IsPayrollLandingPageVisible && !Model.SideBarViewModel.IsPayrollVisible)
                                    {
                                        settings.Add(SubMenuItem("Payroll", "Intro", "Payroll"));
                                    }
                                    settings.Add(SubMenuItem("Scheduling", "Settings", "Schedule"));
                                    settings.Add(SubMenuItem("Time Entry Options", "TimeEntryOptions", "Account"));
                                    settings.Add(SubMenuItem("Time Off", "PTOSettings", "Account"));
                                    settings.Add(SubMenuItem("Webcam Settings", "WebcamSettings", "Account"));

                                    //br
                                    settings.Add(EmptyMenuItem());
                                    settings.Add(SubMenuItem("Locations", "Index", "Location"));
                                    settings.Add(SubMenuItem("Department Codes", "Index", "JobCode"));
                                    if (Model.SideBarViewModel.IsScheduleVisible)
                                    {
                                        settings.Add(SubMenuItem("Positions", "Index", "Position"));
                                    }
                                    //settings.Add(SubMenuItem("Groups", "Index", "Group"));
                                    //col 2
                                    var settings2 = new List<HelperResult>();
                                    settings2.Add(SubMenuHeader("Advanced", true));
                                    settings2.Add(SubMenuItem("Alerts & Reminders", "Index", "AlertRule", icon: "fas fa-bell"));
                                    settings2.Add(SubMenuItem("Break Rules", "Index", "BreakRule", icon: "fas fa-hourglass-start"));
                                    settings2.Add(SubMenuItem("Device Locks", "Index", "DeviceLock", icon: "fas fa-lock"));
                                    settings2.Add(SubMenuItem("Geofences", "Index", "Geofence", icon: "fas fa-globe"));
                                    settings2.Add(SubMenuItem("GPS Settings", "GpsSettings", "Account", icon: "fas fa-map-marker-alt"));
                                    settings2.Add(SubMenuItem("IP Address Locks", "Index", "IpAddressLock", icon: "fas fa-network-wired"));
                                    settings2.Add(SubMenuItem("Kiosk Settings", "KioskModeSettings", "Account", icon: "fas fa-tablet-alt"));
                                    settings2.Add(SubMenuItem("Overtime", "OvertimeSettings", "Account", icon: "fas fa-user-clock"));
                                    settings2.Add(SubMenuItem("Pay Rates", "PayRatesSettings", "Account", icon: "fas fa-dollar-sign"));

                                    settings2.Add(SubMenuItem("Punch Limiting", "Index", "PunchLimitRule", icon: "fas fa-stopwatch"));
                                    settings2.Add(SubMenuItem("Punch Rounding", "Index", "PunchRounding", icon: "fas fa-redo"));

                                    if (Model.SideBarViewModel.EnableQrCodeSettings)
                                    {
                                        settings2.Add(SubMenuItem("QR Code Settings", "QrCodeSettings", "Account", icon: "fas fa-qrcode"));
                                    }

                                    settings2.Add(SubMenuItem("Time Card Approvals Settings", "TimesheetApprovalSettings", "Account", icon: "fas fa-check-circle"));

                                    @MainMenuItem("Settings", Model.SideBarViewModel.IsSettingsSectionActive,
                                        new[] { settings, settings2 }, "javascript:;")
                                }
                            }
                            @if (Model.SideBarViewModel.IsPunchInOutVisible)
                            {
                                @MainMenuItem("Time Entry", Model.SideBarViewModel.IsPunchInOutSectionActive,
                                    new List<HelperResult>[] { }
                                    , Url.Action("Punch", "Employee"))
                            }
                            @if (Model.SideBarViewModel.IsSendMessageVisible)
                            {
                                <li class="m-menu__item @(Model.SideBarViewModel.IsMessageLogSectionActive  ? "m-menu__item--active" : "") m-menu__item--rel" aria-haspopup="true">
                                    <a href="javascript:;" class="m-menu__link m-menu__toggle"
                                       data-toggle="modal" data-target="#messageModal">
                                        <span class="m-menu__item-here"></span>
                                        <span class="m-menu__link-text d-lg-none d-xl-none">Send Message</span>
                                        <i class="m-menu__hor-arrow fa fa-envelope" style="font-size: 1.5em;"></i>
                                    </a>
                                </li>
                            }
                            else
                            {
                                <li class="m-menu__item  @(Model.SideBarViewModel.IsMessageLogSectionActive  ? "m-menu__item--active" : "") m-menu__item--rel" aria-haspopup="true">
                                    <a href="@Url.Action("Index", "MessageLog")" class="m-menu__link ">
                                        <span class="m-menu__item-here"></span>
                                        <span class="m-menu__link-text d-lg-none d-xl-none">Message Log</span>

                                        <i class="m-menu__hor-arrow fa fa-envelope" style="font-size: 1.5em;"></i>
                                    </a>
                                </li>
                            }
                        </ul>
                    </div>
                </div>
                <!-- end::Horizontal Menu -->
            </div>
        </div>
    </div>
</header>
