/*! tailwindcss v4.1.7 | MIT License | https://tailwindcss.com */
@layer properties;
@layer theme, base, components, utilities;
@layer theme {
  :root, :host {
    --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
      "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono",
      "Courier New", monospace;
    --color-red-50: oklch(97.1% 0.013 17.38);
    --color-red-100: oklch(93.6% 0.032 17.717);
    --color-red-600: oklch(57.7% 0.245 27.325);
    --color-red-700: oklch(50.5% 0.213 27.518);
    --color-red-800: oklch(44.4% 0.177 26.899);
    --color-red-950: oklch(25.8% 0.092 26.042);
    --color-yellow-50: oklch(98.7% 0.026 102.212);
    --color-yellow-100: oklch(97.3% 0.071 103.193);
    --color-yellow-200: oklch(94.5% 0.129 101.54);
    --color-yellow-400: oklch(85.2% 0.199 91.936);
    --color-yellow-500: oklch(79.5% 0.184 86.047);
    --color-yellow-600: oklch(68.1% 0.162 75.834);
    --color-yellow-700: oklch(55.4% 0.135 66.442);
    --color-yellow-800: oklch(47.6% 0.114 61.907);
    --color-yellow-950: oklch(28.6% 0.066 53.813);
    --color-green-50: oklch(98.2% 0.018 155.826);
    --color-green-100: oklch(96.2% 0.044 156.743);
    --color-green-200: oklch(92.5% 0.084 155.995);
    --color-green-500: oklch(72.3% 0.219 149.579);
    --color-green-600: oklch(62.7% 0.194 149.214);
    --color-green-700: oklch(52.7% 0.154 150.069);
    --color-green-800: oklch(44.8% 0.119 151.328);
    --color-green-950: oklch(26.6% 0.065 152.934);
    --color-blue-50: oklch(97% 0.014 254.604);
    --color-blue-100: oklch(93.2% 0.032 255.585);
    --color-blue-200: oklch(88.2% 0.059 254.128);
    --color-blue-300: oklch(80.9% 0.105 251.813);
    --color-blue-400: oklch(70.7% 0.165 254.624);
    --color-blue-500: oklch(62.3% 0.214 259.815);
    --color-blue-600: oklch(54.6% 0.245 262.881);
    --color-blue-700: oklch(48.8% 0.243 264.376);
    --color-blue-800: oklch(42.4% 0.199 265.638);
    --color-blue-950: oklch(28.2% 0.091 267.935);
    --color-violet-50: oklch(96.9% 0.016 293.756);
    --color-violet-100: oklch(94.3% 0.029 294.588);
    --color-violet-200: oklch(89.4% 0.057 293.283);
    --color-violet-500: oklch(60.6% 0.25 292.717);
    --color-violet-600: oklch(54.1% 0.281 293.009);
    --color-violet-700: oklch(49.1% 0.27 292.581);
    --color-violet-800: oklch(43.2% 0.232 292.759);
    --color-violet-950: oklch(28.3% 0.141 291.089);
    --color-pink-500: oklch(65.6% 0.241 354.308);
    --color-gray-50: oklch(98.5% 0.002 247.839);
    --color-gray-100: oklch(96.7% 0.003 264.542);
    --color-gray-200: oklch(92.8% 0.006 264.531);
    --color-gray-300: oklch(87.2% 0.01 258.338);
    --color-gray-400: oklch(70.7% 0.022 261.325);
    --color-gray-500: oklch(55.1% 0.027 264.364);
    --color-gray-600: oklch(44.6% 0.03 256.802);
    --color-gray-700: oklch(37.3% 0.034 259.733);
    --color-gray-800: oklch(27.8% 0.033 256.848);
    --color-gray-900: oklch(21% 0.034 264.665);
    --color-zinc-50: oklch(98.5% 0 0);
    --color-zinc-100: oklch(96.7% 0.001 286.375);
    --color-zinc-200: oklch(92% 0.004 286.32);
    --color-zinc-300: oklch(87.1% 0.006 286.286);
    --color-zinc-400: oklch(70.5% 0.015 286.067);
    --color-zinc-500: oklch(55.2% 0.016 285.938);
    --color-zinc-600: oklch(44.2% 0.017 285.786);
    --color-zinc-800: oklch(27.4% 0.006 286.033);
    --color-zinc-900: oklch(21% 0.006 285.885);
    --color-zinc-950: oklch(14.1% 0.005 285.823);
    --color-neutral-200: oklch(92.2% 0 0);
    --color-neutral-700: oklch(37.1% 0 0);
    --color-black: #000;
    --color-white: #fff;
    --spacing: 0.25rem;
    --container-sm: 24rem;
    --container-md: 28rem;
    --text-xs: 0.75rem;
    --text-xs--line-height: calc(1 / 0.75);
    --text-sm: 0.875rem;
    --text-sm--line-height: calc(1.25 / 0.875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --tracking-tight: -0.025em;
    --leading-relaxed: 1.625;
    --radius-sm: calc(var(--radius) - 4px);
    --radius-md: calc(var(--radius) - 2px);
    --radius-lg: var(--radius);
    --radius-xl: calc(var(--radius) + 4px);
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
    --animate-spin: spin 1s linear infinite;
    --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    --default-transition-duration: 150ms;
    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);
    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);
    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);
    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);
    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);
    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);
    --color-destructive: var(--destructive);
    --color-destructive-foreground: var(--destructive-foreground);
    --color-mono: var(--mono);
    --color-mono-foreground: var(--mono-foreground);
    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);
  }
}
@layer base {
  *, ::after, ::before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0 solid;
  }
  html, :host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }
  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }
  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }
  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }
  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }
  b, strong {
    font-weight: bolder;
  }
  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }
  small {
    font-size: 80%;
  }
  sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }
  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }
  :-moz-focusring {
    outline: auto;
  }
  progress {
    vertical-align: baseline;
  }
  summary {
    display: list-item;
  }
  ol, ul, menu {
    list-style: none;
  }
  img, svg, video, canvas, audio, iframe, embed, object {
    display: block;
    vertical-align: middle;
  }
  img, video {
    max-width: 100%;
    height: auto;
  }
  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    border-radius: 0;
    background-color: transparent;
    opacity: 1;
  }
  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }
  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }
  ::file-selector-button {
    margin-inline-end: 4px;
  }
  ::placeholder {
    opacity: 1;
  }
  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentcolor;
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }
  textarea {
    resize: vertical;
  }
  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }
  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }
  ::-webkit-datetime-edit {
    display: inline-flex;
  }
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }
  :-moz-ui-invalid {
    box-shadow: none;
  }
  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }
  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }
  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}
@layer utilities {
  .collapse {
    visibility: collapse;
  }
  .visible {
    visibility: visible;
  }
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
  }
  .absolute {
    position: absolute;
  }
  .fixed {
    position: fixed;
  }
  .relative {
    position: relative;
  }
  .static {
    position: static;
  }
  .sticky {
    position: sticky;
  }
  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }
  .top-1\/2 {
    top: calc(1/2 * 100%);
  }
  .top-3 {
    top: calc(var(--spacing) * 3);
  }
  .right-2 {
    right: calc(var(--spacing) * 2);
  }
  .right-3 {
    right: calc(var(--spacing) * 3);
  }
  .left-1\/2 {
    left: calc(1/2 * 100%);
  }
  .z-50 {
    z-index: 50;
  }
  .container {
    width: 100%;
    @media (width >= 40rem) {
      max-width: 40rem;
    }
    @media (width >= 48rem) {
      max-width: 48rem;
    }
    @media (width >= 64rem) {
      max-width: 64rem;
    }
    @media (width >= 80rem) {
      max-width: 80rem;
    }
    @media (width >= 96rem) {
      max-width: 96rem;
    }
  }
  .mx-0\.5 {
    margin-inline: calc(var(--spacing) * 0.5);
  }
  .mx-1 {
    margin-inline: calc(var(--spacing) * 1);
  }
  .mx-auto {
    margin-inline: auto;
  }
  .ms-auto {
    margin-inline-start: auto;
  }
  .mt-3 {
    margin-top: calc(var(--spacing) * 3);
  }
  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }
  .mt-10 {
    margin-top: calc(var(--spacing) * 10);
  }
  .mr-1 {
    margin-right: calc(var(--spacing) * 1);
  }
  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }
  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }
  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }
  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }
  .-ml-1 {
    margin-left: calc(var(--spacing) * -1);
  }
  .ml-1 {
    margin-left: calc(var(--spacing) * 1);
  }
  .block {
    display: block;
  }
  .contents {
    display: contents;
  }
  .flex {
    display: flex;
  }
  .grid {
    display: grid;
  }
  .hidden {
    display: none;
  }
  .inline {
    display: inline;
  }
  .table {
    display: table;
  }
  .size-3\.5 {
    width: calc(var(--spacing) * 3.5);
    height: calc(var(--spacing) * 3.5);
  }
  .size-4 {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }
  .size-5 {
    width: calc(var(--spacing) * 5);
    height: calc(var(--spacing) * 5);
  }
  .size-6 {
    width: calc(var(--spacing) * 6);
    height: calc(var(--spacing) * 6);
  }
  .h-5 {
    height: calc(var(--spacing) * 5);
  }
  .h-6 {
    height: calc(var(--spacing) * 6);
  }
  .h-8 {
    height: calc(var(--spacing) * 8);
  }
  .max-h-\[250px\] {
    max-height: 250px;
  }
  .min-h-screen {
    min-height: 100vh;
  }
  .w-5 {
    width: calc(var(--spacing) * 5);
  }
  .w-6 {
    width: calc(var(--spacing) * 6);
  }
  .w-7 {
    width: calc(var(--spacing) * 7);
  }
  .w-10 {
    width: calc(var(--spacing) * 10);
  }
  .w-12 {
    width: calc(var(--spacing) * 12);
  }
  .w-full {
    width: 100%;
  }
  .max-w-md {
    max-width: var(--container-md);
  }
  .max-w-sm {
    max-width: var(--container-sm);
  }
  .shrink-0 {
    flex-shrink: 0;
  }
  .flex-grow {
    flex-grow: 1;
  }
  .grow {
    flex-grow: 1;
  }
  .border-collapse {
    border-collapse: collapse;
  }
  .-translate-x-1\/2 {
    --tw-translate-x: calc(calc(1/2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .-translate-y-1\/2 {
    --tw-translate-y: calc(calc(1/2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .translate-y-0 {
    --tw-translate-y: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .translate-y-2 {
    --tw-translate-y: calc(var(--spacing) * 2);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .transform {
    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
  }
  .animate-spin {
    animation: var(--animate-spin);
  }
  .cursor-not-allowed {
    cursor: not-allowed;
  }
  .cursor-pointer {
    cursor: pointer;
  }
  .resize {
    resize: both;
  }
  .grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  .grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
  .flex-col {
    flex-direction: column;
  }
  .flex-wrap {
    flex-wrap: wrap;
  }
  .items-center {
    align-items: center;
  }
  .justify-between {
    justify-content: space-between;
  }
  .justify-center {
    justify-content: center;
  }
  .gap-0\.5 {
    gap: calc(var(--spacing) * 0.5);
  }
  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }
  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }
  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }
  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }
  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }
  .space-x-2 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .overflow-auto {
    overflow: auto;
  }
  .overflow-hidden {
    overflow: hidden;
  }
  .rounded {
    border-radius: 0.25rem;
  }
  .rounded-full {
    border-radius: calc(infinity * 1px);
  }
  .rounded-lg {
    border-radius: var(--radius);
  }
  .rounded-md {
    border-radius: calc(var(--radius) - 2px);
  }
  .rounded-sm {
    border-radius: calc(var(--radius) - 4px);
  }
  .rounded-t {
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0.25rem;
  }
  .rounded-b {
    border-bottom-right-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
  }
  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }
  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }
  .border-dashed {
    --tw-border-style: dashed;
    border-style: dashed;
  }
  .border-none {
    --tw-border-style: none;
    border-style: none;
  }
  .border-blue-500 {
    border-color: var(--color-blue-500);
  }
  .border-gray-200 {
    border-color: var(--color-gray-200);
  }
  .border-gray-300 {
    border-color: var(--color-gray-300);
  }
  .border-input {
    border-color: var(--input);
  }
  .border-primary {
    border-color: var(--primary);
  }
  .bg-black {
    background-color: var(--color-black);
  }
  .bg-blue-50 {
    background-color: var(--color-blue-50);
  }
  .bg-blue-100 {
    background-color: var(--color-blue-100);
  }
  .bg-blue-500 {
    background-color: var(--color-blue-500);
  }
  .bg-blue-600 {
    background-color: var(--color-blue-600);
  }
  .bg-gray-50 {
    background-color: var(--color-gray-50);
  }
  .bg-gray-100 {
    background-color: var(--color-gray-100);
  }
  .bg-gray-900 {
    background-color: var(--color-gray-900);
  }
  .bg-pink-500 {
    background-color: var(--color-pink-500);
  }
  .bg-transparent {
    background-color: transparent;
  }
  .bg-white {
    background-color: var(--color-white);
  }
  .p-0\.5 {
    padding: calc(var(--spacing) * 0.5);
  }
  .p-1 {
    padding: calc(var(--spacing) * 1);
  }
  .p-2 {
    padding: calc(var(--spacing) * 2);
  }
  .p-6 {
    padding: calc(var(--spacing) * 6);
  }
  .p-8 {
    padding: calc(var(--spacing) * 8);
  }
  .px-1 {
    padding-inline: calc(var(--spacing) * 1);
  }
  .px-1\.5 {
    padding-inline: calc(var(--spacing) * 1.5);
  }
  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }
  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }
  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }
  .py-0\.5 {
    padding-block: calc(var(--spacing) * 0.5);
  }
  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }
  .py-1\.5 {
    padding-block: calc(var(--spacing) * 1.5);
  }
  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }
  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }
  .ps-5 {
    padding-inline-start: calc(var(--spacing) * 5);
  }
  .pt-3 {
    padding-top: calc(var(--spacing) * 3);
  }
  .pb-3 {
    padding-bottom: calc(var(--spacing) * 3);
  }
  .pl-5 {
    padding-left: calc(var(--spacing) * 5);
  }
  .text-center {
    text-align: center;
  }
  .text-left {
    text-align: left;
  }
  .text-start {
    text-align: start;
  }
  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }
  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }
  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }
  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }
  .leading-none {
    --tw-leading: 1;
    line-height: 1;
  }
  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }
  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }
  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }
  .text-blue-600 {
    color: var(--color-blue-600);
  }
  .text-blue-800 {
    color: var(--color-blue-800);
  }
  .text-foreground {
    color: var(--foreground);
  }
  .text-gray-300 {
    color: var(--color-gray-300);
  }
  .text-gray-400 {
    color: var(--color-gray-400);
  }
  .text-gray-500 {
    color: var(--color-gray-500);
  }
  .text-gray-600 {
    color: var(--color-gray-600);
  }
  .text-gray-700 {
    color: var(--color-gray-700);
  }
  .text-gray-800 {
    color: var(--color-gray-800);
  }
  .text-gray-900 {
    color: var(--color-gray-900);
  }
  .text-muted-foreground {
    color: var(--muted-foreground);
  }
  .text-primary {
    color: var(--primary);
  }
  .text-white {
    color: var(--color-white);
  }
  .capitalize {
    text-transform: capitalize;
  }
  .uppercase {
    text-transform: uppercase;
  }
  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  .opacity-0 {
    opacity: 0%;
  }
  .opacity-25 {
    opacity: 25%;
  }
  .opacity-75 {
    opacity: 75%;
  }
  .opacity-100 {
    opacity: 100%;
  }
  .shadow {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .ring {
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .ring-blue-300 {
    --tw-ring-color: var(--color-blue-300);
  }
  .outline {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }
  .blur {
    --tw-blur: blur(8px);
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .invert {
    --tw-invert: invert(100%);
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .filter {
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .duration-300 {
    --tw-duration: 300ms;
    transition-duration: 300ms;
  }
  .hover\:bg-blue-50 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-blue-50);
      }
    }
  }
  .hover\:bg-blue-200 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-blue-200);
      }
    }
  }
  .hover\:bg-blue-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-blue-600);
      }
    }
  }
  .hover\:bg-blue-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-blue-700);
      }
    }
  }
  .hover\:bg-gray-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-100);
      }
    }
  }
  .hover\:bg-gray-200 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-200);
      }
    }
  }
  .hover\:text-blue-600 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-blue-600);
      }
    }
  }
  .hover\:text-gray-600 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-600);
      }
    }
  }
  .focus\:ring-2 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\:ring-blue-400 {
    &:focus {
      --tw-ring-color: var(--color-blue-400);
    }
  }
  .focus\:ring-blue-500 {
    &:focus {
      --tw-ring-color: var(--color-blue-500);
    }
  }
  .focus\:outline-none {
    &:focus {
      --tw-outline-style: none;
      outline-style: none;
    }
  }
  .rtl\:rotate-180 {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      rotate: 180deg;
    }
  }
  .rtl\:transform {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
    }
  }
  .rtl\:pr-5 {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      padding-right: calc(var(--spacing) * 5);
    }
  }
  .rtl\:text-right {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      text-align: right;
    }
  }
  .kt-select-option-selected\:block {
    [data-kt-select-option].selected {
      display: block;
    }
    [data-kt-select-option].selected & {
      display: block;
    }
  }
  .\[\&\>svg\]\:size-3\.5 {
    &>svg {
      width: calc(var(--spacing) * 3.5);
      height: calc(var(--spacing) * 3.5);
    }
  }
}
:root {
  --background: oklch(1 0 0);
  --foreground: oklch(14.1% 0.005 285.823);
  --card: oklch(1 0 0);
  --card-foreground: oklch(14.1% 0.005 285.823);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(14.1% 0.005 285.823);
  --primary: oklch(62.3% 0.214 259.815);
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(96.7% 0.003 264.542);
  --secondary-foreground: oklch(21% 0.006 285.885);
  --muted: oklch(96.7% 0.003 264.542);
  --muted-foreground: oklch(55.2% 0.016 285.938);
  --accent: oklch(96.7% 0.003 264.542);
  --accent-foreground: oklch(21% 0.006 285.885);
  --destructive: oklch(57.7% 0.245 27.325);
  --destructive-foreground: oklch(1 0 0);
  --mono: oklch(14.1% 0.005 285.823);
  --mono-foreground: oklch(1 0 0);
  --border: oklch(94% 0.004 286.32);
  --input: oklch(92% 0.004 286.32);
  --ring: oklch(87.1% 0.006 286.286);
  --radius: 0.5rem;
}
.dark {
  --background: oklch(14.1% 0.005 285.823);
  --foreground: oklch(98.5% 0 0);
  --card: oklch(14.1% 0.005 285.823);
  --card-foreground: oklch(98.5% 0 0);
  --popover: oklch(14.1% 0.005 285.823);
  --popover-foreground: oklch(98.5% 0 0);
  --primary: oklch(54.6% 0.245 262.881);
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(27.4% 0.006 286.033);
  --secondary-foreground: oklch(98.5% 0 0);
  --muted: oklch(21% 0.006 285.885);
  --muted-foreground: oklch(55.2% 0.016 285.938);
  --accent: oklch(21% 0.006 285.885);
  --accent-foreground: oklch(98.5% 0 0);
  --destructive: oklch(57.7% 0.245 27.325);
  --destructive-foreground: oklch(1 0 0);
  --mono: oklch(87.1% 0.006 286.286);
  --mono-foreground: oklch(0 0 0);
  --border: oklch(27.4% 0.006 286.033);
  --input: oklch(27.4% 0.006 286.033);
  --ring: oklch(27.4% 0.006 286.033);
}
@layer components {
  .kt-avatar {
    position: relative;
    display: flex;
    width: calc(var(--spacing) * 10);
    height: calc(var(--spacing) * 10);
    flex-shrink: 0;
  }
  .kt-avatar-image {
    overflow: hidden;
    border-radius: calc(infinity * 1px);
    img {
      aspect-ratio: 1 / 1;
      height: 100%;
      width: 100%;
    }
  }
  .kt-avatar-fallback {
    display: flex;
    height: 100%;
    width: 100%;
    align-items: center;
    justify-content: center;
    border-radius: calc(infinity * 1px);
    border-style: var(--tw-border-style);
    border-width: 1px;
    border-color: var(--border);
    background-color: var(--accent);
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }
  .kt-avatar-indicator {
    position: absolute;
    display: flex;
    width: calc(var(--spacing) * 6);
    height: calc(var(--spacing) * 6);
    align-items: center;
    justify-content: center;
  }
  .kt-avatar-status {
    display: flex;
    width: calc(var(--spacing) * 2);
    height: calc(var(--spacing) * 2);
    align-items: center;
    border-radius: calc(infinity * 1px);
    border-style: var(--tw-border-style);
    border-width: 2px;
    border-color: var(--background);
    &.kt-avatar-status-online {
      background-color: var(--color-green-600);
    }
    &.kt-avatar-status-offline {
      background-color: var(--mono);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--mono) 50%, transparent);
      }
    }
    &.kt-avatar-status-busy {
      background-color: var(--color-yellow-600);
    }
    &.kt-avatar-status-away {
      background-color: var(--color-blue-600);
    }
  }
}
@layer components {
  .kt-accordion-item {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
    border-color: var(--border);
    &:last-child {
      border-bottom-style: var(--tw-border-style);
      border-bottom-width: 0px;
    }
  }
  .kt-accordion-toggle {
    display: flex;
    width: 100%;
    cursor: pointer;
    align-items: center;
    justify-content: space-between;
    gap: calc(var(--spacing) * 2.5);
    padding-block: calc(var(--spacing) * 4);
    text-align: start;
  }
  .kt-accordion-title {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
    color: var(--mono);
  }
  .kt-accordion-content {
    overflow: hidden;
    transition-property: height;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .kt-accordion-wrapper {
    padding-bottom: calc(var(--spacing) * 4);
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
    color: var(--foreground);
  }
  .kt-accordion-indicator {
    display: inline-flex;
    width: calc(var(--spacing) * 3);
    height: calc(var(--spacing) * 3);
    align-items: center;
    color: var(--muted-foreground);
  }
  .kt-accordion-indicator-on {
    display: block;
    width: calc(var(--spacing) * 3.5);
    height: calc(var(--spacing) * 3.5);
    flex-shrink: 0;
    &[data-kt-accordion-item].active {
      display: none;
    }
    [data-kt-accordion-item].active > [data-kt-accordion-toggle] & {
      display: none;
    }
    [data-kt-accordion-item].active > [data-kt-accordion-toggle]& {
      display: none;
    }
  }
  .kt-accordion-indicator-off {
    display: none;
    width: calc(var(--spacing) * 3.5);
    height: calc(var(--spacing) * 3.5);
    flex-shrink: 0;
    &[data-kt-accordion-item].active {
      display: block;
    }
    [data-kt-accordion-item].active > [data-kt-accordion-toggle] & {
      display: block;
    }
    [data-kt-accordion-item].active > [data-kt-accordion-toggle]& {
      display: block;
    }
  }
  .kt-accordion.kt-accordion-outline {
    display: flex;
    flex-direction: column;
    gap: calc(var(--spacing) * 3.5);
    .kt-accordion-item {
      border-radius: var(--radius);
      border-style: var(--tw-border-style);
      border-width: 1px;
      border-color: var(--border);
    }
    .kt-accordion-content {
      border-top-style: var(--tw-border-style);
      border-top-width: 1px;
      border-color: var(--border);
    }
    .kt-accordion-toggle {
      padding: calc(var(--spacing) * 4);
    }
    .kt-accordion-wrapper {
      padding: calc(var(--spacing) * 4);
    }
  }
}
@layer components {
  .kt-accordion-menu {
    display: flex;
    width: 100%;
    flex-direction: column;
    row-gap: calc(var(--spacing) * 1);
  }
  .kt-accordion-menu-content {
    overflow: hidden;
    padding-inline-start: calc(var(--spacing) * 6);
    transition-property: height;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .kt-accordion-menu-sub {
    width: 100%;
    row-gap: calc(var(--spacing) * 1);
  }
  .kt-accordion-menu-item {
    width: 100%;
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .kt-accordion-menu-toggle {
    display: flex;
    width: 100%;
    align-items: center;
    column-gap: calc(var(--spacing) * 2.5);
    border-radius: calc(var(--radius) - 2px);
    padding-inline: calc(var(--spacing) * 2.5);
    padding-block: calc(var(--spacing) * 2);
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
    color: var(--foreground);
    &:hover {
      @media (hover: hover) {
        background-color: var(--accent);
      }
    }
    &:disabled {
      pointer-events: none;
    }
    &:disabled {
      opacity: 50%;
    }
    &[data-kt-accordion-item].active {
      background-color: var(--accent);
    }
    [data-kt-accordion-item].active > [data-kt-accordion-toggle] & {
      background-color: var(--accent);
    }
    [data-kt-accordion-item].active > [data-kt-accordion-toggle]& {
      background-color: var(--accent);
    }
    svg {
      width: calc(var(--spacing) * 4);
      height: calc(var(--spacing) * 4);
      flex-shrink: 0;
      color: var(--muted-foreground);
    }
  }
  .kt-accordion-menu-link {
    display: flex;
    width: 100%;
    align-items: center;
    column-gap: calc(var(--spacing) * 2.5);
    border-radius: calc(var(--radius) - 2px);
    padding-inline: calc(var(--spacing) * 2.5);
    padding-block: calc(var(--spacing) * 2);
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
    color: var(--foreground);
    &:hover {
      @media (hover: hover) {
        background-color: var(--accent);
      }
    }
    &:disabled {
      pointer-events: none;
    }
    &:disabled {
      opacity: 50%;
    }
    [data-kt-accordion-initialized] .selected {
      background-color: var(--accent);
    }
    [data-kt-accordion-initialized] .selected & {
      background-color: var(--accent);
    }
    [data-kt-accordion-initialized] &.selected {
      background-color: var(--accent);
    }
    svg {
      width: calc(var(--spacing) * 4);
      height: calc(var(--spacing) * 4);
      flex-shrink: 0;
      color: var(--muted-foreground);
    }
  }
  .kt-accordion-menu-indicator {
    margin-inline-start: auto;
    width: calc(var(--spacing) * 3.5);
    height: calc(var(--spacing) * 3.5);
    flex-shrink: 0;
    color: var(--muted-foreground);
    &[data-kt-accordion-item].active {
      rotate: 180deg;
    }
    [data-kt-accordion-item].active > [data-kt-accordion-toggle] & {
      rotate: 180deg;
    }
    [data-kt-accordion-item].active > [data-kt-accordion-toggle]& {
      rotate: 180deg;
    }
  }
}
@layer components {
  [dir='rtl'] .kt-accordion-menu-indicator {
    &[data-kt-accordion-item].active {
      rotate: calc(180deg * -1);
    }
    [data-kt-accordion-item].active > [data-kt-accordion-toggle] & {
      rotate: calc(180deg * -1);
    }
    [data-kt-accordion-item].active > [data-kt-accordion-toggle]& {
      rotate: calc(180deg * -1);
    }
  }
}
@layer components {
  .kt-alert {
    display: flex;
    width: 100%;
    align-items: stretch;
  }
  .kt-alert-title {
    flex-grow: 1;
    --tw-tracking: var(--tracking-tight);
    letter-spacing: var(--tracking-tight);
  }
  .kt-alert-toolbar {
    display: flex;
    align-items: baseline;
    gap: calc(var(--spacing) * 2.5);
  }
  .kt-alert-actions {
    display: flex;
    align-items: center;
    gap: calc(var(--spacing) * 2);
  }
  .kt-alert-description {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    & p {
      margin-bottom: calc(var(--spacing) * 2);
    }
    & p {
      --tw-leading: var(--leading-relaxed);
      line-height: var(--leading-relaxed);
    }
  }
  .kt-alert-content {
    width: 100%;
    flex-grow: 1;
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 1.5) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 1.5) * calc(1 - var(--tw-space-y-reverse)));
    }
    .kt-alert-title {
      --tw-font-weight: var(--font-weight-semibold);
      font-weight: var(--font-weight-semibold);
    }
  }
  .kt-alert-icon {
    flex-shrink: 0;
  }
  .kt-alert-close {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
    flex-shrink: 0;
    cursor: pointer;
    i {
      font-size: var(--text-xs);
      line-height: var(--tw-leading, var(--text-xs--line-height));
      color: var(--muted-foreground);
    }
    > svg {
      width: calc(var(--spacing) * 4);
      height: calc(var(--spacing) * 4);
      color: var(--muted-foreground);
    }
    &:focus,	&:hover {
      i,	> svg {
        color: var(--foreground);
      }
    }
  }
  .kt-alert {
    gap: calc(var(--spacing) * 2.5);
    border-radius: var(--radius);
    padding: calc(var(--spacing) * 3.5);
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    .kt-alert-icon svg {
      width: calc(var(--spacing) * 5);
      height: calc(var(--spacing) * 5);
    }
    .kt-alert-toolbar {
      padding-top: calc(var(--spacing) * 0.25);
    }
  }
  .kt-alert-sm {
    gap: calc(var(--spacing) * 1.5);
    border-radius: calc(var(--radius) - 2px);
    padding-inline: calc(var(--spacing) * 2.5);
    padding-block: calc(var(--spacing) * 2);
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
    .kt-alert-close {
      width: calc(var(--spacing) * 3.5);
      height: calc(var(--spacing) * 3.5);
      > svg {
        width: calc(var(--spacing) * 3.5);
        height: calc(var(--spacing) * 3.5);
      }
    }
    .kt-alert-icon svg {
      width: calc(var(--spacing) * 4);
      height: calc(var(--spacing) * 4);
    }
  }
  .kt-alert-lg {
    gap: calc(var(--spacing) * 2.5);
    border-radius: calc(var(--radius) - 2px);
    padding: calc(var(--spacing) * 4);
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
    .kt-alert-icon svg {
      width: calc(var(--spacing) * 6);
      height: calc(var(--spacing) * 6);
    }
    .kt-alert-toolbar {
      padding-top: calc(var(--spacing) * 0.75);
    }
  }
  .kt-alert {
    background-color: var(--muted);
    color: var(--foreground);
  }
  .kt-alert-primary {
    background-color: var(--primary);
    color: var(--primary-foreground);
    .kt-alert-close > svg {
      color: var(--primary-foreground);
    }
  }
  .kt-alert-destructive {
    background-color: var(--destructive);
    color: var(--destructive-foreground);
    .kt-alert-close > svg {
      color: var(--destructive-foreground);
    }
  }
  .kt-alert-success {
    background-color: var(--color-green-500);
    color: var(--color-white);
    .kt-alert-close > svg {
      color: var(--color-white);
    }
  }
  .kt-alert-info {
    background-color: var(--color-violet-600);
    color: var(--color-white);
    .kt-alert-close > svg {
      color: var(--color-white);
    }
  }
  .kt-alert-warning {
    background-color: var(--color-yellow-500);
    color: var(--color-white);
    .kt-alert-close > svg {
      color: var(--color-white);
    }
  }
  .kt-alert-mono {
    background-color: var(--mono);
    color: var(--mono-foreground);
    .kt-alert-close > svg {
      color: var(--mono-foreground);
    }
  }
  .kt-alert-mono {
    &.kt-alert-primary {
      .kt-alert-icon {
        color: var(--primary);
      }
    }
    &.kt-alert-success {
      .kt-alert-icon {
        color: var(--color-green-500);
      }
    }
    &.kt-alert-destructive {
      .kt-alert-icon {
        color: var(--destructive);
      }
    }
    &.kt-alert-warning {
      .kt-alert-icon {
        color: var(--color-yellow-500);
      }
    }
    &.kt-alert-info {
      .kt-alert-icon {
        color: var(--color-violet-600);
      }
    }
  }
  .kt-alert-outline {
    .kt-alert-close {
      width: calc(var(--spacing) * 4);
      height: calc(var(--spacing) * 4);
      flex-shrink: 0;
      cursor: pointer;
      > svg {
        width: calc(var(--spacing) * 4);
        height: calc(var(--spacing) * 4);
        color: var(--muted-foreground);
      }
      &:focus,	&:hover {
        > svg {
          color: var(--foreground);
        }
      }
    }
    border-style: var(--tw-border-style);
    border-width: 1px;
    border-color: var(--border);
    background-color: var(--background);
    color: var(--foreground);
    &.kt-alert-primary {
      border-style: var(--tw-border-style);
      border-width: 1px;
      border-color: var(--border);
      background-color: var(--background);
      color: var(--primary);
    }
    &.kt-alert-destructive {
      border-style: var(--tw-border-style);
      border-width: 1px;
      border-color: var(--border);
      background-color: var(--background);
      color: var(--destructive);
    }
    &.kt-alert-success {
      border-style: var(--tw-border-style);
      border-width: 1px;
      border-color: var(--border);
      background-color: var(--background);
      color: var(--color-green-500);
    }
    &.kt-alert-info {
      border-style: var(--tw-border-style);
      border-width: 1px;
      border-color: var(--border);
      background-color: var(--background);
      color: var(--color-violet-600);
    }
    &.kt-alert-warning {
      border-style: var(--tw-border-style);
      border-width: 1px;
      border-color: var(--border);
      background-color: var(--background);
      color: var(--color-yellow-500);
    }
    &.kt-alert-mono {
      border-style: var(--tw-border-style);
      border-width: 1px;
      border-color: var(--border);
      background-color: var(--background);
      color: var(--mono);
    }
  }
  .kt-alert-light {
    border-style: var(--tw-border-style);
    border-width: 1px;
    border-color: var(--border);
    background-color: var(--muted);
    color: var(--foreground);
    .kt-alert-icon > svg {
      color: var(--muted-foreground);
    }
    .kt-alert-close {
      width: calc(var(--spacing) * 4);
      height: calc(var(--spacing) * 4);
      flex-shrink: 0;
      cursor: pointer;
      > svg {
        width: calc(var(--spacing) * 4);
        height: calc(var(--spacing) * 4);
        color: var(--muted-foreground);
      }
      &:focus,	&:hover {
        > svg {
          color: var(--foreground);
        }
      }
    }
    &.kt-alert-primary {
      border-style: var(--tw-border-style);
      border-width: 1px;
      border-color: var(--primary);
      @supports (color: color-mix(in lab, red, red)) {
        border-color: color-mix(in oklab, var(--primary) 10%, transparent);
      }
      background-color: var(--primary);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--primary) 5%, transparent);
      }
      color: var(--foreground);
      .kt-alert-icon > svg {
        color: var(--primary);
      }
    }
    &.kt-alert-destructive {
      border-style: var(--tw-border-style);
      border-width: 1px;
      border-color: var(--destructive);
      @supports (color: color-mix(in lab, red, red)) {
        border-color: color-mix(in oklab, var(--destructive) 10%, transparent);
      }
      background-color: var(--destructive);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--destructive) 5%, transparent);
      }
      color: var(--foreground);
      .kt-alert-icon > svg {
        color: var(--destructive);
      }
    }
    &.kt-alert-success {
      border-style: var(--tw-border-style);
      border-width: 1px;
      border-color: var(--color-green-200);
      background-color: var(--color-green-50);
      color: var(--foreground);
      &:is(.dark *) {
        border-color: color-mix(in srgb, oklch(26.6% 0.065 152.934) 50%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          border-color: color-mix(in oklab, var(--color-green-950) 50%, transparent);
        }
      }
      &:is(.dark *) {
        background-color: color-mix(in srgb, oklch(26.6% 0.065 152.934) 30%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-green-950) 30%, transparent);
        }
      }
      .kt-alert-icon > svg {
        color: var(--color-green-500);
      }
    }
    &.kt-alert-info {
      border-style: var(--tw-border-style);
      border-width: 1px;
      border-color: var(--color-violet-200);
      background-color: var(--color-violet-50);
      color: var(--foreground);
      &:is(.dark *) {
        border-color: color-mix(in srgb, oklch(28.3% 0.141 291.089) 50%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          border-color: color-mix(in oklab, var(--color-violet-950) 50%, transparent);
        }
      }
      &:is(.dark *) {
        background-color: color-mix(in srgb, oklch(28.3% 0.141 291.089) 30%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-violet-950) 30%, transparent);
        }
      }
      .kt-alert-icon > svg {
        color: var(--color-violet-500);
      }
    }
    &.kt-alert-warning {
      border-style: var(--tw-border-style);
      border-width: 1px;
      border-color: var(--color-yellow-200);
      background-color: var(--color-yellow-50);
      color: var(--foreground);
      &:is(.dark *) {
        border-color: color-mix(in srgb, oklch(28.6% 0.066 53.813) 50%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          border-color: color-mix(in oklab, var(--color-yellow-950) 50%, transparent);
        }
      }
      &:is(.dark *) {
        background-color: color-mix(in srgb, oklch(28.6% 0.066 53.813) 30%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-yellow-950) 30%, transparent);
        }
      }
      .kt-alert-icon > svg {
        color: var(--color-yellow-500);
      }
    }
  }
}
@layer components {
  .kt-form {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .kt-form-item {
    display: flex;
    flex-direction: column;
    gap: calc(var(--spacing) * 2.5);
  }
  .kt-form-control {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 2.5) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 2.5) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .kt-form-control-inline {
    display: flex;
    align-items: center;
    gap: calc(var(--spacing) * 2.5);
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 0) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .kt-form-label {
    display: flex;
    width: 100%;
    align-items: center;
    gap: calc(var(--spacing) * 2);
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    --tw-leading: 1;
    line-height: 1;
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
    color: var(--foreground);
    &:is(:where(.peer):disabled ~ *) {
      cursor: not-allowed;
    }
    &:is(:where(.peer):disabled ~ *) {
      opacity: 50%;
    }
    .kt-switch:disabled + &,	.kt-checkbox:disabled + &,	.kt-radio:disabled + & {
      cursor: not-allowed;
      opacity: 50%;
    }
  }
  .kt-form-description {
    margin-top: calc(var(--spacing) * -1);
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
    --tw-font-weight: var(--font-weight-normal);
    font-weight: var(--font-weight-normal);
    color: var(--secondary-foreground);
  }
  .kt-form-message {
    margin-top: calc(var(--spacing) * -1);
    display: none;
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
    --tw-font-weight: var(--font-weight-normal);
    font-weight: var(--font-weight-normal);
    color: var(--destructive);
  }
  .kt-form-actions {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: calc(var(--spacing) * 2.5);
  }
  .kt-form-item:has([aria-invalid='true']) {
    .kt-form-message {
      display: block;
    }
    .kt-form-description {
      display: none;
    }
  }
}
@layer components {
  .kt-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: var(--secondary);
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
    white-space: nowrap;
    color: var(--accent-foreground);
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
    &:focus {
      --tw-ring-color: var(--ring);
    }
    &:focus {
      --tw-ring-offset-width: 2px;
      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    }
    &:focus {
      --tw-outline-style: none;
      outline-style: none;
      @media (forced-colors: active) {
        outline: 2px solid transparent;
        outline-offset: 2px;
      }
    }
    & svg {
      margin-inline-start: -1px;
    }
    & svg {
      flex-shrink: 0;
    }
  }
  .kt-badge-primary {
    background-color: var(--primary);
    color: var(--primary-foreground);
  }
  .kt-badge-secondary {
    background-color: var(--secondary);
    color: var(--accent-foreground);
  }
  .kt-badge-success {
    background-color: var(--color-green-500);
    color: var(--color-white);
  }
  .kt-badge-warning {
    background-color: var(--color-yellow-400);
    color: var(--color-white);
  }
  .kt-badge-info {
    background-color: var(--color-violet-500);
    color: var(--color-white);
  }
  .kt-badge-mono {
    background-color: var(--mono);
    color: var(--mono-foreground);
  }
  .kt-badge-destructive {
    background-color: var(--destructive);
    color: var(--destructive-foreground);
  }
  .kt-badge-stroke {
    border-style: var(--tw-border-style);
    border-width: 1px;
    border-color: var(--border);
    background-color: var(--background);
    color: var(--secondary-foreground);
  }
  .kt-badge-disabled {
    pointer-events: none;
    opacity: 50%;
  }
  .kt-badge-lg {
    height: calc(var(--spacing) * 7);
    min-width: calc(var(--spacing) * 7);
    gap: calc(var(--spacing) * 1.5);
    border-radius: calc(var(--radius) - 2px);
    padding-inline: 0.5rem;
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
    & svg {
      width: calc(var(--spacing) * 3.5);
      height: calc(var(--spacing) * 3.5);
    }
  }
  .kt-badge {
    height: calc(var(--spacing) * 6);
    min-width: calc(var(--spacing) * 6);
    gap: calc(var(--spacing) * 1.5);
    border-radius: calc(var(--radius) - 2px);
    padding-inline: 0.45rem;
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
    & svg {
      width: calc(var(--spacing) * 3.5);
      height: calc(var(--spacing) * 3.5);
    }
  }
  .kt-badge-sm {
    height: calc(var(--spacing) * 5);
    min-width: calc(var(--spacing) * 5);
    gap: calc(var(--spacing) * 1);
    border-radius: calc(var(--radius) - 4px);
    padding-inline: 0.325rem;
    font-size: 0.6875rem;
    --tw-leading: 0.75rem;
    line-height: 0.75rem;
    & svg {
      width: calc(var(--spacing) * 3);
      height: calc(var(--spacing) * 3);
    }
  }
  .kt-badge-xs {
    height: calc(var(--spacing) * 4);
    min-width: calc(var(--spacing) * 4);
    gap: calc(var(--spacing) * 1);
    border-radius: calc(var(--radius) - 4px);
    padding-inline: 0.25rem;
    font-size: 0.625rem;
    --tw-leading: 0.5rem;
    line-height: 0.5rem;
    & svg {
      width: calc(var(--spacing) * 3);
      height: calc(var(--spacing) * 3);
    }
  }
  .kt-badge-outline {
    border-style: var(--tw-border-style);
    border-width: 1px;
    border-color: var(--border);
    background-color: var(--muted);
    color: var(--secondary-foreground);
    &.kt-badge-primary {
      border-style: var(--tw-border-style);
      border-width: 1px;
      border-color: var(--color-blue-100);
      background-color: var(--color-blue-50);
      color: var(--color-blue-700);
      &:is(.dark *) {
        border-color: var(--color-blue-950);
      }
      &:is(.dark *) {
        background-color: color-mix(in srgb, oklch(28.2% 0.091 267.935) 50%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-blue-950) 50%, transparent);
        }
      }
      &:is(.dark *) {
        color: var(--color-blue-600);
      }
    }
    &.kt-badge-secondary {
      border-color: var(--border);
      background-color: var(--secondary);
      color: var(--foreground);
      &:is(.dark *) {
        background-color: var(--secondary);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--secondary) 50%, transparent);
        }
      }
    }
    &.kt-badge-success {
      border-style: var(--tw-border-style);
      border-width: 1px;
      border-color: var(--color-green-200);
      background-color: var(--color-green-50);
      color: var(--color-green-700);
      &:is(.dark *) {
        border-color: var(--color-green-950);
      }
      &:is(.dark *) {
        background-color: color-mix(in srgb, oklch(26.6% 0.065 152.934) 50%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-green-950) 50%, transparent);
        }
      }
      &:is(.dark *) {
        color: var(--color-green-600);
      }
    }
    &.kt-badge-warning.kt-badge-outline {
      border-style: var(--tw-border-style);
      border-width: 1px;
      border-color: var(--color-yellow-100);
      background-color: var(--color-yellow-50);
      color: var(--color-yellow-700);
      &:is(.dark *) {
        border-color: var(--color-yellow-950);
      }
      &:is(.dark *) {
        background-color: color-mix(in srgb, oklch(28.6% 0.066 53.813) 50%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-yellow-950) 50%, transparent);
        }
      }
      &:is(.dark *) {
        color: var(--color-yellow-600);
      }
    }
    &.kt-badge-info.kt-badge-outline {
      border-style: var(--tw-border-style);
      border-width: 1px;
      border-color: var(--color-violet-100);
      background-color: var(--color-violet-50);
      color: var(--color-violet-700);
      &:is(.dark *) {
        border-color: var(--color-violet-950);
      }
      &:is(.dark *) {
        background-color: color-mix(in srgb, oklch(28.3% 0.141 291.089) 50%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-violet-950) 50%, transparent);
        }
      }
      &:is(.dark *) {
        color: var(--color-violet-600);
      }
    }
    &.kt-badge-mono.kt-badge-outline {
      border-color: var(--mono);
      @supports (color: color-mix(in lab, red, red)) {
        border-color: color-mix(in oklab, var(--mono) 10%, transparent);
      }
      background-color: var(--mono);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--mono) 10%, transparent);
      }
      color: var(--mono);
    }
    &.kt-badge-destructive.kt-badge-outline {
      border-style: var(--tw-border-style);
      border-width: 1px;
      border-color: var(--color-red-100);
      background-color: var(--color-red-50);
      color: var(--color-red-700);
      &:is(.dark *) {
        border-color: var(--color-red-950);
      }
      &:is(.dark *) {
        background-color: color-mix(in srgb, oklch(25.8% 0.092 26.042) 50%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-red-950) 50%, transparent);
        }
      }
      &:is(.dark *) {
        color: var(--color-red-600);
      }
    }
  }
  .kt-badge-light {
    &.kt-badge-primary {
      background-color: var(--color-blue-100);
      color: var(--color-blue-800);
      &:is(.dark *) {
        background-color: color-mix(in srgb, oklch(28.2% 0.091 267.935) 50%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-blue-950) 50%, transparent);
        }
      }
      &:is(.dark *) {
        color: var(--color-blue-600);
      }
    }
    &.kt-badge-secondary {
      background-color: var(--secondary);
      color: var(--secondary-foreground);
      &:is(.dark *) {
        background-color: var(--secondary);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--secondary) 50%, transparent);
        }
      }
    }
    &.kt-badge-success {
      background-color: var(--color-green-100);
      color: var(--color-green-800);
      &:is(.dark *) {
        background-color: color-mix(in srgb, oklch(26.6% 0.065 152.934) 50%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-green-950) 50%, transparent);
        }
      }
      &:is(.dark *) {
        color: var(--color-green-600);
      }
    }
    &.kt-badge-warning {
      background-color: var(--color-yellow-100);
      color: var(--color-yellow-800);
      &:is(.dark *) {
        background-color: color-mix(in srgb, oklch(28.6% 0.066 53.813) 50%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-yellow-950) 50%, transparent);
        }
      }
      &:is(.dark *) {
        color: var(--color-yellow-600);
      }
    }
    &.kt-badge-info {
      background-color: var(--color-violet-100);
      color: var(--color-violet-800);
      &:is(.dark *) {
        background-color: color-mix(in srgb, oklch(28.3% 0.141 291.089) 50%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-violet-950) 50%, transparent);
        }
      }
      &:is(.dark *) {
        color: var(--color-violet-600);
      }
    }
    &.kt-badge-mono {
      background-color: var(--mono);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--mono) 10%, transparent);
      }
      color: var(--mono);
    }
    &.kt-badge-destructive {
      background-color: var(--color-red-100);
      color: var(--color-red-800);
      &:is(.dark *) {
        background-color: color-mix(in srgb, oklch(25.8% 0.092 26.042) 50%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-red-950) 50%, transparent);
        }
      }
      &:is(.dark *) {
        color: var(--color-red-600);
      }
    }
  }
  .kt-badge-ghost {
    background-color: transparent;
    padding-inline: calc(var(--spacing) * 0);
    &.kt-badge-primary {
      color: var(--primary);
    }
    &.kt-badge-secondary {
      color: var(--secondary-foreground);
    }
    &.kt-badge-destructive {
      color: var(--destructive);
    }
    &.kt-badge-success {
      color: var(--color-green-500);
    }
    &.kt-badge-warning {
      color: var(--color-yellow-500);
    }
    &.kt-badge-info {
      color: var(--color-violet-500);
    }
    &.kt-badge-mono {
      color: var(--mono);
    }
  }
  .kt-badge-btn {
    margin-inline-end: calc(var(--spacing) * -0.5);
    display: inline-flex;
    width: calc(var(--spacing) * 3.5);
    height: calc(var(--spacing) * 3.5);
    cursor: pointer;
    align-items: center;
    justify-content: center;
    border-radius: calc(var(--radius) - 2px);
    padding: calc(var(--spacing) * 0);
    --tw-leading: 1;
    line-height: 1;
    i {
      font-size: var(--text-xs);
      line-height: var(--tw-leading, var(--text-xs--line-height));
      opacity: 70%;
      transition-property: opacity;
      transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
      transition-duration: var(--tw-duration, var(--default-transition-duration));
    }
    > svg {
      width: calc(var(--spacing) * 3.5);
      height: calc(var(--spacing) * 3.5);
      opacity: 70%;
      transition-property: opacity;
      transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
      transition-duration: var(--tw-duration, var(--default-transition-duration));
    }
    &:focus,	&:hover {
      i,	> svg {
        opacity: 100%;
      }
    }
  }
  .kt-badge-dot {
    width: calc(var(--spacing) * 1.5);
    height: calc(var(--spacing) * 1.5);
    border-radius: calc(infinity * 1px);
    background-color: currentcolor;
    opacity: 75%;
  }
}
@layer components {
  .kt-link {
    display: inline-flex;
    cursor: pointer;
    align-items: center;
    border-radius: calc(var(--radius) - 2px);
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
    &:focus {
      --tw-outline-style: none;
      outline-style: none;
    }
    &:focus-visible {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
    &:focus-visible {
      --tw-ring-color: var(--color-neutral-200);
    }
    &:focus-visible {
      --tw-ring-offset-width: 2px;
      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    }
    &:is(.dark *) {
      &:focus-visible {
        --tw-ring-color: var(--color-neutral-700);
      }
    }
  }
  .kt-link {
    color: var(--primary);
  }
  .kt-link-inverse {
    color: var(--background);
  }
  .kt-link-mono {
    color: var(--mono);
  }
  .kt-link {
    gap: calc(var(--spacing) * 1);
    font-size: 0.8125rem;
    --tw-leading: var(--text-sm--line-height);
    line-height: var(--text-sm--line-height);
    svg {
      width: calc(var(--spacing) * 4);
      height: calc(var(--spacing) * 4);
    }
    i {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }
  .kt-link-sm {
    gap: calc(var(--spacing) * 1);
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
    svg {
      width: calc(var(--spacing) * 3.5);
      height: calc(var(--spacing) * 3.5);
    }
    i {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }
  .kt-link-lg {
    gap: calc(var(--spacing) * 1.5);
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    svg {
      width: calc(var(--spacing) * 4);
      height: calc(var(--spacing) * 4);
    }
    i {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }
  .kt-link-underline {
    margin-top: calc(var(--spacing) * -0.5);
    text-decoration-style: solid;
    &:hover {
      @media (hover: hover) {
        text-decoration-line: underline;
      }
    }
    &:hover {
      @media (hover: hover) {
        text-underline-offset: 4px;
      }
    }
  }
  .kt-link-underlined {
    margin-top: calc(var(--spacing) * -0.5);
    text-decoration-line: underline;
    text-decoration-style: solid;
    text-underline-offset: 4px;
  }
  .kt-link-dashed {
    text-decoration-style: dashed;
    text-decoration-thickness: 1px;
  }
  .kt-link-disabled {
    pointer-events: none;
    opacity: 50%;
  }
}
@layer components {
  .kt-btn {
    display: inline-flex;
    cursor: pointer;
    align-items: center;
    justify-content: center;
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
    white-space: nowrap;
    --tw-ring-offset-color: var(--background);
    transition-property: color,box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
    &:focus-visible {
      --tw-outline-style: none;
      outline-style: none;
      @media (forced-colors: active) {
        outline: 2px solid transparent;
        outline-offset: 2px;
      }
    }
    &:disabled {
      pointer-events: none;
    }
    &:disabled {
      opacity: 50%;
    }
    &:focus-visible {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
    &:focus-visible {
      --tw-ring-color: var(--ring);
    }
    &:focus-visible {
      --tw-ring-offset-width: 2px;
      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    }
    flex-shrink: 0;
    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    --tw-shadow-color: rgba(0,0,0,0.05);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-shadow-color: color-mix(in oklab, rgba(0,0,0,0.05) var(--tw-shadow-alpha), transparent);
    }
    svg {
      flex-shrink: 0;
    }
  }
  .kt-btn {
    background-color: var(--primary);
    color: var(--primary-foreground);
    &:hover {
      @media (hover: hover) {
        background-color: var(--primary);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--primary) 90%, transparent);
        }
      }
    }
    &.active {
      background-color: var(--primary);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--primary) 90%, transparent);
      }
    }
  }
  .kt-btn-mono {
    background-color: var(--mono);
    color: var(--mono-foreground);
    &:hover {
      @media (hover: hover) {
        background-color: var(--mono);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--mono) 90%, transparent);
        }
      }
    }
    &.active {
      background-color: var(--mono);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--mono) 90%, transparent);
      }
    }
  }
  .kt-btn-destructive {
    background-color: var(--destructive);
    color: var(--destructive-foreground);
    &:hover {
      @media (hover: hover) {
        background-color: var(--destructive);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--destructive) 90%, transparent);
        }
      }
    }
    &.active {
      background-color: var(--destructive);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--destructive) 90%, transparent);
      }
    }
  }
  .kt-btn-secondary {
    background-color: var(--secondary);
    color: var(--secondary-foreground);
    &:hover {
      @media (hover: hover) {
        background-color: var(--secondary);
      }
    }
    &:hover {
      @media (hover: hover) {
        color: var(--foreground);
      }
    }
    &.active {
      background-color: var(--secondary);
    }
    &.active {
      color: var(--foreground);
    }
    i {
      color: var(--muted-foreground);
    }
    svg {
      color: var(--muted-foreground);
    }
    &:hover,	&.active {
      i {
        color: var(--muted-foreground);
      }
      svg {
        color: var(--muted-foreground);
      }
    }
  }
  .kt-btn-outline {
    border-style: var(--tw-border-style);
    border-width: 1px;
    border-color: var(--input);
    background-color: var(--background);
    color: var(--secondary-foreground);
    &:hover {
      @media (hover: hover) {
        background-color: var(--accent);
      }
    }
    &:hover {
      @media (hover: hover) {
        color: var(--accent-foreground);
      }
    }
    &.active {
      background-color: var(--accent);
    }
    &.active {
      color: var(--accent-foreground);
    }
    i {
      color: var(--muted-foreground);
    }
    svg {
      color: var(--muted-foreground);
    }
    &:hover,	&.active {
      i {
        color: var(--secondary-foreground);
      }
      svg {
        color: var(--secondary-foreground);
      }
    }
    &.kt-btn-primary {
      border-color: var(--primary);
      @supports (color: color-mix(in lab, red, red)) {
        border-color: color-mix(in oklab, var(--primary) 10%, transparent);
      }
      background-color: var(--primary);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--primary) 10%, transparent);
      }
      color: var(--primary);
      &:hover {
        @media (hover: hover) {
          background-color: var(--primary);
        }
      }
      &:hover {
        @media (hover: hover) {
          color: var(--primary-foreground);
        }
      }
      &.active {
        border-color: var(--primary);
      }
      &.active {
        background-color: var(--primary);
      }
      &.active {
        color: var(--primary-foreground);
      }
      i {
        color: var(--primary);
      }
      svg {
        color: var(--primary);
      }
      &:hover,	&.active {
        i {
          color: var(--primary-foreground);
        }
        svg {
          color: var(--primary-foreground);
        }
      }
    }
    &.kt-btn-destructive {
      border-color: var(--destructive);
      @supports (color: color-mix(in lab, red, red)) {
        border-color: color-mix(in oklab, var(--destructive) 10%, transparent);
      }
      background-color: var(--destructive);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--destructive) 10%, transparent);
      }
      color: var(--destructive);
      &:hover {
        @media (hover: hover) {
          background-color: var(--destructive);
        }
      }
      &:hover {
        @media (hover: hover) {
          color: var(--destructive-foreground);
        }
      }
      &.active {
        border-color: var(--destructive);
      }
      &.active {
        background-color: var(--destructive);
      }
      &.active {
        color: var(--destructive-foreground);
      }
      i {
        color: var(--destructive);
      }
      svg {
        color: var(--destructive);
      }
      &:hover,	&.active {
        i {
          color: var(--destructive-foreground);
        }
        svg {
          color: var(--destructive-foreground);
        }
      }
    }
  }
  .kt-btn-ghost {
    background-color: transparent;
    color: var(--accent-foreground);
    --tw-shadow: 0 0 #0000;
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    &:hover {
      @media (hover: hover) {
        background-color: var(--accent);
      }
    }
    &:hover {
      @media (hover: hover) {
        color: var(--accent-foreground);
      }
    }
    &.active {
      background-color: var(--accent);
    }
    &.active {
      color: var(--accent-foreground);
    }
    &:not(.kt-btn-primary):not(.kt-btn-destructive) {
      i {
        color: var(--muted-foreground);
      }
      svg {
        color: var(--muted-foreground);
      }
      &:hover,	&.active {
        i {
          color: var(--secondary-foreground);
        }
        svg {
          color: var(--secondary-foreground);
        }
      }
    }
    &.kt-btn-primary {
      color: var(--primary);
      &:hover {
        @media (hover: hover) {
          background-color: var(--primary);
        }
      }
      &:hover {
        @media (hover: hover) {
          color: var(--primary-foreground);
        }
      }
      &.active {
        background-color: var(--primary);
      }
      &.active {
        color: var(--primary-foreground);
      }
    }
    &.kt-btn-destructive {
      color: var(--destructive);
      &:hover {
        @media (hover: hover) {
          background-color: var(--destructive);
        }
      }
      &:hover {
        @media (hover: hover) {
          color: var(--destructive-foreground);
        }
      }
      &.active {
        background-color: var(--destructive);
      }
      &.active {
        color: var(--destructive-foreground);
      }
    }
  }
  .kt-btn-dim {
    background-color: transparent;
    color: var(--muted-foreground);
    --tw-shadow: 0 0 #0000;
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    &:hover {
      @media (hover: hover) {
        background-color: transparent;
      }
    }
    &:hover {
      @media (hover: hover) {
        color: var(--foreground);
      }
    }
    &.active {
      background-color: transparent;
    }
    &.active {
      color: var(--foreground);
    }
    i {
      color: var(--muted-foreground);
    }
    svg {
      color: var(--muted-foreground);
    }
    &:hover,	&.active {
      i {
        color: var(--secondary-foreground);
      }
      svg {
        color: var(--secondary-foreground);
      }
    }
  }
  .kt-btn {
    height: calc(var(--spacing) * 8.5);
    gap: calc(var(--spacing) * 1.5);
    border-radius: calc(var(--radius) - 2px);
    padding-inline: calc(var(--spacing) * 3);
    font-size: 0.8125rem;
    --tw-leading: var(--text-sm--line-height);
    line-height: var(--text-sm--line-height);
    svg {
      width: calc(var(--spacing) * 4);
      height: calc(var(--spacing) * 4);
    }
    i {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
    &.kt-btn-icon {
      width: calc(var(--spacing) * 8.5);
      height: calc(var(--spacing) * 8.5);
      padding: calc(var(--spacing) * 0);
    }
  }
  .kt-btn-lg {
    height: calc(var(--spacing) * 10);
    gap: calc(var(--spacing) * 1.5);
    border-radius: calc(var(--radius) - 2px);
    padding-inline: calc(var(--spacing) * 4);
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    svg {
      width: calc(var(--spacing) * 4);
      height: calc(var(--spacing) * 4);
    }
    i {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
    &.kt-btn-icon {
      width: calc(var(--spacing) * 10);
      height: calc(var(--spacing) * 10);
      padding: calc(var(--spacing) * 0);
    }
  }
  .kt-btn-sm {
    height: calc(var(--spacing) * 7);
    gap: calc(var(--spacing) * 1.25);
    border-radius: calc(var(--radius) - 2px);
    padding-inline: calc(var(--spacing) * 2.5);
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
    svg {
      width: calc(var(--spacing) * 3.5);
      height: calc(var(--spacing) * 3.5);
    }
    i {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
    &.kt-btn-icon {
      width: calc(var(--spacing) * 7);
      height: calc(var(--spacing) * 7);
      padding: calc(var(--spacing) * 0);
    }
  }
}
@layer components {
  .kt-label {
    display: inline-flex;
    align-items: center;
    gap: calc(var(--spacing) * 2);
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    --tw-leading: 1;
    line-height: 1;
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
    color: var(--foreground);
    .kt-switch:disabled + &,	.kt-checkbox:disabled + &,	.kt-radio:disabled + & {
      cursor: not-allowed;
      opacity: 50%;
    }
  }
  .kt-label-secondary {
    --tw-font-weight: var(--font-weight-normal);
    font-weight: var(--font-weight-normal);
  }
}
@layer components {
  .kt-card {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    border-radius: calc(var(--radius) + 4px);
    color: var(--card-foreground);
  }
  .kt-card {
    border-style: var(--tw-border-style);
    border-width: 1px;
    border-color: var(--border);
    background-color: var(--card);
    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    --tw-shadow-color: rgba(0,0,0,0.05);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-shadow-color: color-mix(in oklab, rgba(0,0,0,0.05) var(--tw-shadow-alpha), transparent);
    }
  }
  .kt-card-header {
    display: flex;
    min-height: calc(var(--spacing) * 14);
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    gap: calc(var(--spacing) * 2.5);
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
    border-color: var(--border);
    padding-inline: calc(var(--spacing) * 5);
  }
  .kt-card-footer {
    display: flex;
    align-items: center;
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
    border-color: var(--border);
    padding-inline: calc(var(--spacing) * 5);
    padding-block: calc(var(--spacing) * 4);
  }
  .kt-card-content {
    flex-grow: 1;
    padding-inline: calc(var(--spacing) * 5);
    padding-block: calc(var(--spacing) * 5);
  }
  .kt-card-table {
    display: grid;
    flex-grow: 1;
    .kt-table-border {
      border-style: var(--tw-border-style);
      border-width: 0px;
    }
    .kt-table {
      th,	td {
        &:first-child {
          padding-inline-start: calc(var(--spacing) * 5);
        }
        &:last-child {
          padding-inline-end: calc(var(--spacing) * 5);
        }
      }
    }
  }
  .kt-card-grid {
    & .kt-card-header,	& .kt-card-footer {
      padding-inline: calc(var(--spacing) * 5);
    }
    .kt-card-content {
      padding: 0;
      .kt-table {
        border: 0;
        th:first-child,	td:first-child {
          padding-inline-start: calc(var(--spacing) * 5);
          &.kt-table-cell-center {
            padding-inline-end: calc(var(--spacing) * 5);
          }
        }
        th:last-child,	td:last-child {
          padding-inline-end: calc(var(--spacing) * 5);
          &.table-cell-center {
            padding-inline-start: calc(var(--spacing) * 5);
          }
        }
      }
    }
  }
  .kt-card-heading {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .kt-card-toolbar {
    display: flex;
    align-items: center;
    gap: calc(var(--spacing) * 2.5);
  }
  .kt-card-title {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
    --tw-leading: 1;
    line-height: 1;
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
    --tw-tracking: var(--tracking-tight);
    letter-spacing: var(--tracking-tight);
  }
  .kt-card-description {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    color: var(--muted-foreground);
  }
  .kt-card-group {
    flex-grow: 1;
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
    border-color: var(--border);
    padding-inline: calc(var(--spacing) * 5);
    padding-block: calc(var(--spacing) * 5);
    &:last-child {
      border-bottom-style: var(--tw-border-style);
      border-bottom-width: 0px;
    }
    & + .kt-card-footer {
      border-top-style: var(--tw-border-style);
      border-top-width: 0px;
    }
  }
  .kt-card-accent {
    background-color: var(--muted);
    padding: calc(var(--spacing) * 1);
    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    --tw-shadow-color: rgba(0,0,0,0.05);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-shadow-color: color-mix(in oklab, rgba(0,0,0,0.05) var(--tw-shadow-alpha), transparent);
    }
    .kt-card-header {
      border-bottom-style: var(--tw-border-style);
      border-bottom-width: 0px;
    }
    .kt-card-content {
      border-top-left-radius: calc(var(--radius) + 4px);
      border-top-right-radius: calc(var(--radius) + 4px);
      background-color: var(--card);
      &:last-child {
        border-bottom-right-radius: calc(var(--radius) + 4px);
        border-bottom-left-radius: calc(var(--radius) + 4px);
      }
    }
    .kt-card-table {
      border-radius: calc(var(--radius) + 4px);
      background-color: var(--card);
      &:last-child {
        border-bottom-right-radius: calc(var(--radius) + 4px);
        border-bottom-left-radius: calc(var(--radius) + 4px);
      }
    }
    .kt-card-footer {
      margin-top: 2px;
      border-bottom-right-radius: calc(var(--radius) + 4px);
      border-bottom-left-radius: calc(var(--radius) + 4px);
      border-top-style: var(--tw-border-style);
      border-top-width: 0px;
      background-color: var(--card);
    }
  }
  .kt-card-border {
    border-style: var(--tw-border-style);
    border-width: 1px;
    border-color: var(--border);
  }
  .kt-card-rounded-t {
    border-top-left-radius: calc(var(--radius) + 4px);
    border-top-right-radius: calc(var(--radius) + 4px);
  }
  .kt-card-rounded-b {
    border-bottom-right-radius: calc(var(--radius) + 4px);
    border-bottom-left-radius: calc(var(--radius) + 4px);
  }
}
@layer components {
  .kt-checkbox {
    cursor: pointer;
    appearance: none;
    background-position: center;
    background-repeat: no-repeat;
    flex-shrink: 0;
    border-radius: calc(var(--radius) - 4px);
    border-style: var(--tw-border-style);
    border-width: 1px;
    border-color: var(--input);
    background-color: var(--background);
    --tw-ring-offset-color: var(--background);
    &:focus-visible {
      --tw-outline-style: none;
      outline-style: none;
    }
    &:focus-visible {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
    &:focus-visible {
      --tw-ring-color: var(--ring);
    }
    &:focus-visible {
      --tw-ring-offset-width: 2px;
      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    }
    &:disabled {
      cursor: not-allowed;
    }
    &:disabled {
      opacity: 50%;
    }
    &[aria-invalid="true"] {
      border-color: var(--destructive);
      @supports (color: color-mix(in lab, red, red)) {
        border-color: color-mix(in oklab, var(--destructive) 60%, transparent);
      }
    }
    &[aria-invalid="true"] {
      --tw-ring-color: var(--destructive);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--destructive) 10%, transparent);
      }
    }
  }
  .kt-checkbox {
    &:checked {
      border-color: var(--primary);
    }
    &:checked {
      background-color: var(--primary);
    }
    &:checked {
      color: var(--primary-foreground);
    }
    &:indeterminate {
      border-color: var(--primary);
    }
    &:indeterminate {
      background-color: var(--primary);
    }
    &:indeterminate {
      color: var(--primary-foreground);
    }
  }
  .kt-checkbox-mono {
    &:checked {
      border-color: var(--mono);
    }
    &:checked {
      background-color: var(--mono);
    }
    &:checked {
      color: var(--mono-foreground);
    }
    &:indeterminate {
      border-color: var(--mono);
    }
    &:indeterminate {
      background-color: var(--mono);
    }
    &:indeterminate {
      color: var(--mono-foreground);
    }
  }
  .kt-checkbox {
    width: calc(var(--spacing) * 4.5);
    height: calc(var(--spacing) * 4.5);
    &:checked,	&[aria-checked='true'] {
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='9' viewBox='0 0 12 9' fill='none'%3E%3Cpath d='M10.3667 0.541643L4.80007 6.10831L1.56674 2.87498C1.41061 2.71977 1.1994 2.63265 0.979241 2.63265C0.759086 2.63265 0.547876 2.71977 0.391741 2.87498C0.236532 3.03111 0.149414 3.24232 0.149414 3.46248C0.149414 3.68263 0.236532 3.89384 0.391741 4.04998L4.21674 7.87498C4.37288 8.03019 4.58409 8.1173 4.80424 8.1173C5.0244 8.1173 5.23561 8.03019 5.39174 7.87498L11.5417 1.72498C11.6198 1.64751 11.6818 1.55534 11.7241 1.45379C11.7665 1.35224 11.7882 1.24332 11.7882 1.13331C11.7882 1.0233 11.7665 0.914379 11.7241 0.81283C11.6818 0.711281 11.6198 0.619113 11.5417 0.541643C11.3856 0.386434 11.1744 0.299316 10.9542 0.299316C10.7341 0.299316 10.5229 0.386434 10.3667 0.541643Z' fill='white'/%3E%3C/svg%3E");
    }
    &:indeterminate {
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3E%3Cpath fill='none' stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M6 10h8'/%3E%3C/svg%3E");
    }
  }
  .kt-checkbox {
    width: calc(var(--spacing) * 5);
    height: calc(var(--spacing) * 5);
  }
  .kt-checkbox-sm {
    width: calc(var(--spacing) * 4.5);
    height: calc(var(--spacing) * 4.5);
    &:checked,	&[aria-checked='true'] {
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='10' height='7' viewBox='0 0 10 7' fill='none'%3E%3Cpath d='M8.4932 0.233321L4.03986 4.68665L1.4532 2.09999C1.32829 1.97582 1.15932 1.90613 0.983198 1.90613C0.807074 1.90613 0.638106 1.97582 0.513198 2.09999C0.38903 2.2249 0.319336 2.39386 0.319336 2.56999C0.319336 2.74611 0.38903 2.91508 0.513198 3.03999L3.5732 6.09999C3.69811 6.22415 3.86707 6.29385 4.0432 6.29385C4.21932 6.29385 4.38829 6.22415 4.5132 6.09999L9.4332 1.17999C9.49568 1.11801 9.54528 1.04428 9.57912 0.963038C9.61297 0.881799 9.6304 0.794662 9.6304 0.706655C9.6304 0.618647 9.61297 0.53151 9.57912 0.45027C9.54528 0.369031 9.49568 0.295296 9.4332 0.233321C9.30829 0.109154 9.13932 0.0394592 8.9632 0.0394592C8.78707 0.0394592 8.61811 0.109154 8.4932 0.233321Z' fill='white'/%3E%3C/svg%3E");
    }
    &:indeterminate {
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3E%3Cpath fill='none' stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M6 10h8'/%3E%3C/svg%3E");
    }
  }
  .kt-checkbox-lg {
    width: calc(var(--spacing) * 5.5);
    height: calc(var(--spacing) * 5.5);
    &:checked,	&[aria-checked='true'] {
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='14' height='10' viewBox='0 0 14 10' fill='none'%3E%3Cpath d='M11.8035 1.19582L5.68018 7.31915L2.12351 3.76249C1.95176 3.59176 1.71943 3.49593 1.47726 3.49593C1.23509 3.49593 1.00276 3.59176 0.831013 3.76249C0.660283 3.93424 0.564453 4.16657 0.564453 4.40874C0.564453 4.65091 0.660283 4.88324 0.831013 5.05499L5.03851 9.26249C5.21026 9.43322 5.44259 9.52905 5.68476 9.52905C5.92693 9.52905 6.15926 9.43322 6.33101 9.26249L13.096 2.49749C13.1819 2.41227 13.2501 2.31089 13.2967 2.19918C13.3432 2.08748 13.3672 1.96766 13.3672 1.84665C13.3672 1.72564 13.3432 1.60583 13.2967 1.49413C13.2501 1.38242 13.1819 1.28104 13.096 1.19582C12.9243 1.02509 12.6919 0.92926 12.4498 0.92926C12.2076 0.92926 11.9753 1.02509 11.8035 1.19582Z' fill='white'/%3E%3C/svg%3E");
    }
    &:indeterminate {
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3E%3Cpath fill='none' stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M6 10h8'/%3E%3C/svg%3E");
    }
  }
}
@layer components {
  .dark .kt-checkbox {
    &[aria-invalid="true"] {
      border-color: var(--destructive);
    }
    &[aria-invalid="true"] {
      --tw-ring-color: var(--destructive);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);
      }
    }
  }
}
@layer components {
  .kt-datatable-toolbar {
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: calc(var(--spacing) * 3);
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
    border-color: var(--border);
    padding-inline: calc(var(--spacing) * 4);
    padding-block: calc(var(--spacing) * 3);
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
    color: var(--muted-foreground);
    @media (width >= 40rem) {
      flex-direction: row;
    }
    @media (width >= 40rem) {
      justify-content: space-between;
    }
  }
  .kt-datatable-pagination {
    display: flex;
    align-items: center;
    gap: calc(var(--spacing) * 1);
    .kt-datatable-pagination-button {
      display: inline-flex;
      height: calc(var(--spacing) * 7);
      min-width: calc(var(--spacing) * 7);
      cursor: pointer;
      align-items: center;
      justify-content: center;
      border-radius: calc(var(--radius) - 2px);
      background-color: transparent;
      padding-inline: calc(var(--spacing) * 0.5);
      color: var(--muted-foreground);
      &.active,	&:hover:not(:disabled) {
        background-color: var(--accent);
        color: var(--accent-foreground);
      }
      &.kt-datatable-pagination-prev {
        color: var(--foreground);
      }
      &.kt-datatable-pagination-next {
        color: var(--foreground);
      }
      &:disabled {
        cursor: default;
        color: var(--muted-foreground);
      }
    }
  }
  .kt-datatable-length {
    display: flex;
    align-items: center;
    gap: calc(var(--spacing) * 2);
    white-space: nowrap;
  }
  .kt-datatable-info {
    display: flex;
    align-items: center;
    gap: calc(var(--spacing) * 4);
  }
  .kt-datatable-loading {
    display: flex;
    align-items: center;
    gap: calc(var(--spacing) * 2);
    border-radius: calc(var(--radius) - 2px);
    border-style: var(--tw-border-style);
    border-width: 1px;
    border-color: var(--border);
    background-color: var(--card);
    padding-inline: calc(var(--spacing) * 4);
    padding-block: calc(var(--spacing) * 2);
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    --tw-leading: 1;
    line-height: 1;
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
    color: var(--card-foreground);
    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    --tw-shadow-color: rgba(0,0,0,0.05);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-shadow-color: color-mix(in oklab, rgba(0,0,0,0.05) var(--tw-shadow-alpha), transparent);
    }
  }
  .kt-datatable-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
  }
  [data-kt-datatable] {
    position: relative;
  }
  [data-kt-datatable].loading table {
    opacity: 0.6;
  }
}
@layer components;
@layer components {
  .kt-drawer {
    position: fixed;
    inset: calc(var(--spacing) * 0);
    z-index: 10;
    flex-direction: column;
    background-color: var(--popover);
    color: var(--popover-foreground);
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
    --tw-duration: 300ms;
    transition-duration: 300ms;
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
    --tw-outline-style: none;
    outline-style: none;
  }
  .kt-drawer-backdrop {
    position: fixed;
    inset: calc(var(--spacing) * 0);
    background-color: color-mix(in srgb, #000 30%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-black) 30%, transparent);
    }
    backdrop-filter: blur(4px);
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
    --tw-duration: 300ms;
    transition-duration: 300ms;
  }
  .kt-drawer-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
    border-color: var(--border);
    padding: calc(var(--spacing) * 5);
  }
  .kt-drawer-title {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
    color: var(--mono);
  }
  .kt-drawer-close {
    margin-inline-end: calc(var(--spacing) * -2.5);
    width: calc(var(--spacing) * 6);
    height: calc(var(--spacing) * 6);
    flex-shrink: 0;
    cursor: pointer;
    i {
      font-size: var(--text-xs);
      line-height: var(--tw-leading, var(--text-xs--line-height));
      opacity: 70%;
    }
    > svg {
      width: calc(var(--spacing) * 4);
      height: calc(var(--spacing) * 4);
      opacity: 70%;
    }
    &:focus,	&:hover {
      i {
        opacity: 100%;
      }
      > svg {
        opacity: 100%;
      }
    }
  }
  .kt-drawer-content {
    flex-grow: 1;
    overflow-y: auto;
    padding: calc(var(--spacing) * 5);
  }
  .kt-drawer-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
    border-color: var(--border);
    padding: calc(var(--spacing) * 5);
  }
  .kt-drawer-start {
    inset-inline-end: auto;
    max-width: 90%;
    --tw-translate-x: -100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
    &[data-kt-drawer-initialized].open {
      --tw-translate-x: calc(var(--spacing) * 0);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
    [data-kt-drawer-initialized].open & {
      --tw-translate-x: calc(var(--spacing) * 0);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .kt-drawer-end {
    inset-inline-start: auto;
    max-width: 90%;
    --tw-translate-x: 100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
    &[data-kt-drawer-initialized].open {
      --tw-translate-x: calc(var(--spacing) * 0);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
    [data-kt-drawer-initialized].open & {
      --tw-translate-x: calc(var(--spacing) * 0);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .kt-drawer-top {
    inset-inline-start: calc(var(--spacing) * 0);
    inset-inline-end: calc(var(--spacing) * 0);
    top: auto;
    bottom: calc(var(--spacing) * 0);
    max-height: 90%;
    --tw-translate-y: 100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
    &[data-kt-drawer-initialized].open {
      --tw-translate-y: calc(var(--spacing) * 0);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
    [data-kt-drawer-initialized].open & {
      --tw-translate-y: calc(var(--spacing) * 0);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .kt-drawer-bottom {
    inset-inline-start: calc(var(--spacing) * 0);
    inset-inline-end: calc(var(--spacing) * 0);
    top: calc(var(--spacing) * 0);
    bottom: auto;
    max-height: 90%;
    --tw-translate-y: -100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
    &[data-kt-drawer-initialized].open {
      --tw-translate-y: calc(var(--spacing) * 0);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
    [data-kt-drawer-initialized].open & {
      --tw-translate-y: calc(var(--spacing) * 0);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .kt-drawer.open .kt-drawer-start {
    --tw-translate-x: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .kt-drawer.open .kt-drawer-end {
    --tw-translate-x: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .kt-drawer.open .kt-drawer-top {
    --tw-translate-y: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .kt-drawer.open .kt-drawer-bottom {
    --tw-translate-y: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
}
@layer components {
  .kt-dropdown {
    border-radius: calc(var(--radius) - 2px);
    border-style: var(--tw-border-style);
    border-width: 1px;
    border-color: var(--border);
    background-color: var(--popover);
    color: var(--popover-foreground);
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    --tw-shadow-color: rgba(0,0,0,0.05);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-shadow-color: color-mix(in oklab, rgba(0,0,0,0.05) var(--tw-shadow-alpha), transparent);
    }
    &:not(.open) {
      display: none;
    }
  }
  .kt-dropdown-header {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
    border-color: var(--border);
    padding-inline: calc(var(--spacing) * 4);
    padding-block: calc(var(--spacing) * 3);
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }
  .kt-dropdown-body {
    padding-inline: calc(var(--spacing) * 4);
    padding-block: calc(var(--spacing) * 3);
  }
}
@layer components {
  .kt-dropdown-menu {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
    }
    border-radius: calc(var(--radius) - 2px);
    border-style: var(--tw-border-style);
    border-width: 1px;
    border-color: var(--border);
    background-color: var(--popover);
    padding: calc(var(--spacing) * 2);
    color: var(--popover-foreground);
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    --tw-shadow-color: rgba(0,0,0,0.05);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-shadow-color: color-mix(in oklab, rgba(0,0,0,0.05) var(--tw-shadow-alpha), transparent);
    }
    &:not(.open) {
      display: none;
    }
  }
  .kt-dropdown-menu-sub {
    width: 100%;
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .kt-dropdown-menu-toggle {
    display: flex;
    width: 100%;
    cursor: pointer;
    align-items: center;
    column-gap: calc(var(--spacing) * 2.5);
    border-radius: calc(var(--radius) - 2px);
    padding-inline: calc(var(--spacing) * 2.5);
    padding-block: calc(var(--spacing) * 2);
    text-align: start;
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
    &:disabled {
      pointer-events: none;
    }
    &:disabled {
      opacity: 50%;
    }
    color: var(--foreground);
    &:hover {
      @media (hover: hover) {
        background-color: var(--accent);
      }
    }
    &:hover {
      @media (hover: hover) {
        color: var(--accent-foreground);
      }
    }
    [data-kt-dropdown-initialized].open > [data-kt-dropdown-toggle]& {
      background-color: var(--accent);
    }
    [data-kt-dropdown-initialized].open > [data-kt-dropdown-toggle] & {
      background-color: var(--accent);
    }
    [data-kt-dropdown-initialized].open > [data-kt-dropdown-toggle]& {
      color: var(--accent-foreground);
    }
    [data-kt-dropdown-initialized].open > [data-kt-dropdown-toggle] & {
      color: var(--accent-foreground);
    }
    .kt-dropdown-menu-indicator {
      margin-inline-start: auto;
      display: inline-flex;
      width: calc(var(--spacing) * 3.5);
      height: calc(var(--spacing) * 3.5);
      flex-shrink: 0;
      align-items: center;
      color: var(--muted-foreground);
    }
    i {
      flex-shrink: 0;
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
      color: var(--muted-foreground);
    }
    svg {
      width: calc(var(--spacing) * 4);
      height: calc(var(--spacing) * 4);
      flex-shrink: 0;
      color: var(--muted-foreground);
    }
  }
  .kt-dropdown-menu-link {
    display: flex;
    width: 100%;
    cursor: pointer;
    align-items: center;
    column-gap: calc(var(--spacing) * 2.5);
    border-radius: calc(var(--radius) - 2px);
    padding-inline: calc(var(--spacing) * 2.5);
    padding-block: calc(var(--spacing) * 2);
    text-align: start;
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
    &:disabled {
      pointer-events: none;
    }
    &:disabled {
      opacity: 50%;
    }
    color: var(--foreground);
    &:hover {
      @media (hover: hover) {
        background-color: var(--accent);
      }
    }
    &:hover {
      @media (hover: hover) {
        color: var(--accent-foreground);
      }
    }
    [data-kt-dropdown-initialized] .selected {
      background-color: var(--accent);
    }
    [data-kt-dropdown-initialized] .selected & {
      background-color: var(--accent);
    }
    [data-kt-dropdown-initialized] &.selected {
      background-color: var(--accent);
    }
    [data-kt-dropdown-initialized] .selected {
      color: var(--accent-foreground);
    }
    [data-kt-dropdown-initialized] .selected & {
      color: var(--accent-foreground);
    }
    [data-kt-dropdown-initialized] &.selected {
      color: var(--accent-foreground);
    }
    i {
      flex-shrink: 0;
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
      color: var(--muted-foreground);
    }
    svg {
      width: calc(var(--spacing) * 4);
      height: calc(var(--spacing) * 4);
      flex-shrink: 0;
      color: var(--muted-foreground);
    }
  }
  .kt-dropdown-menu-separator {
    margin-inline: calc(var(--spacing) * -2);
    margin-block: calc(var(--spacing) * 2.5);
    height: 1px;
    background-color: var(--border);
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      rotate: 180deg;
    }
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
    }
  }
}
@layer components {
  .kt-image-input {
    position: relative;
    display: inline-flex;
    width: calc(var(--spacing) * 20);
    height: calc(var(--spacing) * 20);
    cursor: pointer;
    align-items: center;
    justify-content: center;
    input[type='file'] {
      position: absolute;
      width: calc(var(--spacing) * 0);
      height: calc(var(--spacing) * 0);
      appearance: none;
      opacity: 0%;
    }
  }
  .kt-image-input-remove {
    position: absolute;
    inset-inline-end: calc(var(--spacing) * 0.25);
    top: calc(var(--spacing) * 0.25);
    z-index: 1;
    display: flex;
    width: calc(var(--spacing) * 5);
    height: calc(var(--spacing) * 5);
    cursor: pointer;
    align-items: center;
    justify-content: center;
    border-radius: calc(infinity * 1px);
    border-style: var(--tw-border-style);
    border-width: 1px;
    border-color: var(--border);
    background-color: var(--background);
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    i {
      font-size: 11px;
      color: var(--muted-foreground);
    }
    svg {
      width: calc(var(--spacing) * 3.25);
      height: calc(var(--spacing) * 3.25);
      color: var(--muted-foreground);
    }
    &:hover {
      i {
        color: var(--foreground);
      }
      svg {
        color: var(--foreground);
      }
    }
  }
  .kt-image-input-placeholder {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    border-radius: calc(infinity * 1px);
    border-style: var(--tw-border-style);
    border-width: 1px;
    border-color: var(--border);
    background-size: cover;
    background-repeat: no-repeat;
    [data-kt-image-input-initialized].empty & {
      border-color: var(--border);
    }
  }
  .kt-image-input-preview {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    border-radius: calc(infinity * 1px);
    background-size: cover;
    background-repeat: no-repeat;
  }
}
@layer components {
  .kt-input {
    display: block;
    width: 100%;
    border-style: var(--tw-border-style);
    border-width: 1px;
    border-color: var(--input);
    background-color: var(--background);
    color: var(--foreground);
    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    --tw-shadow-color: rgba(0,0,0,0.05);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-shadow-color: color-mix(in oklab, rgba(0,0,0,0.05) var(--tw-shadow-alpha), transparent);
    }
    outline-style: var(--tw-outline-style);
    outline-width: 0px;
    transition-property: color,box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
    &::placeholder {
      color: var(--muted-foreground);
    }
    &:focus-visible {
      border-color: var(--ring);
    }
    &:focus-visible {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
    &:focus-visible {
      --tw-ring-color: var(--ring);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--ring) 30%, transparent);
      }
    }
    &:focus-visible {
      --tw-outline-style: none;
      outline-style: none;
    }
    &:disabled {
      cursor: not-allowed;
    }
    &:disabled {
      opacity: 60%;
    }
    &[readonly] {
      cursor: not-allowed;
    }
    &[readonly] {
      background-color: var(--muted);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--muted) 80%, transparent);
      }
    }
    &[readonly] {
      color: var(--secondary-foreground);
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, var(--secondary-foreground) 80%, transparent);
      }
    }
    &::file-selector-button {
      height: 100%;
    }
    &[type=file] {
      padding-block: calc(var(--spacing) * 0);
    }
    &::file-selector-button {
      border-style: var(--tw-border-style);
      border-width: 0px;
    }
    &::file-selector-button {
      border-inline-end-style: var(--tw-border-style);
      border-inline-end-width: 1px;
    }
    &::file-selector-button {
      --tw-border-style: solid;
      border-style: solid;
    }
    &::file-selector-button {
      border-color: var(--input);
    }
    &::file-selector-button {
      background-color: transparent;
    }
    &::file-selector-button {
      padding: calc(var(--spacing) * 0);
    }
    &::file-selector-button {
      --tw-font-weight: var(--font-weight-medium);
      font-weight: var(--font-weight-medium);
    }
    &::file-selector-button {
      color: var(--foreground);
    }
    &::file-selector-button {
      font-style: normal;
    }
    &[aria-invalid="true"] {
      border-color: var(--destructive);
      @supports (color: color-mix(in lab, red, red)) {
        border-color: color-mix(in oklab, var(--destructive) 60%, transparent);
      }
    }
    &[aria-invalid="true"] {
      --tw-ring-color: var(--destructive);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--destructive) 10%, transparent);
      }
    }
  }
  .kt-input {
    height: calc(var(--spacing) * 8.5);
    border-radius: calc(var(--radius) - 2px);
    padding-inline: calc(var(--spacing) * 3);
    font-size: 0.8125rem;
    --tw-leading: var(--text-sm--line-height);
    line-height: var(--text-sm--line-height);
    &::file-selector-button {
      margin-inline-end: calc(var(--spacing) * 3);
    }
    &::file-selector-button {
      padding-inline-end: calc(var(--spacing) * 3);
    }
  }
  .kt-input-lg {
    height: calc(var(--spacing) * 10);
    border-radius: calc(var(--radius) - 2px);
    padding-inline: calc(var(--spacing) * 4);
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    &::file-selector-button {
      margin-inline-end: calc(var(--spacing) * 4);
    }
    &::file-selector-button {
      padding-inline-end: calc(var(--spacing) * 4);
    }
  }
  .kt-input-sm {
    height: calc(var(--spacing) * 7);
    border-radius: calc(var(--radius) - 2px);
    padding-inline: calc(var(--spacing) * 2.5);
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
    &::file-selector-button {
      margin-inline-end: calc(var(--spacing) * 2.5);
    }
    &::file-selector-button {
      padding-inline-end: calc(var(--spacing) * 2.5);
    }
  }
}
@layer components {
  .kt-input:not(input) {
    display: flex;
    align-items: center;
    gap: calc(var(--spacing) * 1.5);
    &:has(input:focus-visible) {
      border-color: var(--ring);
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      --tw-ring-color: var(--ring);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--ring) 30%, transparent);
      }
      --tw-outline-style: none;
      outline-style: none;
    }
    input {
      display: flex;
      height: auto;
      width: 100%;
      border-style: var(--tw-border-style);
      border-width: 0px;
      background-color: transparent;
      padding: calc(var(--spacing) * 0);
      color: var(--foreground);
      --tw-shadow: 0 0 #0000;
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      transition-property: color;
      transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
      transition-duration: var(--tw-duration, var(--default-transition-duration));
      --tw-outline-style: none;
      outline-style: none;
      &::placeholder {
        color: var(--muted-foreground);
      }
      &:focus-visible {
        --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
      &:disabled {
        cursor: not-allowed;
      }
      &:disabled {
        opacity: 50%;
      }
      font-size: inherit;
      list-style: inherit;
    }
    i {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
      color: var(--muted-foreground);
    }
    svg {
      width: calc(var(--spacing) * 4);
      height: calc(var(--spacing) * 4);
      color: var(--muted-foreground);
    }
    &.kt-input-sm {
      gap: calc(var(--spacing) * 1.25);
      i {
        font-size: var(--text-sm);
        line-height: var(--tw-leading, var(--text-sm--line-height));
      }
      svg {
        width: calc(var(--spacing) * 3.5);
        height: calc(var(--spacing) * 3.5);
        color: var(--muted-foreground);
      }
    }
    &.kt-input-lg {
      gap: calc(var(--spacing) * 1.5);
      i {
        font-size: var(--text-lg);
        line-height: var(--tw-leading, var(--text-lg--line-height));
      }
      svg {
        width: calc(var(--spacing) * 4);
        height: calc(var(--spacing) * 4);
        color: var(--muted-foreground);
      }
    }
  }
}
@layer components {
  .kt-input-addon {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    border-style: var(--tw-border-style);
    border-width: 1px;
    border-color: var(--input);
    background-color: var(--muted);
    color: var(--secondary-foreground);
    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    --tw-shadow-color: rgba(0,0,0,0.05);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-shadow-color: color-mix(in oklab, rgba(0,0,0,0.05) var(--tw-shadow-alpha), transparent);
    }
    height: calc(var(--spacing) * 8.5);
    min-width: calc(var(--spacing) * 8.5);
    border-radius: calc(var(--radius) - 2px);
    padding-inline: calc(var(--spacing) * 3);
    font-size: 0.8125rem;
    --tw-leading: var(--text-sm--line-height);
    line-height: var(--text-sm--line-height);
    i {
      font-size: var(--text-lg);
      line-height: var(--tw-leading, var(--text-lg--line-height));
    }
    svg {
      width: calc(var(--spacing) * 4.5);
      height: calc(var(--spacing) * 4.5);
    }
    &.kt-input-addon-sm {
      height: calc(var(--spacing) * 7);
      min-width: calc(var(--spacing) * 7);
      border-radius: calc(var(--radius) - 2px);
      padding-inline: calc(var(--spacing) * 2.5);
      font-size: var(--text-xs);
      line-height: var(--tw-leading, var(--text-xs--line-height));
      i {
        font-size: var(--text-base);
        line-height: var(--tw-leading, var(--text-base--line-height));
      }
      svg {
        width: calc(var(--spacing) * 3.5);
        height: calc(var(--spacing) * 3.5);
      }
    }
    &.kt-input-addon-lg {
      height: calc(var(--spacing) * 10);
      min-width: calc(var(--spacing) * 10);
      border-radius: calc(var(--radius) - 2px);
      padding-inline: calc(var(--spacing) * 4);
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
      i {
        font-size: var(--text-lg);
        line-height: var(--tw-leading, var(--text-lg--line-height));
      }
      svg {
        width: calc(var(--spacing) * 4.5);
        height: calc(var(--spacing) * 4.5);
      }
    }
    &.kt-input-addon-icon {
      padding-inline: calc(var(--spacing) * 0);
    }
  }
}
@layer components {
  .kt-input-ghost {
    border-style: var(--tw-border-style);
    border-width: 0px;
    background-color: var(--background);
    padding: calc(var(--spacing) * 0);
    --tw-shadow: 0 0 #0000;
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    &:focus-visible {
      border-style: var(--tw-border-style);
      border-width: 0px;
    }
    &:focus-visible {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
}
@layer components {
  .dark .kt-input {
    &[aria-invalid="true"] {
      border-color: var(--destructive);
    }
    &[aria-invalid="true"] {
      --tw-ring-color: var(--destructive);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);
      }
    }
  }
}
@layer components {
  .kt-input-group {
    position: relative;
    display: flex;
    align-items: stretch;
    .kt-input {
      z-index: 1;
      flex-grow: 1;
    }
    .kt-input-addon:has(+ .kt-input) {
      border-start-end-radius: 0 !important;
      border-end-end-radius: 0 !important;
      border-inline-end-style: var(--tw-border-style) !important;
      border-inline-end-width: 0px !important;
    }
    .kt-input + .kt-input-addon {
      border-start-start-radius: 0 !important;
      border-end-start-radius: 0 !important;
      border-inline-start-style: var(--tw-border-style) !important;
      border-inline-start-width: 0px !important;
    }
    .kt-input-addon:has(+ .kt-btn) {
      border-start-end-radius: 0 !important;
      border-end-end-radius: 0 !important;
    }
    .kt-input + .kt-btn {
      border-start-start-radius: 0 !important;
      border-end-start-radius: 0 !important;
    }
    .kt-input + .kt-btn.kt-btn-outline {
      border-inline-start-style: var(--tw-border-style) !important;
      border-inline-start-width: 0px !important;
    }
    .kt-btn + .kt-input,	.kt-input-addon + .kt-input {
      border-start-start-radius: 0 !important;
      border-end-start-radius: 0 !important;
    }
    .kt-input:has(+ .kt-btn),	.kt-input:has(+ .kt-input-addon) {
      border-start-end-radius: 0 !important;
      border-end-end-radius: 0 !important;
    }
  }
}
@layer components {
  .kt-modal {
    position: fixed;
    inset: calc(var(--spacing) * 0);
    overflow: auto;
    padding: calc(var(--spacing) * 4);
    &:not(.open) {
      display: none;
    }
  }
  .kt-modal-backdrop {
    position: fixed;
    inset: calc(var(--spacing) * 0);
    background-color: color-mix(in srgb, #000 30%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-black) 30%, transparent);
    }
    backdrop-filter: blur(4px);
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
    --tw-duration: 300ms;
    transition-duration: 300ms;
  }
  .kt-modal-close {
    margin-inline-end: calc(var(--spacing) * -2.5);
    width: calc(var(--spacing) * 6);
    height: calc(var(--spacing) * 6);
    flex-shrink: 0;
    cursor: pointer;
    i {
      font-size: var(--text-xs);
      line-height: var(--tw-leading, var(--text-xs--line-height));
      opacity: 70%;
    }
    > svg {
      width: calc(var(--spacing) * 4);
      height: calc(var(--spacing) * 4);
      opacity: 70%;
    }
    &:focus,	&:hover {
      i {
        opacity: 100%;
      }
      > svg {
        opacity: 100%;
      }
    }
  }
  .kt-modal-dialog {
    position: fixed;
    inset-inline-start: calc(1/2 * 100%);
    top: calc(1/2 * 100%);
    display: none;
    --tw-translate-x: calc(calc(1/2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
    --tw-translate-y: calc(calc(1/2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
    overflow: auto;
    padding: calc(var(--spacing) * 4);
  }
  .kt-modal-content {
    position: relative;
    margin-inline: auto;
    display: flex;
    flex-direction: column;
    border-radius: var(--radius);
    border-style: var(--tw-border-style);
    border-width: 1px;
    border-color: var(--border);
    background-color: var(--popover);
    color: var(--popover-foreground);
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .kt-modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: calc(var(--spacing) * 2);
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
    border-color: var(--border);
    padding-inline: calc(var(--spacing) * 5);
    padding-block: calc(var(--spacing) * 4);
  }
  .kt-modal-title {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
    color: var(--mono);
  }
  .kt-modal-body {
    overflow-y: auto;
    padding-inline: calc(var(--spacing) * 5);
    padding-block: calc(var(--spacing) * 4);
  }
  .kt-modal-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: calc(var(--spacing) * 2);
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
    border-color: var(--border);
    padding-inline: calc(var(--spacing) * 5);
    padding-block: calc(var(--spacing) * 4);
  }
  .kt-modal-center:not(.kt-modal-fit) {
    .kt-modal-content {
      top: calc(1/2 * 100%);
      --tw-translate-y: calc(calc(1/2 * 100%) * -1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .kt-modal-fit {
    overflow: visible;
    padding: calc(var(--spacing) * 0);
    &.kt-modal-center {
      inset-inline-start: calc(1/2 * 100%);
      top: calc(1/2 * 100%);
      --tw-translate-x: calc(calc(1/2 * 100%) * -1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
      --tw-translate-y: calc(calc(1/2 * 100%) * -1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
}
@layer components {
  .kt-radio {
    cursor: pointer;
    appearance: none;
    background-position: center;
    background-repeat: no-repeat;
    flex-shrink: 0;
    border-radius: calc(infinity * 1px);
    border-style: var(--tw-border-style);
    border-width: 1px;
    border-color: var(--input);
    background-color: var(--background);
    --tw-ring-offset-color: var(--background);
    &:focus-visible {
      --tw-outline-style: none;
      outline-style: none;
    }
    &:focus-visible {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
    &:focus-visible {
      --tw-ring-color: var(--ring);
    }
    &:focus-visible {
      --tw-ring-offset-width: 2px;
      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    }
    &:disabled {
      cursor: not-allowed;
    }
    &:disabled {
      opacity: 50%;
    }
    &[aria-invalid="true"] {
      border-color: var(--destructive);
      @supports (color: color-mix(in lab, red, red)) {
        border-color: color-mix(in oklab, var(--destructive) 60%, transparent);
      }
    }
    &[aria-invalid="true"] {
      --tw-ring-color: var(--destructive);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--destructive) 10%, transparent);
      }
    }
    &:is(.dark *) {
      &[aria-invalid="true"] {
        border-color: var(--destructive);
      }
    }
    &:is(.dark *) {
      &[aria-invalid="true"] {
        --tw-ring-color: var(--destructive);
        @supports (color: color-mix(in lab, red, red)) {
          --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);
        }
      }
    }
  }
  .kt-radio {
    &:checked {
      border-color: var(--primary);
    }
    &:checked {
      background-color: var(--primary);
    }
    &:checked {
      color: var(--primary-foreground);
    }
    &:indeterminate {
      border-color: var(--primary);
    }
    &:indeterminate {
      background-color: var(--primary);
    }
    &:indeterminate {
      color: var(--primary-foreground);
    }
  }
  .kt-radio {
    &:checked,	&[aria-checked='true'] {
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 22 22' fill='none'%3E%3Ccircle cx='10.9995' cy='11' r='5.86667' fill='white'/%3E%3C/svg%3E");
    }
  }
  .kt-radio {
    width: calc(var(--spacing) * 5);
    height: calc(var(--spacing) * 5);
  }
  .kt-radio-sm {
    width: calc(var(--spacing) * 4.5);
    height: calc(var(--spacing) * 4.5);
  }
  .kt-radio-lg {
    width: calc(var(--spacing) * 5.5);
    height: calc(var(--spacing) * 5.5);
  }
  .kt-radio-mono {
    &:checked {
      border-color: var(--mono);
    }
    &:checked {
      background-color: var(--mono);
    }
    &:checked {
      color: var(--mono-foreground);
    }
    &:indeterminate {
      border-color: var(--mono);
    }
    &:indeterminate {
      background-color: var(--mono);
    }
    &:indeterminate {
      color: var(--mono-foreground);
    }
  }
}
@layer components {
  .dark .kt-radio {
    &[aria-invalid="true"] {
      border-color: var(--destructive);
    }
    &[aria-invalid="true"] {
      --tw-ring-color: var(--destructive);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);
      }
    }
  }
}
@layer components {
  :root {
    --kt-scrollable-scrollbar-size: 5px;
    --kt-scrollable-thumb-color: var(--color-accent);
  }
  .kt-scrollable::-webkit-scrollbar {
    width: var(--kt-scrollable-scrollbar-size);
    height: var(--kt-scrollable-scrollbar-size);
  }
  .kt-scrollable::-webkit-scrollbar-track {
    background: transparent;
  }
  .kt-scrollable::-webkit-scrollbar-thumb {
    background: var(--kt-scrollable-thumb-color);
    border-radius: var(--kt-scrollable-scrollbar-size);
  }
  @-moz-document url-prefix() {
    .kt-scrollable {
      scrollbar-width: thin;
      scrollbar-color: var(--kt-scrollable-thumb-color) transparent;
    }
  }
}
@layer components {
  .kt-select {
    display: flex;
    width: 100%;
    cursor: pointer;
    appearance: none;
    align-items: center;
    gap: calc(var(--spacing) * 2);
    border-style: var(--tw-border-style);
    border-width: 1px;
    border-color: var(--input);
    background-color: var(--background);
    padding-block: calc(var(--spacing) * 0);
    color: var(--foreground);
    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    --tw-shadow-color: rgba(0,0,0,0.05);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-shadow-color: color-mix(in oklab, rgba(0,0,0,0.05) var(--tw-shadow-alpha), transparent);
    }
    transition-property: color,box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
    &::placeholder {
      color: var(--muted-foreground);
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, var(--muted-foreground) 80%, transparent);
      }
    }
    &:focus-visible {
      border-color: var(--ring);
    }
    &:focus-visible {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
    &:focus-visible {
      --tw-ring-color: var(--ring);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--ring) 30%, transparent);
      }
    }
    &:focus-visible {
      --tw-outline-style: none;
      outline-style: none;
    }
    &:disabled {
      cursor: not-allowed;
    }
    &:disabled {
      opacity: 60%;
    }
    &[readonly] {
      opacity: 70%;
    }
    &[aria-invalid="true"] {
      border-color: var(--destructive);
      @supports (color: color-mix(in lab, red, red)) {
        border-color: color-mix(in oklab, var(--destructive) 60%, transparent);
      }
    }
    &[aria-invalid="true"] {
      --tw-ring-color: var(--destructive);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--destructive) 10%, transparent);
      }
    }
    background-repeat: no-repeat;
    background-size: 14px 11px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath fill='none' stroke='%239f9fa9' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3E%3C/svg%3E");
    .dark & {
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath fill='none' stroke='%239f9fa9' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3E%3C/svg%3E");
    }
    &[multiple],	&[size]:not([size='1']) {
      padding-inline-end: var(--btn-default-px);
      background-image: none;
    }
    &:-moz-focusring {
      color: transparent;
      text-shadow: none;
    }
    &.active {
      border-color: var(--ring);
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      --tw-ring-color: var(--ring);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--ring) 30%, transparent);
      }
      --tw-outline-style: none;
      outline-style: none;
    }
    &.disabled {
      cursor: not-allowed;
      opacity: 60%;
    }
  }
  .kt-select-label {
    display: flex;
    align-items: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .kt-select-arrow {
    margin-inline-start: auto;
    svg {
      width: calc(var(--spacing) * 4);
      height: calc(var(--spacing) * 4);
      color: var(--muted-foreground);
    }
  }
  .kt-select-search {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
    border-color: var(--border);
    padding-inline: calc(var(--spacing) * 4);
    padding-block: calc(var(--spacing) * 1);
    .kt-input {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }
  .kt-select-dropdown {
    border-radius: calc(var(--radius) - 2px);
    border-style: var(--tw-border-style);
    border-width: 1px;
    border-color: var(--border);
    background-color: var(--popover);
    color: var(--popover-foreground);
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    --tw-shadow-color: rgba(0,0,0,0.05);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-shadow-color: color-mix(in oklab, rgba(0,0,0,0.05) var(--tw-shadow-alpha), transparent);
    }
  }
  .kt-select-options {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 0.5) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 0.5) * calc(1 - var(--tw-space-y-reverse)));
    }
    padding: calc(var(--spacing) * 1);
  }
  .kt-select-wrapper {
    width: 100%;
    &.disabled {
      pointer-events: none;
      opacity: 60%;
    }
  }
  .kt-select-option {
    display: flex;
    flex-grow: 1;
    cursor: pointer;
    align-items: center;
    gap: calc(var(--spacing) * 2.5);
    border-radius: calc(var(--radius) - 2px);
    padding-inline: calc(var(--spacing) * 2.5);
    padding-block: calc(var(--spacing) * 1.75);
    text-align: start;
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    &:hover {
      @media (hover: hover) {
        background-color: var(--accent);
      }
    }
    &:hover {
      @media (hover: hover) {
        color: var(--accent-foreground);
      }
    }
    &[aria-disabled=true],	&.disabled {
      pointer-events: none;
      opacity: 60%;
    }
    &.focused,	&.hover,	&.highlighted {
      background-color: var(--accent);
      color: var(--accent-foreground);
    }
  }
  .kt-select-option-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .kt-select-group {
    padding-block: calc(var(--spacing) * 1);
  }
  .kt-select-group-header {
    padding-inline: calc(var(--spacing) * 3);
    padding-block: calc(var(--spacing) * 1);
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
    color: var(--muted-foreground);
    text-transform: uppercase;
  }
  .kt-select-placeholder {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .kt-select-display:not([data-multiple=true]) {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
@layer components {
  .kt-select {
    height: calc(var(--spacing) * 8.5);
    gap: calc(var(--spacing) * 1);
    border-radius: calc(var(--radius) - 2px);
    padding-inline-start: calc(var(--spacing) * 3);
    padding-inline-end: calc(var(--spacing) * 6);
    font-size: 0.8125rem;
    --tw-leading: var(--text-sm--line-height);
    line-height: var(--text-sm--line-height);
    background-position: right 0.5rem center;
    &[data-multiple=true] {
      height: auto;
      min-height: calc(var(--spacing) * 8.5);
      flex-wrap: wrap;
      padding-block: calc(var(--spacing) * 1.5);
      background-position: right 0.5rem top 0.675rem;
    }
  }
  .kt-select-sm {
    height: calc(var(--spacing) * 7);
    gap: calc(var(--spacing) * 1);
    border-radius: calc(var(--radius) - 2px);
    padding-inline-start: calc(var(--spacing) * 2.5);
    padding-inline-end: calc(var(--spacing) * 5);
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
    background-position: right 0.5rem center;
    &[data-multiple=true] {
      height: auto;
      min-height: calc(var(--spacing) * 7);
      background-position: right 0.5rem top 0.575rem;
    }
  }
  .kt-select-lg {
    height: calc(var(--spacing) * 10);
    gap: calc(var(--spacing) * 1.5);
    border-radius: calc(var(--radius) - 2px);
    padding-block: calc(var(--spacing) * 1);
    padding-inline-start: calc(var(--spacing) * 4);
    padding-inline-end: calc(var(--spacing) * 8);
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    background-position: right 0.6rem center;
    &[data-multiple=true] {
      height: auto;
      min-height: calc(var(--spacing) * 10);
      padding-block: calc(var(--spacing) * 2);
      background-position: right 0.6rem top 0.85rem;
    }
  }
}
@layer components {
  [dir='rtl'] {
    .kt-select {
      background-position: left 0.5rem center;
      &[data-multiple=true] {
        background-position: left 0.5rem top 0.675rem;
      }
    }
    .kt-select-sm {
      background-position: left 0.5rem center;
      &[data-multiple=true] {
        background-position: left 0.5rem top 0.575rem;
      }
    }
    .kt-select-lg {
      background-position: left 0.75rem center;
      &[data-multiple=true] {
        background-position: left 0.75rem top 0.85rem;
      }
    }
  }
}
@layer components {
  .dark .kt-input {
    &[aria-invalid="true"] {
      border-color: var(--destructive);
    }
    &[aria-invalid="true"] {
      --tw-ring-color: var(--destructive);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);
      }
    }
  }
}
@layer components {
  .kt-switch {
    position: relative;
    display: inline-flex;
    flex-shrink: 0;
    cursor: pointer;
    appearance: none;
    background-color: var(--input);
    &:focus-visible {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
    &:focus-visible {
      --tw-ring-color: var(--ring);
    }
    &:focus-visible {
      --tw-ring-offset-width: 2px;
      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    }
    &:focus-visible {
      --tw-ring-offset-color: var(--background);
    }
    &:focus-visible {
      --tw-outline-style: none;
      outline-style: none;
      @media (forced-colors: active) {
        outline: 2px solid transparent;
        outline-offset: 2px;
      }
    }
    &:disabled {
      cursor: not-allowed;
    }
    &:disabled {
      opacity: 50%;
    }
    &[aria-invalid="true"] {
      border-style: var(--tw-border-style);
      border-width: 1px;
    }
    &[aria-invalid="true"] {
      border-color: var(--destructive);
      @supports (color: color-mix(in lab, red, red)) {
        border-color: color-mix(in oklab, var(--destructive) 60%, transparent);
      }
    }
    &[aria-invalid="true"] {
      --tw-ring-color: var(--destructive);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--destructive) 10%, transparent);
      }
    }
    &:before {
      pointer-events: none;
      position: absolute;
      inset-inline-start: calc(var(--spacing) * 0);
      top: calc(1/2 * 100%);
      display: block;
      --tw-translate-x: calc(var(--spacing) * 1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
      --tw-translate-y: calc(calc(1/2 * 100%) * -1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
      border-radius: calc(infinity * 1px);
      background-color: var(--color-white);
      --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      transition-property: transform, translate, scale, rotate;
      transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
      transition-duration: var(--tw-duration, var(--default-transition-duration));
      --tw-content: "";
      content: var(--tw-content);
    }
    &:checked,	&[aria-checked='true'] {
      background-color: var(--primary);
    }
  }
  .kt-switch {
    height: calc(var(--spacing) * 5);
    width: calc(var(--spacing) * 7.5);
    border-radius: calc(infinity * 1px);
    &:before {
      width: calc(var(--spacing) * 3);
      height: calc(var(--spacing) * 3);
    }
    &:checked,	&[aria-checked='true'] {
      &:before {
        --tw-translate-x: calc(var(--spacing) * 3.5);
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
    }
  }
  .kt-switch-sm {
    height: calc(var(--spacing) * 4.5);
    width: calc(var(--spacing) * 6.5);
    border-radius: calc(infinity * 1px);
    &:before {
      width: calc(var(--spacing) * 2.5);
      height: calc(var(--spacing) * 2.5);
    }
    &:checked,	&[aria-checked='true'] {
      &:before {
        --tw-translate-x: calc(var(--spacing) * 3);
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
    }
  }
  .kt-switch-lg {
    height: calc(var(--spacing) * 5.5);
    width: calc(var(--spacing) * 8.5);
    border-radius: calc(infinity * 1px);
    &:before {
      width: calc(var(--spacing) * 3.5);
      height: calc(var(--spacing) * 3.5);
    }
    &:checked,	&[aria-checked='true'] {
      &:before {
        --tw-translate-x: calc(var(--spacing) * 4);
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
    }
  }
}
@layer components {
  [dir='rtl'] .kt-switch {
    &:before {
      --tw-translate-x: calc(var(--spacing) * -1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
    &:checked,	&[aria-checked='true'] {
      &:before {
        --tw-translate-x: calc(var(--spacing) * -3.5);
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
    }
    &.kt-switch-sm {
      &:checked,	&[aria-checked='true'] {
        &:before {
          --tw-translate-x: calc(var(--spacing) * -3);
          translate: var(--tw-translate-x) var(--tw-translate-y);
        }
      }
    }
    &.kt-switch-lg {
      &:checked,	&[aria-checked='true'] {
        &:before {
          --tw-translate-x: calc(var(--spacing) * -4);
          translate: var(--tw-translate-x) var(--tw-translate-y);
        }
      }
    }
  }
}
@layer components {
  .dark .kt-switch {
    &[aria-invalid="true"] {
      border-color: var(--destructive);
    }
    &[aria-invalid="true"] {
      --tw-ring-color: var(--destructive);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);
      }
    }
  }
}
@layer components {
  .kt-tabs.kt-tabs-line {
    display: flex;
    align-items: center;
    gap: calc(var(--spacing) * 7);
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
    border-color: var(--border);
  }
  .kt-tabs.kt-tabs-line .kt-tab-toggle {
    display: inline-flex;
    cursor: pointer;
    align-items: center;
    gap: calc(var(--spacing) * 2);
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 2px;
    border-bottom-color: transparent;
    padding-block: calc(var(--spacing) * 2);
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
    color: var(--secondary-foreground);
    &:hover {
      @media (hover: hover) {
        color: var(--primary);
      }
    }
    &[data-kt-tab-toggle].active {
      border-color: var(--primary);
    }
    [data-kt-tab-toggle].active & {
      border-color: var(--primary);
    }
    [data-kt-tabs-initialized]	[data-kt-dropdown-initialized]:has([data-kt-tab-toggle].active)	& {
      border-color: var(--primary);
    }
    &[data-kt-tab-toggle].active {
      color: var(--primary);
    }
    [data-kt-tab-toggle].active & {
      color: var(--primary);
    }
    [data-kt-tabs-initialized]	[data-kt-dropdown-initialized]:has([data-kt-tab-toggle].active)	& {
      color: var(--primary);
    }
    svg {
      width: calc(var(--spacing) * 3.5);
      height: calc(var(--spacing) * 3.5);
      flex-shrink: 0;
      color: var(--muted-foreground);
      &[data-kt-tab-toggle].active {
        color: var(--primary);
      }
      [data-kt-tab-toggle].active & {
        color: var(--primary);
      }
      [data-kt-tabs-initialized]	[data-kt-dropdown-initialized]:has([data-kt-tab-toggle].active)	& {
        color: var(--primary);
      }
    }
    &:hover {
      svg {
        color: var(--primary);
      }
    }
  }
}
@layer components {
  .kt-textarea {
    width: 100%;
    border-style: var(--tw-border-style);
    border-width: 1px;
    border-color: var(--input);
    background-color: var(--background);
    color: var(--foreground);
    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    --tw-shadow-color: rgba(0,0,0,0.05);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-shadow-color: color-mix(in oklab, rgba(0,0,0,0.05) var(--tw-shadow-alpha), transparent);
    }
    transition-property: color,box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
    &::placeholder {
      color: var(--muted-foreground);
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, var(--muted-foreground) 80%, transparent);
      }
    }
    &:focus-visible {
      border-color: var(--ring);
    }
    &:focus-visible {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
    &:focus-visible {
      --tw-ring-color: var(--ring);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--ring) 30%, transparent);
      }
    }
    &:focus-visible {
      --tw-outline-style: none;
      outline-style: none;
    }
    &:disabled {
      cursor: not-allowed;
    }
    &:disabled {
      opacity: 60%;
    }
    &[readonly] {
      cursor: not-allowed;
    }
    &[readonly] {
      background-color: var(--muted);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--muted) 80%, transparent);
      }
    }
    &[aria-invalid="true"] {
      border-color: var(--destructive);
      @supports (color: color-mix(in lab, red, red)) {
        border-color: color-mix(in oklab, var(--destructive) 60%, transparent);
      }
    }
    &[aria-invalid="true"] {
      --tw-ring-color: var(--destructive);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--destructive) 10%, transparent);
      }
    }
  }
  .kt-textarea {
    border-radius: calc(var(--radius) - 2px);
    padding: calc(var(--spacing) * 3);
    font-size: 0.8125rem;
    --tw-leading: var(--text-sm--line-height);
    line-height: var(--text-sm--line-height);
  }
  .kt-textarea-lg {
    border-radius: calc(var(--radius) - 2px);
    padding: calc(var(--spacing) * 4);
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }
  .kt-textarea-sm {
    border-radius: calc(var(--radius) - 2px);
    padding: calc(var(--spacing) * 2.5);
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }
}
@layer components {
  .dark .kt-textarea {
    &[aria-invalid="true"] {
      border-color: var(--destructive);
    }
    &[aria-invalid="true"] {
      --tw-ring-color: var(--destructive);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);
      }
    }
  }
}
@layer components {
  .kt-tooltip {
    border-radius: calc(var(--radius) - 2px);
    background-color: var(--mono);
    padding: calc(var(--spacing) * 1.5);
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
    color: var(--mono-foreground);
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    --tw-shadow-color: rgba(0,0,0,0.05);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-shadow-color: color-mix(in oklab, rgba(0,0,0,0.05) var(--tw-shadow-alpha), transparent);
    }
    &:is(.dark *) {
      border-style: var(--tw-border-style);
      border-width: 1px;
    }
    &:is(.dark *) {
      border-color: var(--border);
    }
    &:not(.show) {
      display: none;
    }
  }
  .kt-tooltip-light {
    border-style: var(--tw-border-style);
    border-width: 1px;
    border-color: var(--border);
    background-color: var(--popover);
    color: var(--popover-foreground);
  }
}
@layer components {
  .kt-popover {
    border-radius: calc(var(--radius) - 2px);
    border-style: var(--tw-border-style);
    border-width: 1px;
    border-color: var(--border);
    background-color: var(--popover);
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    color: var(--popover-foreground);
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    --tw-shadow-color: rgba(0,0,0,0.05);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-shadow-color: color-mix(in oklab, rgba(0,0,0,0.05) var(--tw-shadow-alpha), transparent);
    }
    &:not(.show) {
      display: none;
    }
  }
  .kt-popover-header {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
    border-color: var(--border);
    padding-inline: calc(var(--spacing) * 2.5);
    padding-block: calc(var(--spacing) * 2);
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
    color: var(--mono);
  }
  .kt-popover-content {
    padding-inline: calc(var(--spacing) * 2.5);
    padding-block: calc(var(--spacing) * 2);
  }
}
@layer components {
  .kt-table-wrapper {
    position: relative;
    width: 100%;
    overflow: auto;
  }
  .kt-table {
    width: 100%;
    border-collapse: collapse;
    text-align: left;
    vertical-align: bottom;
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    color: var(--foreground);
    caption-side: bottom;
    tr {
      border-bottom-style: var(--tw-border-style);
      border-bottom-width: 1px;
      border-color: var(--border);
    }
    caption {
      margin-top: calc(var(--spacing) * 4);
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
      color: var(--muted-foreground);
    }
    thead {
      & tr {
        border-bottom-style: var(--tw-border-style);
        border-bottom-width: 1px;
      }
      th {
        height: calc(var(--spacing) * 10);
        background-color: var(--muted);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--muted) 40%, transparent);
        }
        padding-inline: calc(var(--spacing) * 4);
        text-align: left;
        vertical-align: middle;
        --tw-font-weight: var(--font-weight-normal);
        font-weight: var(--font-weight-normal);
        color: var(--secondary-foreground);
        &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
          text-align: right;
        }
        &:has([role=checkbox]) {
          padding-inline-end: calc(var(--spacing) * 0);
        }
      }
    }
    tbody {
      & tr:last-child {
        border-style: var(--tw-border-style);
        border-width: 0px;
      }
      td {
        padding-inline: calc(var(--spacing) * 4);
        padding-block: calc(var(--spacing) * 3);
        vertical-align: middle;
        &:has([role=checkbox]) {
          padding-inline-end: calc(var(--spacing) * 0);
        }
      }
    }
    tfoot {
      border-top-style: var(--tw-border-style);
      border-top-width: 1px;
      background-color: var(--muted);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--muted) 50%, transparent);
      }
      --tw-font-weight: var(--font-weight-medium);
      font-weight: var(--font-weight-medium);
      &:last-child {
        &>tr {
          border-bottom-style: var(--tw-border-style);
          border-bottom-width: 0px;
        }
      }
      th {
        height: calc(var(--spacing) * 10);
        padding-inline: calc(var(--spacing) * 4);
        text-align: left;
        vertical-align: middle;
        --tw-font-weight: var(--font-weight-normal);
        font-weight: var(--font-weight-normal);
        color: var(--secondary-foreground);
        &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
          text-align: right;
        }
        &:has([role=checkbox]) {
          padding-inline-end: calc(var(--spacing) * 0);
        }
      }
    }
    td,	th {
      input[type=checkbox] {
        vertical-align: inherit;
      }
    }
  }
  [data-kt-datatable-table],	.kt-table-highlight {
    tr {
      &.checked {
        background-color: var(--muted);
      }
      &:has(td):hover {
        background-color: var(--muted);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--muted) 50%, transparent);
        }
      }
    }
  }
  .kt-table-col {
    display: inline-flex;
    align-items: center;
    gap: 0.35rem;
    cursor: pointer;
  }
  .kt-table-col-label {
    display: inline-flex;
    align-items: center;
    flex-wrap: nowrap;
    white-space: nowrap;
    gap: 0.35rem;
  }
  .kt-table-col-sort {
    display: inline-flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 0.975rem;
    width: 0.975rem;
    gap: 0.125rem;
    line-height: 1;
    &:before {
      display: inline-block;
      content: '';
      height: 0.25rem;
      width: 0.438rem;
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='5' viewBox='0 0 8 5' fill='none'%3E%3Cpath d='M1.08333 4.83333C0.908333 4.83333 0.791667 4.775 0.675 4.65833C0.441667 4.425 0.441667 4.075 0.675 3.84167L3.59167 0.925C3.825 0.691667 4.175 0.691667 4.40833 0.925L7.325 3.84167C7.55833 4.075 7.55833 4.425 7.325 4.65833C7.09167 4.89167 6.74167 4.89167 6.50833 4.65833L4 2.15L1.49167 4.65833C1.375 4.775 1.25833 4.83333 1.08333 4.83333Z' fill='%2378829D'/%3E%3C/svg%3E");
    }
    &:after {
      display: inline-block;
      content: '';
      height: 0.25rem;
      width: 0.438rem;
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='5' viewBox='0 0 8 5' fill='none'%3E%3Cpath d='M4 4.24984C3.825 4.24984 3.70833 4.1915 3.59167 4.07484L0.675 1.15817C0.441667 0.924838 0.441667 0.574837 0.675 0.341504C0.908333 0.108171 1.25833 0.108171 1.49167 0.341504L4 2.84984L6.50833 0.341504C6.74167 0.108171 7.09167 0.108171 7.325 0.341504C7.55833 0.574837 7.55833 0.924838 7.325 1.15817L4.40833 4.07484C4.29167 4.1915 4.175 4.24984 4 4.24984Z' fill='%2378829D'/%3E%3C/svg%3E");
    }
    [aria-sort='asc'] & {
      &:before {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='5' viewBox='0 0 8 5' fill='none'%3E%3Cpath d='M1.08333 4.83333C0.908333 4.83333 0.791667 4.775 0.675 4.65833C0.441667 4.425 0.441667 4.075 0.675 3.84167L3.59167 0.925C3.825 0.691667 4.175 0.691667 4.40833 0.925L7.325 3.84167C7.55833 4.075 7.55833 4.425 7.325 4.65833C7.09167 4.89167 6.74167 4.89167 6.50833 4.65833L4 2.15L1.49167 4.65833C1.375 4.775 1.25833 4.83333 1.08333 4.83333Z' fill='%234B5675'/%3E%3C/svg%3E");
      }
      &:after {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='5' viewBox='0 0 8 5' fill='none'%3E%3Cpath d='M4 4.24984C3.825 4.24984 3.70833 4.1915 3.59167 4.07484L0.675 1.15817C0.441667 0.924838 0.441667 0.574837 0.675 0.341504C0.908333 0.108171 1.25833 0.108171 1.49167 0.341504L4 2.84984L6.50833 0.341504C6.74167 0.108171 7.09167 0.108171 7.325 0.341504C7.55833 0.574837 7.55833 0.924838 7.325 1.15817L4.40833 4.07484C4.29167 4.1915 4.175 4.24984 4 4.24984Z' fill='%23C9CEDA'/%3E%3C/svg%3E");
      }
    }
    [aria-sort='desc'] & {
      &:before {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='5' viewBox='0 0 8 5' fill='none'%3E%3Cpath d='M1.08333 4.83333C0.908333 4.83333 0.791667 4.775 0.675 4.65833C0.441667 4.425 0.441667 4.075 0.675 3.84167L3.59167 0.925C3.825 0.691667 4.175 0.691667 4.40833 0.925L7.325 3.84167C7.55833 4.075 7.55833 4.425 7.325 4.65833C7.09167 4.89167 6.74167 4.89167 6.50833 4.65833L4 2.15L1.49167 4.65833C1.375 4.775 1.25833 4.83333 1.08333 4.83333Z' fill='%23C9CEDA'/%3E%3C/svg%3E");
      }
      &:after {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='5' viewBox='0 0 8 5' fill='none'%3E%3Cpath d='M4 4.24984C3.825 4.24984 3.70833 4.1915 3.59167 4.07484L0.675 1.15817C0.441667 0.924838 0.441667 0.574837 0.675 0.341504C0.908333 0.108171 1.25833 0.108171 1.49167 0.341504L4 2.84984L6.50833 0.341504C6.74167 0.108171 7.09167 0.108171 7.325 0.341504C7.55833 0.574837 7.55833 0.924838 7.325 1.15817L4.40833 4.07484C4.29167 4.1915 4.175 4.24984 4 4.24984Z' fill='%234B5675'/%3E%3C/svg%3E");
      }
    }
  }
}
@layer components {
  .kt-table-border {
    border-style: var(--tw-border-style);
    border-width: 1px;
    border-color: var(--border);
    & td,	& th {
      border-inline-end-style: var(--tw-border-style);
      border-inline-end-width: 1px;
      border-color: var(--border);
      &:last-child {
        border-inline-end-style: var(--tw-border-style);
        border-inline-end-width: 0px;
      }
    }
  }
  .kt-table-border-s {
    border-inline-start-style: var(--tw-border-style);
    border-inline-start-width: 1px;
    border-color: var(--border);
  }
  .kt-table-border-e {
    border-inline-end-style: var(--tw-border-style);
    border-inline-end-width: 1px;
    border-color: var(--border);
  }
  .kt-table-border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
    border-color: var(--border);
  }
  .kt-table-border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
    border-color: var(--border);
  }
}
@layer components {
  [dir='rtl'] .kt-table {
    text-align: right;
  }
}
@layer components {
  .kt-toggle-group {
    display: inline-flex;
    align-items: center;
    background-color: var(--background);
    --tw-leading: 1;
    line-height: 1;
  }
  .kt-toggle-group {
    .kt-btn {
      flex-grow: 1;
      border-style: var(--tw-border-style);
      border-width: 1px;
      border-inline-end-style: var(--tw-border-style);
      border-inline-end-width: 0px;
      border-color: var(--border);
      background-color: transparent;
      color: var(--accent-foreground);
      &:last-child {
        border-inline-end-style: var(--tw-border-style);
        border-inline-end-width: 1px;
        border-color: var(--border);
      }
      &:not(:first-child) {
        border-start-start-radius: 0;
        border-end-start-radius: 0;
      }
      &:not(:last-child) {
        border-start-end-radius: 0;
        border-end-end-radius: 0;
      }
      &:not(:first-child):not(:last-child) {
        border-radius: 0;
      }
      svg,	i {
        color: var(--muted-foreground);
      }
      input[type='checkbox'],	input[type='radio'] {
        display: none;
      }
      &:hover,	&:focus,	&:active,	&:has(input:checked),	&.active {
        background-color: var(--accent);
        color: var(--accent-foreground);
        svg,	i {
          color: var(--accent-foreground);
        }
      }
    }
  }
}
@layer components {
  .kt-separator {
    height: 1px;
    width: 100%;
    flex-shrink: 0;
    background-color: var(--border);
  }
  .kt-separator-vertical {
    height: 100%;
    width: 1px;
  }
}
@layer components {
  .kt-progress {
    position: relative;
    height: calc(var(--spacing) * 1);
    width: 100%;
    overflow: hidden;
    border-radius: calc(infinity * 1px);
    background-color: var(--secondary);
  }
  .kt-progress-indicator {
    height: 100%;
    width: 100%;
    flex: 1;
    transform: translateX(calc(100 - var(--progress-value)));
    border-radius: calc(infinity * 1px);
    background-color: var(--primary);
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .kt-progress-primary {
    .kt-progress-indicator {
      background-color: var(--primary);
    }
  }
  .kt-progress-success {
    .kt-progress-indicator {
      background-color: var(--color-green-500);
    }
  }
  .kt-progress-warning {
    .kt-progress-indicator {
      background-color: var(--color-yellow-500);
    }
  }
  .kt-progress-info {
    .kt-progress-indicator {
      background-color: var(--color-violet-500);
    }
  }
  .kt-progress-destructive {
    .kt-progress-indicator {
      background-color: var(--destructive);
    }
  }
  .kt-progress-mono {
    .kt-progress-indicator {
      background-color: var(--mono);
    }
  }
}
@layer components {
  .kt-pagination {
    display: flex;
    align-items: center;
    gap: calc(var(--spacing) * 1);
  }
  .kt-pagination-item {
    display: flex;
    flex-shrink: 0;
    flex-direction: row;
    align-items: center;
    gap: calc(var(--spacing) * 1);
    svg {
      width: calc(var(--spacing) * 4);
      height: calc(var(--spacing) * 4);
      color: var(--muted-foreground);
    }
  }
  .kt-pagination-ellipsis {
    display: flex;
    height: calc(var(--spacing) * 9);
    width: calc(var(--spacing) * 9);
    align-items: center;
    justify-content: center;
    svg {
      width: calc(var(--spacing) * 4);
      height: calc(var(--spacing) * 4);
      color: var(--muted-foreground);
    }
  }
}
@layer components {
  .kt-skeleton {
    animation: var(--animate-pulse);
    border-radius: calc(var(--radius) - 2px);
    background-color: var(--accent);
  }
}
@layer components {
  .kt-kbd {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: calc(var(--radius) - 2px);
    font-family: var(--font-mono);
  }
  .kt-kbd {
    border-style: var(--tw-border-style);
    border-width: 1px;
    border-color: var(--border);
    background-color: var(--accent);
    color: var(--accent-foreground);
  }
  .kt-kbd-outline {
    border-style: var(--tw-border-style);
    border-width: 1px;
    border-color: var(--input);
    background-color: transparent;
    color: var(--accent-foreground);
  }
  .kt-kbd {
    height: calc(var(--spacing) * 7);
    min-width: calc(var(--spacing) * 7);
    padding-inline: calc(var(--spacing) * 1.5);
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
    & svg {
      width: calc(var(--spacing) * 3.5);
      height: calc(var(--spacing) * 3.5);
    }
  }
  .kt-kbd-xs {
    height: calc(var(--spacing) * 5);
    min-width: calc(var(--spacing) * 5);
    padding-inline: calc(var(--spacing) * 1);
    font-size: 0.6875rem;
    --tw-leading: 0.75rem;
    line-height: 0.75rem;
    & svg {
      width: calc(var(--spacing) * 3);
      height: calc(var(--spacing) * 3);
    }
  }
  .kt-kbd-sm {
    height: calc(var(--spacing) * 6);
    min-width: calc(var(--spacing) * 6);
    padding-inline: calc(var(--spacing) * 1);
    font-size: 0.75rem;
    --tw-leading: 0.75rem;
    line-height: 0.75rem;
    & svg {
      width: calc(var(--spacing) * 3);
      height: calc(var(--spacing) * 3);
    }
  }
}
@layer components {
  .kt-breadcrumb {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: calc(var(--spacing) * 1.5);
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
    overflow-wrap: break-word;
    color: var(--muted-foreground);
  }
  .kt-breadcrumb-item {
    display: inline-flex;
    align-items: center;
    gap: calc(var(--spacing) * 1.5);
  }
  .kt-breadcrumb-link {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
    &:hover {
      @media (hover: hover) {
        color: var(--foreground);
      }
    }
    svg {
      width: calc(var(--spacing) * 4);
      height: calc(var(--spacing) * 4);
    }
  }
  .kt-breadcrumb-page {
    --tw-font-weight: var(--font-weight-normal);
    font-weight: var(--font-weight-normal);
    color: var(--foreground);
  }
  .kt-breadcrumb-separator {
    svg {
      width: calc(var(--spacing) * 3.5);
      height: calc(var(--spacing) * 3.5);
    }
  }
  .kt-breadcrumb-ellipsis {
    svg {
      width: calc(var(--spacing) * 4);
      height: calc(var(--spacing) * 4);
    }
  }
}
@layer components {
  .kt-toast-container {
    position: fixed;
    z-index: 9999;
  }
  .kt-toast {
    pointer-events: auto;
    position: fixed;
    z-index: 9999;
    width: calc(var(--spacing) * 76);
    max-width: 95%;
    overflow: hidden;
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    opacity: 0;
    animation: kt-toast-in 0.28s cubic-bezier(.4,0,.2,1) forwards;
    transition: top 0.28s cubic-bezier(.4,0,.2,1), opacity 0.28s cubic-bezier(.4,0,.2,1);
    &.kt-toast-top-end {
      inset-inline-end: calc(var(--spacing) * 0);
      top: calc(var(--spacing) * 0);
      bottom: auto;
    }
    &.kt-toast-top-center {
      inset-inline-start: calc(1/2 * 100%);
      top: calc(var(--spacing) * 0);
      bottom: auto;
      --tw-translate-x: calc(calc(1/2 * 100%) * -1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
    &.kt-toast-top-start {
      inset-inline-start: calc(var(--spacing) * 0);
      top: calc(var(--spacing) * 0);
      bottom: auto;
    }
    &.kt-toast-bottom-end {
      inset-inline-end: calc(var(--spacing) * 0);
      top: auto;
      bottom: calc(var(--spacing) * 0);
    }
    &.kt-toast-bottom-center {
      inset-inline-start: calc(1/2 * 100%);
      top: auto;
      bottom: calc(var(--spacing) * 0);
      --tw-translate-x: calc(calc(1/2 * 100%) * -1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
    &.kt-toast-bottom-start {
      inset-inline-start: calc(var(--spacing) * 0);
      top: auto;
      bottom: calc(var(--spacing) * 0);
    }
  }
  .kt-toast-progress {
    position: fixed;
    inset-inline-start: calc(var(--spacing) * 0);
    bottom: calc(var(--spacing) * 0);
    height: 3px;
    width: 100%;
    background-color: var(--primary);
    transform-origin: left;
    animation: kt-toast-progress-line linear forwards;
  }
}
@layer components {
  [dir='rtl'] .kt-toast-progress {
    transform-origin: right;
  }
}
@layer components {
  @keyframes kt-toast-in {
    from {
      opacity: 0;
      transform: translateY(-24px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  @keyframes kt-toast-out {
    from {
      opacity: 1;
    }
    to {
      opacity: 0;
    }
  }
  @keyframes kt-toast-progress-line {
    from {
      transform: scaleX(1);
    }
    to {
      transform: scaleX(0);
    }
  }
}
@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-rotate-x {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-y {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-z {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-x {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-y {
  syntax: "*";
  inherits: false;
}
@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-leading {
  syntax: "*";
  inherits: false;
}
@property --tw-font-weight {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0px;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false;
}
@property --tw-duration {
  syntax: "*";
  inherits: false;
}
@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-tracking {
  syntax: "*";
  inherits: false;
}
@property --tw-ease {
  syntax: "*";
  inherits: false;
}
@property --tw-content {
  syntax: "*";
  inherits: false;
  initial-value: "";
}
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
@keyframes pulse {
  50% {
    opacity: 0.5;
  }
}
@layer properties {
  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {
    *, ::before, ::after, ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-space-x-reverse: 0;
      --tw-border-style: solid;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-duration: initial;
      --tw-space-y-reverse: 0;
      --tw-tracking: initial;
      --tw-ease: initial;
      --tw-content: "";
    }
  }
}
