@using BuddyPunch.Web.App
@using BuddyPunch.Web.App.Helpers
@using BuddyPunch.Web.App.ViewModel
@model BuddyPunch.Web.App.ViewModel.TopNavViewModel
@{
    bool alwaysOnGPSEnabled = Model.SideBarViewModel.IsAlwaysOnGPSFeatureEligible && Model.SideBarViewModel.IsAlwaysOnGPSFeatureOn;
}

@helper TopMenuItem(string url, string icon, string content, string target = "")
{
    <div class="kt-menu-item">
        <a href="@url" target="@target" class="kt-menu-link">
            @if (!string.IsNullOrWhiteSpace(icon))
            {
                <span class="kt-menu-icon">
                    <i class="ki-filled @icon">
                    </i>
                </span>
            }
            <span class="kt-menu-title">
                @content
            </span>
        </a>
    </div>
}

@helper SubMenuItem(string content, string action, string controller, HtmlString badge = null, string icon = null)
{
    var urlHelper = new UrlHelper(this.ViewContext.RequestContext);
    var url = urlHelper.Action(action, controller);

    @: @SubMenuItem(url, content, icon: icon, badge: null)
}

@helper SubMenuItem(string url, string content, HtmlString badge = null, string icon = null)
{
    <div class="kt-menu-item">
        <a class="kt-menu-link" href="@url" tabindex="0">
            @if (!string.IsNullOrWhiteSpace(icon))
            {
                <span class="kt-menu-icon">
                    <i class="@icon"></i>
                </span>
            }
            <span class="kt-menu-title grow-0">
                @content
            </span>
            @if (badge != null)
            {
                <span class="kt-menu-badge">
                    @badge
                </span>
            }
        </a>
    </div>
}


@helper SubMenuItemPayrollDocuments(string content)
{
    <div class="kt-menu-item" data-kt-menu-item-placement="right-start" data-kt-menu-item-placement-rtl="left-start" data-kt-menu-item-toggle="dropdown" data-kt-menu-item-trigger="click|lg:hover">
        <div class="kt-menu-link">
            <span class="kt-menu-icon">
                <i class="ki-filled ki-folder"></i>
            </span>
            <span class="kt-menu-title">
                @content
            </span>
            <span class="kt-menu-arrow">
                <i class="ki-filled ki-right text-xs rtl:transform rtl:rotate-180">
                </i>
            </span>
        </div>
        <div class="kt-menu-dropdown kt-menu-default w-full max-w-[175px] lg:max-w-[220px]">
            <div class="kt-menu-item">
                <a class="kt-menu-link" href="@Url.Action("FilingAuthorizationDocuments", "Payroll")">
                    <span class="kt-menu-icon">
                        <i class="ki-filled ki-tablet-ok"></i>
                    </span>
                    <span class="kt-menu-title grow-0">Filing Authorizations</span>
                </a>
            </div>
            <div class="kt-menu-item">
                <a class="kt-menu-link" href="@Url.Action("CompanyTaxDocuments", "Payroll")">
                    <span class="kt-menu-icon">
                        <i class="ki-filled ki-cheque"></i>
                    </span>
                    <span class="kt-menu-title grow-0">Tax Documents</span>
                </a>
            </div>
        </div>
    </div>
}


@helper SubMenuItemPayrollReports(string content)
{
    <div class="kt-menu-item" data-kt-menu-item-placement="right-start" data-kt-menu-item-placement-rtl="left-start" data-kt-menu-item-toggle="dropdown" data-kt-menu-item-trigger="click|lg:hover">
        <div class="kt-menu-link">
            <span class="kt-menu-icon">
                <i class="ki-filled ki-chart-pie-3"></i>
            </span>
            <span class="kt-menu-title">
                @content
            </span>
            <span class="kt-menu-arrow">
                <i class="ki-filled ki-right text-xs rtl:transform rtl:rotate-180">
                </i>
            </span>
        </div>
        <div class="kt-menu-dropdown kt-menu-default w-full max-w-[175px] lg:max-w-[220px]">
            @foreach (var item in new[]
            {
                new { Text = "Payroll Summary", Action = "PayrollSummary", Icon = "ki-filled ki-tablet-text-down" },
                new { Text = "Payroll Journal", Action = "PayrollJournal", Icon = "ki-filled ki-message-edit" },
                new { Text = "Payroll Cash Requirement", Action = "PayrollCashRequirementReport", Icon = "ki-filled ki-dollar" },
                new { Text = "Contractor Payments", Action = "ContractorPayments", Icon = "ki-filled ki-address-book" },
                new { Text = "W2 Preview", Action = "W2Preview", Icon = "ki-filled ki-tablet" },
                new { Text = "W4 Exempt Status", Action = "W4ExemptStatus", Icon = "ki-filled ki-tablet" }
            })
            {
                <div class="kt-menu-item">
                    <a class="kt-menu-link" href="@Url.Action(item.Action, "Payroll")">
                        <span class="kt-menu-icon">
                            <i class="@item.Icon"></i>
                        </span>
                        <span class="kt-menu-title grow-0">@item.Text</span>
                    </a>
                </div>
            }
        </div>
    </div>
}


@helper SubMenuItemReportBy(string content, string icon)
{
    <div class="kt-menu-item" data-kt-menu-item-placement="right-start" data-kt-menu-item-placement-rtl="left-start" data-kt-menu-item-toggle="dropdown" data-kt-menu-item-trigger="click|lg:hover">
        <div class="kt-menu-link">
            <span class="kt-menu-icon">
                <i class="ki-filled @icon"></i>
            </span>
            <span class="kt-menu-title">
                @content
            </span>
            <span class="kt-menu-arrow">
                <i class="ki-filled ki-right text-xs rtl:transform rtl:rotate-180">
                </i>
            </span>
        </div>
        <div class="kt-menu-dropdown kt-menu-default w-full max-w-[175px] lg:max-w-[220px]">
            <div class="kt-menu-item">
                <a class="kt-menu-link" href="@Url.Action("By", "Report", new { initialGroup = (int)ReportByGroup.Location })">
                    <span class="kt-menu-icon">
                        <i class="ki-filled ki-geolocation"></i>
                    </span>
                    <span class="kt-menu-title grow-0">Location</span>
                </a>
            </div>
            <div class="kt-menu-item">
                <a class="kt-menu-link" href="@Url.Action("By", "Report", new { initialGroup = (int)ReportByGroup.Department })">
                    <span class="kt-menu-icon">
                        <i class="ki-filled ki-element-11"></i>
                    </span>
                    <span class="kt-menu-title grow-0">Department</span>
                </a>
            </div>
            @if (Model.SideBarViewModel.IsScheduleVisible)
            {
                <div class="kt-menu-item">
                    <a class="kt-menu-link" href="@Url.Action("By", "Report", new { initialGroup = (int)ReportByGroup.Position })">
                        <span class="kt-menu-icon">
                            <i class="ki-filled ki-user-square"></i>
                        </span>
                        <span class="kt-menu-title grow-0">Position</span>
                    </a>
                </div>
            }
            @if (Model.SideBarViewModel.PTOEnabled)
            {
                <div class="kt-menu-item">
                    <a class="kt-menu-link" href="@Url.Action("By", "Report", new { initialGroup = 4, IsTimeOffOnly = true })">
                        <span class="kt-menu-icon">
                            <i class="ki-filled ki-calendar-remove"></i>
                        </span>
                        <span class="kt-menu-title grow-0">Time Off</span>
                    </a>
                </div>
            }
        </div>
    </div>
}


@helper SubMenuItemScheduling(string content)
{
    <div class="kt-menu-item" data-kt-menu-item-placement="right-start" data-kt-menu-item-placement-rtl="left-start" data-kt-menu-item-toggle="dropdown" data-kt-menu-item-trigger="click|lg:hover">
        <div class="kt-menu-link">
            <span class="kt-menu-icon">
                <i class="ki-filled ki-calendar-add"></i>
            </span>
            <span class="kt-menu-title">
                @content
            </span>
            <span class="kt-menu-arrow">
                <i class="ki-filled ki-right text-xs rtl:transform rtl:rotate-180">
                </i>
            </span>
        </div>
        <div class="kt-menu-dropdown kt-menu-default w-full max-w-[175px] lg:max-w-[220px]">
            <div class="kt-menu-item">
                <a class="kt-menu-link" href="@Url.Action("EarlyLate", "Report")">
                    <span class="kt-menu-icon">
                        <i class="ki-filled ki-time"></i>
                    </span>
                    <span class="kt-menu-title grow-0">Early / Late</span>
                </a>
            </div>
            <div class="kt-menu-item">
                <a class="kt-menu-link" href="@Url.Action("Absence", "Report")">
                    <span class="kt-menu-icon">
                        <i class="ki-filled ki-calendar-remove"></i>
                    </span>
                    <span class="kt-menu-title grow-0">Absence</span>
                </a>
            </div>
        </div>
    </div>
}


@helper EmptyMenuItem()
{
    <div class="kt-menu-item">
        <a class="kt-menu-link
             bg-background
             text-foreground
             hover:!bg-background
             hover:!text-foreground
             hover:!shadow-none
             cursor-default
             pointer-events-none">
            <span class="kt-menu-icon">
                <i class="ki-filled text-inherit"></i>
            </span>
            <span class="kt-menu-title grow-0 text-inherit"></span>
        </a>
    </div>



}

@helper SubMenuHeader(string title, bool hideOnDesktop = false)
{
    <h3 class="m-menu__heading m-menu__toggle @(hideOnDesktop ? "m--hidden-desktop" : string.Empty)">
        <span class="m-menu__link-text hidden">@title</span>
        <i class="m-menu__ver-arrow la la-angle-right"></i>
    </h3>
}

@helper MainMenuItem(string content, bool isActive, List<HelperResult> subMenuItems, string url = null, HtmlString badge = null)
{
    @: @MainMenuItem(content, isActive, new[] { subMenuItems }, url, badge)
}

@helper MainMenuItem(string content, bool isActive, List<HelperResult>[] subMenuItems, string url = null, HtmlString badge = null, bool showNewLabel = false)
{
    var hasSubmenu = subMenuItems != null && subMenuItems.Length > 0;
    var activeClass = isActive ? "kt-menu-item-active:border-b-mono kt-menu-item-active:text-mono" : "";
    if (hasSubmenu)
    {
        <div class="kt-menu-item"
             data-kt-menu-item-placement="bottom-start"
             data-kt-menu-item-placement-rtl="bottom-end"
             data-kt-menu-item-trigger="click|lg:hover"
             data-kt-menu-item-toggle="accordion|lg:dropdown">
            <div class="kt-menu-link lg:py-2 border-b border-b-transparent kt-menu-item-hover:text-mono kt-menu-item-here:border-b-mono kt-menu-item-here:text-mono @activeClass">
                <span class="kt-menu-title font-medium text-foreground text-sm flex items-center gap-1.5">
                        @content
                        <span class="kt-menu-arrow">
                            <i class="ki-filled ki-down text-[10px] text-secondary-foreground"></i>
                        </span>
                    @if (showNewLabel)
                    {
                        <span class="ml-1 text-xs bg-blue-500 text-white rounded px-1.5 py-0.5">new</span>
                    }
                </span>
                    <span class="kt-menu-arrow flex lg:hidden">
                        <span class="flex kt-menu-item-show:hidden">
                            <i class="ki-filled ki-plus text-xs text-secondary-foreground"></i>
                        </span>
                        <span class="hidden kt-menu-item-show:inline-flex">
                            <i class="ki-filled ki-minus text-xs text-secondary-foreground"></i>
                        </span>
                    </span>
            </div>
                @if (subMenuItems.Length > 1)
                {
                    <div class="kt-menu-dropdown max-w-[90%] gap-0 lg:max-w-[@(300 + 150 * @subMenuItems.Length)px]">
                        <div class="pt-4 pb-2 lg:p-7.5">
                            <div class="grid lg:<EMAIL> gap-5 lg:gap-10">
                                @foreach (var column in subMenuItems)
                                {
                                    <div class="kt-menu kt-menu-default kt-menu-fit flex-col">
                                        <div class="grid lg:grid-cols-1 lg:gap-5">
                                            <div class="flex flex-col gap-0.5">
                                                @foreach (var item in column)
                                                {
                                                    @Html.Raw(item.ToString())
                                                }
                                            </div>
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                }
                else
                {
                    <div class="kt-menu-dropdown kt-menu-default py-2.5 w-full max-w-[220px]">
                        @foreach (var column in subMenuItems)
                        {
                            <div class="kt-menu-item">
                                @foreach (var item in column)
                                {
                                    @Html.Raw(item.ToString())
                                }
                            </div>
                        }
                    </div>

                }
        </div>
    }
    else
    {
        <div class="kt-menu-item"
             data-kt-menu-item-placement="bottom-start"
             data-kt-menu-item-placement-rtl="bottom-end">
            <div class="kt-menu-link lg:py-2 border-b border-b-transparent kt-menu-item-hover:text-mono kt-menu-item-here:border-b-mono kt-menu-item-here:text-mono @activeClass">
                <span class="kt-menu-title font-medium text-foreground text-sm flex items-center gap-1.5">
                        <a href="@url" class="kt-menu-link">
                            @content

                        </a>
                    @if (showNewLabel)
                    {
                        <span class="ml-1 text-xs bg-blue-500 text-white rounded px-1.5 py-0.5">new</span>
                    }
                </span>
            </div>
        </div>
    }

}

<!-- Header -->
<header class="flex items-center transition-[height] shrink-0 bg-background" style="height:48px; font-family:inter" data-kt-sticky="true" data-kt-sticky-class="transition-[height] fixed z-10 top-0 left-0 right-0 shadow-xs backdrop-blur-md bg-background/70 border border-border" data-kt-sticky-name="header" data-kt-sticky-offset="100px" id="header">
    <!-- Container -->
    <div class="kt-container-fixed flex lg:justify-between items-center gap-2.5 relative">
        <!-- Logo -->
        <div class="flex items-center gap-1 lg:w-[400px] grow lg:grow-0">
            <button class="kt-btn kt-btn-icon kt-btn-ghost -ms-2.5 lg:hidden" data-kt-drawer-toggle="#navbar">
                <i class="ki-filled ki-menu">
                </i>
            </button>
            <div class="flex items-center gap-2">
                <a class="flex items-center" href="/">
                    <img class="dark:hidden" src="@Url.Cdn("/content/images/buddypunch_logo_black.svg")" />
                    <img class="hidden dark:inline-block" src="@Url.Cdn("/content/images/buddypunch_logo_white.svg")" />
                </a>

            </div>

        </div>
        <!-- End of Logo -->
        <!-- Mobile Search -->
        <div class="kt-menu kt-menu-default lg:hidden" data-kt-menu="true">
            <div class="kt-menu-item" data-kt-menu-item-offset="0, 0" data-kt-menu-item-placement="bottom-start" data-kt-menu-item-placement-rtl="bottom-end" data-kt-menu-item-toggle="dropdown" data-kt-menu-item-trigger="hover">
                <button class="kt-menu-toggle kt-btn kt-btn-ghost kt-btn-icon">
                    <i class="ki-filled ki-magnifier">
                    </i>
                </button>
                <div class="kt-menu-dropdown w-48 py-2">
                    <div class="kt-input">
                        <i class="ki-filled ki-magnifier">
                        </i>
                        <input class="min-w-0" placeholder="Search" type="text" value="" />
                    </div>
                </div>
            </div>
        </div>
        <!-- End of Mobile Search -->
        <!-- Centered Search Bar -->
        <div class="hidden lg:flex absolute left-1/2 transform -translate-x-1/2">
            @if (Model.SideBarViewModel.IsEmployeesVisible)
            {
                <!--BEGIN: Metronic Autocomplete Search -->
                <div class="kt-input lg:flex lg:w-80 relative h-[28px]">
                    <i class="ki-filled ki-magnifier">
                    </i>
                    <input autocomplete="off"
                           type="text"
                           name="q"
                           class="min-w-0"
                           value=""
                           placeholder="Search Employees"
                           id="kt_quicksearch_input">

                    <!--BEGIN: Search Results Dropdown -->
                    <div class="kt-dropdown-menu w-full max-w-[450px] hidden" id="search-results-dropdown"
                         style="z-index: 103; position: absolute; top: 100%; left: 0; margin-top: 4px; max-height: 400px; background: white; border: 1px solid #e5e7eb; border-radius: 8px; box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);">
                        <div class="kt-dropdown-menu-header">
                            <h3 class="text-sm font-semibold text-foreground px-4 py-3 border-b border-border">
                                <i class="ki-filled ki-profile-circle text-primary me-2"></i>
                                Employee Search Results
                            </h3>
                        </div>
                        <div class="kt-dropdown-menu-body max-h-[320px] overflow-y-auto" id="search-results-content">
                            <!-- Search results will be populated here -->
                            <div class="flex flex-col items-center justify-center py-8 px-4" id="search-empty-state">
                                <div class="flex items-center justify-center w-12 h-12 bg-muted rounded-full mb-3">
                                    <i class="ki-filled ki-magnifier text-muted-foreground text-lg"></i>
                                </div>
                                <div class="text-sm text-muted-foreground text-center">
                                    Start typing to search employees...
                                </div>
                            </div>
                            <div class="flex flex-col items-center justify-center py-8 px-4 hidden" id="search-no-results">
                                <div class="flex items-center justify-center w-12 h-12 bg-muted rounded-full mb-3">
                                    <i class="ki-filled ki-information text-muted-foreground text-lg"></i>
                                </div>
                                <div class="text-sm text-muted-foreground text-center">
                                    No employees found matching your search
                                </div>
                            </div>
                            <div class="flex flex-col items-center justify-center py-8 px-4 hidden" id="search-loading">
                                <div class="flex items-center justify-center w-12 h-12 bg-muted rounded-full mb-3">
                                    <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                                </div>
                                <div class="text-sm text-muted-foreground text-center">
                                    Searching employees...
                                </div>
                            </div>
                        </div>
                        <div class="kt-dropdown-menu-footer border-t border-border px-4 py-3 hidden" id="search-results-footer">
                            <div class="text-xs text-muted-foreground text-center">
                                Press <kbd class="px-1.5 py-0.5 text-xs bg-muted border border-border rounded">Enter</kbd> to view all results
                            </div>
                        </div>
                    </div>
                    <!--END: Search Results Dropdown -->
                </div>
                <!--END: Metronic Autocomplete Search -->
            }
            else
            {
                <div class="kt-input lg:flex lg:w-80">
                    <i class="ki-filled ki-magnifier">
                    </i>
                    <input class="min-w-0" placeholder="Search" type="text" value="" disabled />
                </div>
            }
        </div>
        <!-- End of Centered Search Bar -->
        <!-- Topbar -->
        <div class="flex items-center gap-2 lg:gap-3.5 lg:w-[400px] justify-end">
            <!-- Navs -->
            <div class="hidden lg:flex items-center">
                <!-- Nav -->
                <div class="kt-menu kt-menu-default" data-kt-menu="true">
                    <div class="kt-menu-item" data-kt-menu-item-offset="0,10px" data-kt-menu-item-placement="bottom-end" data-kt-menu-item-placement-rtl="bottom-start" data-kt-menu-item-toggle="dropdown" data-kt-menu-item-trigger="click">
                        <button class="kt-btn kt-btn-ghost kt-btn-icon size-9 rounded-full hover:bg-transparent hover:[&_i]:text-primary">
                            <i class="ki-filled ki-message-question text-lg text-secondary-foreground">
                            </i>
                        </button>
                        <div class="kt-menu-dropdown w-48 py-2">
                            @if (!ViewBag.IsMobileBrowser)
                            {
                                if (Model.SideBarViewModel.IsAdministrator)
                                {
                                    @TopMenuItem("https://docs.buddypunch.com/en/collections/9641667-account-setup", "icon-info", "Getting Started", "_blank")
                                }
                                else if (Model.SideBarViewModel.IsDashboardVisible)
                                {
                                    @TopMenuItem("https://docs.buddypunch.com/en/collections/9641744-manager-help", "icon-info", "Getting Started", "_blank")
                                }
                                else
                                {
                                    @TopMenuItem("https://docs.buddypunch.com/en/collections/9641750-employee-help", "icon-info", "Getting Started", "_blank")
                                }
                            }

                            @TopMenuItem("https://docs.buddypunch.com/", "icon-question", "Knowledge Base", "_blank")

                        </div>
                    </div>
                </div>
                <!-- End of Nav -->
                <div class="border-e border-border h-5 mx-4">
                </div>

            </div>
            <!-- End of Navs -->
            <div class="flex items-center gap-2 me-0.5">
                <!-- User -->
                <div data-kt-dropdown="true" data-kt-dropdown-offset="10px, 10px" data-kt-dropdown-offset-rtl="-20px, 10px" data-kt-dropdown-placement="bottom-end" data-kt-dropdown-placement-rtl="bottom-start" data-kt-dropdown-trigger="click">
                    <button class="kt-btn kt-btn-ghost kt-btn-icon size-9 rounded-full hover:bg-transparent hover:[&_i]:text-primary" data-kt-dropdown-toggle="true">
                        <i class="ki-filled ki-profile-circle text-lg">
                        </i>
                        <span class="kt-menu-arrow">
                            <i class="ki-filled ki-down text-[10px] text-secondary-foreground"></i>
                        </span>
                    </button>
                    <div class="kt-dropdown-menu w-[250px]" data-kt-dropdown-menu="true">
                        <div class="flex items-center justify-between px-2.5 py-1.5 gap-1.5">
                            <div class="flex items-center gap-2">
                                @if (Model.SideBarViewModel.UserViewModel.ProfileMiniImageUrl != null)
                                {
                                    <img alt="" class="size-9 shrink-0 rounded-full border-2 border-green-500" src="@Model.SideBarViewModel.UserViewModel.ProfileMiniImageUrl" data-default-src="@Url.Cdn("/dist/images/bp_user.png")" />
                                }
                                else
                                {
                                    <img alt="" class="size-9 shrink-0 rounded-full border-2 border-green-500" src="@Url.Cdn("/dist/images/bp_user.png")" data-default-src="@Url.Cdn("/dist/images/bp_user.png")" />
                                }

                                <div class="flex flex-col gap-1.5 min-w-0 flex-1">
                                    <span class="text-sm text-foreground font-semibold leading-none truncate">
                                        @Model.SideBarViewModel.UserViewModel.FirstName @Model.SideBarViewModel.UserViewModel.LastName
                                    </span>
                                    <a class="text-xs text-secondary-foreground hover:text-primary font-medium leading-none truncate block" href="html/demo9/account/home/<USER>" title="@Model.SideBarViewModel.UserViewModel.Email">
                                        @Model.SideBarViewModel.UserViewModel.Email
                                    </a>
                                </div>
                            </div>

                        </div>
                        <ul class="kt-dropdown-menu-sub">
                            <li>
                                <div class="kt-dropdown-menu-separator">
                                </div>
                            </li>

                            <li>
                                <a class="kt-dropdown-menu-link" href="@Url.Action("Index", "Profile")">
                                    <i class="ki-filled ki-profile-circle">
                                    </i>
                                    My Profile
                                </a>
                            </li>
                            @if (Model.SideBarViewModel.IsSettingsVisible && Model.SideBarViewModel.IsBillingVisible)
                            {
                                <li>
                                    <a class="kt-dropdown-menu-link" href="@Url.Action("ViewPlan", "Account")">
                                        <i class="ki-filled ki-icon">
                                        </i>
                                        Billing
                                    </a>
                                </li>
                            }

                            <li>
                                <div class="kt-dropdown-menu-separator">
                                </div>
                            </li>
                        </ul>
                        <div class="px-2.5 pt-1.5 mb-2.5 flex flex-col gap-3.5">
                            <div class="flex items-center gap-2 justify-between">
                                <span class="flex items-center gap-2">
                                    <i class="ki-filled ki-moon text-base text-muted-foreground">
                                    </i>
                                    <span class="font-medium text-2sm">
                                        Dark Mode
                                    </span>
                                </span>
                                <input class="kt-switch" data-kt-theme-switch-state="dark" data-kt-theme-switch-toggle="true" name="check" type="checkbox" value="1" />
                            </div>
                            <a class="kt-btn kt-btn-outline justify-center w-full" href="@Url.Action("LogOff", "Account")">
                                Log out
                            </a>
                        </div>
                    </div>
                </div>
                <!-- End of User -->
            </div>
            <!-- End of Topbar -->
        </div>
    </div>
    <!-- End of Container -->
</header>
<!-- End of Header -->
<!-- Navbar -->
<div class="bg-muted hidden mb-4 lg:flex lg:items-stretch border-y border-input [--kt-drawer-enable:true] lg:[--kt-drawer-enable:false]" data-kt-drawer="true" data-kt-drawer-class="kt-drawer kt-drawer-start fixed z-10 top-0 bottom-0 w-full me-5 max-w-[250px] p-5 lg:p-0 overflow-auto" id="navbar">
    <!-- Container -->
    <div class="kt-container-fixed lg:flex lg:flex-wrap lg:justify-between lg:items-center gap-2 px-0 lg:px-7.5">
        <!-- Mega Menu -->
        <div class="kt-menu items-stretch flex-col lg:flex-row gap-5 lg:gap-7.5 grow lg:grow-0 " style="height:34px; font-family:inter" data-kt-menu="true" id="mega_menu">


            @if (Model.SideBarViewModel.IsSideBarVisible)
            {
                if (Model.SideBarViewModel.IsDashboardVisible)
                {
                    @MainMenuItem("Dashboard", Model.SideBarViewModel.IsDashboardActive, new List<HelperResult>[0], "/")
                }
                else
                {
                    @MainMenuItem("Time Entry", Model.SideBarViewModel.IsPunchInOutSectionActive,
                        new List<HelperResult>[] { }
                        , Url.Action("Punch", "Employee"))
                }
                // Determine if we need submenu items
                bool needsTimeCardsSubmenu = Model.SideBarViewModel.TimesheetApprovalEnabled &&
                                    Model.SideBarViewModel.IsDashboardVisible;

                if (needsTimeCardsSubmenu)
                {
                    var subMenuItems = new List<HelperResult> {
                                        SubMenuItem(Url.Action("TimeCards", "Report"), "View All", icon: "ki-filled ki-element-11")
                                    };

                    subMenuItems.Add(SubMenuItem(
                        Url.Action("PendingApproval", "TimeCard"),
                        $"Pending ({Model.SideBarViewModel.PendingApprovalCount})",
                        icon: "ki-filled ki-time"
                    ));

                    @MainMenuItem("Time Cards", Model.SideBarViewModel.IsTimeCardsSectionActive, subMenuItems, "javascript:;")
                }
                else
                {
                    @MainMenuItem("Time Cards", Model.SideBarViewModel.IsTimeCardsSectionActive,
                        new List<HelperResult>[0], Url.Action("TimeCards", "Report"))
                }

                if (Model.SideBarViewModel.PTOEnabled)
                {
                    var subMenuItems = new List<HelperResult> { SubMenuItem(Url.Action("PTOCalendar", "PTO"), "Time Off Calendar", icon: "ki-filled ki-calendar") };
                    if (!Model.SideBarViewModel.IsAdministrator && Model.SideBarViewModel.PTOAccrualRulesAllowed)
                    {
                        subMenuItems.Add(SubMenuItem(Url.Action("EmployeePTOSummary", "Employee"), "Time Off Summary", icon: "ki-filled ki-notepad"));
                    }

                    if (subMenuItems.Count > 1)
                    {
                        @MainMenuItem("Time Off", Model.SideBarViewModel.IsTimeOffActive, subMenuItems, "javascript:;")
                    }
                    else
                    {
                        @MainMenuItem("Time Off", Model.SideBarViewModel.IsTimeOffActive, Array.Empty<List<HelperResult>>(), Url.Action("PTOCalendar", "PTO"));
                    }
                }

                if (Model.SideBarViewModel.IsScheduleVisible)
                {
                    if (Model.SideBarViewModel.IsAvailabilityVisible || Model.SideBarViewModel.IsShiftRequestVisible)
                    {
                        HtmlString scheduleBadge = null;
                        var subMenuItems = new List<HelperResult>{
                                            SubMenuItem(Url.Action("Index", "Schedule"), "View Schedule", icon: "ki-filled ki-calendar"),
                                        };
                        if (Model.SideBarViewModel.IsShiftRequestVisible)
                        {
                            if (Model.SideBarViewModel.ShiftTradePendingActionCount > 0)
                            {
                                scheduleBadge = new HtmlString(string.Format("<span class=\"m-badge m-badge--warning\">{0}</span>", Model.SideBarViewModel.ShiftTradePendingActionCount));
                                subMenuItems.Add(SubMenuItem(Url.Action("Index", "ShiftTrade"), "Shift Requests", scheduleBadge, icon: "ki-filled ki-calendar-edit"));
                            }
                            else
                            {
                                subMenuItems.Add(SubMenuItem(Url.Action("Index", "ShiftTrade"), "Shift Requests", icon: "ki-filled ki-calendar-edit"));
                            }
                        }
                        if (Model.SideBarViewModel.IsAvailabilityVisible)
                        {
                            subMenuItems.Add(SubMenuItem(Url.Action("Availability", "Schedule"), "Availability", icon: "ki-filled ki-calendar-tick"));
                        }

                        @MainMenuItem("Schedule", Model.SideBarViewModel.IsScheduleSectionActive, subMenuItems, "javascript:;", scheduleBadge)
                    }
                    else
                    {
                        @MainMenuItem("Schedule", Model.SideBarViewModel.IsScheduleSectionActive, new List<HelperResult>[0], Url.Action("Index", "Schedule"))
                    }
                }
                if (Model.SideBarViewModel.IsEmployeesVisible)
                {
                    @MainMenuItem("Employees", Model.SideBarViewModel.IsEmployeesSectionActive, new List<HelperResult>[0], Url.Action("Index", "Employee"))
                }

                if (Model.SideBarViewModel.IsPayrollVisible)
                {
                    if (Model.SideBarViewModel.IsAdministrator)
                    {
                        var payrollItems = new List<HelperResult>();

                        payrollItems.Add(
                            SubMenuItem(Url.Action("Company", "Payroll"), "Company Details", icon: "ki-filled ki-information-2"));
                        payrollItems.Add(
                            SubMenuItem(Url.Action("PeopleList", "Payroll"), "People", icon: "ki-filled ki-users"));

                        if (Model.SideBarViewModel.IsPayrollFeatureOn)
                        {
                            payrollItems.Add(
                                SubMenuItem(Url.Action("Payrolls", "Payroll"), "View Payrolls", icon: "ki-filled ki-tablet-text-down"));
                            payrollItems.Add(
                                SubMenuItem(Url.Action("TaxDeposits", "Payroll"), "Tax Deposits", icon: "ki-filled ki-files"));
                            payrollItems.Add(
                                                SubMenuItemPayrollDocuments("Documents"));
                            payrollItems.Add(
                                SubMenuItemPayrollReports("Reports"));
                            payrollItems.Add(SubMenuItem(Url.Action("PreparePayroll", "Payroll"), "Run Payroll", icon: "ki-filled ki-to-right"));
                        }

                        @MainMenuItem("Payroll", Model.SideBarViewModel.IsPayrollSectionActive,
                            payrollItems
                            , "javascript:;")
                    }
                    else if (Model.SideBarViewModel.IsPaystubsVisible)
                    {
                        @MainMenuItem("Paystubs", Model.SideBarViewModel.IsPayrollSectionActive, new List<HelperResult>[0], Url.Action("Paystubs", "MyPayroll"))
                    }
                }
                if (Model.SideBarViewModel.IsOnboardingVisible)
                {
                    @MainMenuItem("Payroll Setup", Model.SideBarViewModel.IsOnboardingSectionActive, new List<HelperResult>[0], Url.Action("Onboard", "MyPayroll"))
                }
                if (Model.SideBarViewModel.IsReportsVisible)
                {
                    var reports = new List<HelperResult>();

                    reports.Add(SubMenuItemReportBy("Hours Report By", "ki-filled ki-time"));
                    reports.Add(SubMenuItem(Url.Action("SummaryHours", "Report"), "Hours Summary", icon: "ki-filled ki-chart-pie-4"));
                    reports.Add(SubMenuItem(Url.Action("DailyHours", "Report"), "Daily Hours", icon: "ki-filled ki-calendar-8"));
                    reports.Add(SubMenuItem(Url.Action("InOutActivity", "Report"), "In/Out Activity", icon: "ki-filled ki-graph-2"));
                    reports.Add(SubMenuItem(Url.Action("PTOSummary", "Report"), "PTO Summary", icon: "ki-filled ki-chart-pie-3"));

                    if (Model.SideBarViewModel.EnableInOutDetailReport)
                    {
                        reports.Add(SubMenuItem(Url.Action("InOutDetailReport", "Report"), "Employee Detail", icon: "ki-filled ki-user-square"));
                    }

                    reports.Add(SubMenuItem(Url.Action("TimeCardReport", "Report"), "Time Card Report", icon: "ki-filled ki-badge"));
                    reports.Add(SubMenuItem(Url.Action("DeleteReport", "Report"), "Deleted Time Report", icon: "ki-filled ki-chart-simple"));
                    reports.Add(SubMenuItem(Url.Action("PayrollExport", "Report"), "Payroll Export", icon: "ki-filled ki-files"));

                    if (Model.SideBarViewModel.IsScheduleVisible)
                    {
                        reports.Add(SubMenuItemScheduling("Scheduling"));
                    }

                    reports.Add(SubMenuItem(Url.Action("EmployeeErrorLog", "Report"), "Employee Error Log", icon: "ki-filled ki-shield-cross"));

                    if (Model.SideBarViewModel.AccountId == Constants.Veeva_AccountId)
                    {
                        reports.Add(SubMenuItem(Url.Action("Veeva", "Report"), "Veeva Custom", icon: "ki-filled ki-graph-3"));
                    }

                    if (alwaysOnGPSEnabled)
                    {
                        reports.Add(SubMenuItem(Url.Action("GPSActivity", "Report"), "GPS Activity", icon: "ki-filled ki-geolocation"));
                    }

                    if (Model.SideBarViewModel.IsCustomReportsVisible)
                    {
                        reports.Add(SubMenuItem(Url.Action("CustomReports", "Report"), "Custom Reports", icon: "ki-filled ki-graph-3"));
                    }

                    @MainMenuItem("Reports", Model.SideBarViewModel.IsReportsSectionActive, reports, "javascript:;")
                }

                if (Model.SideBarViewModel.IsSettingsVisible)
                {
                    var settings = new List<HelperResult>();
                    settings.Add(SubMenuHeader("Basic", true));
                    settings.Add(SubMenuItem("Account Settings", "Edit", "Account", icon: "ki-filled ki-profile-circle"));
                    settings.Add(SubMenuItem("Administrators", "AdminList", "Account", icon: "ki-filled ki-users"));
                    if (Model.SideBarViewModel.IsBillingVisible)
                    {
                        settings.Add(SubMenuItem("Billing", "ViewPlan", "Account", icon: "ki-filled ki-two-credit-cart"));
                    }
                    settings.Add(SubMenuItem("Integrations", "Index", "Integration", icon: "ki-filled ki-exit-up"));
                    settings.Add(SubMenuItem("Pay Periods", "Index", "PayPeriod", icon: "ki-filled ki-calendar-2"));

                    if (Model.SideBarViewModel.IsPayrollLandingPageVisible && !Model.SideBarViewModel.IsPayrollVisible)
                    {
                        settings.Add(SubMenuItem("Payroll", "Intro", "Payroll", icon: "ki-filled ki-cheque"));
                    }
                    settings.Add(SubMenuItem("Scheduling", "Settings", "Schedule", icon: "ki-filled ki-calendar-edit"));
                    settings.Add(SubMenuItem("Time Entry Options", "TimeEntryOptions", "Account", icon: "ki-filled ki-switch"));
                    settings.Add(SubMenuItem("Time Off", "PTOSettings", "Account", icon: "ki-filled ki-calendar-remove"));
                    settings.Add(SubMenuItem("Webcam Settings", "WebcamSettings", "Account", icon: "ki-filled ki-setting-4"));

                    //br
                    settings.Add(EmptyMenuItem());
                    settings.Add(SubMenuItem("Locations", "Index", "Location", icon: "ki-filled ki-geolocation"));
                    settings.Add(SubMenuItem("Department Codes", "Index", "JobCode", icon: "ki-filled ki-menu"));
                    if (Model.SideBarViewModel.IsScheduleVisible)
                    {
                        settings.Add(SubMenuItem("Positions", "Index", "Position", icon: "ki-filled ki-user-square"));
                    }
                    //settings.Add(SubMenuItem("Groups", "Index", "Group"));
                    //col 2
                    var settings2 = new List<HelperResult>();
                    settings2.Add(SubMenuHeader("Advanced", true));
                    settings2.Add(SubMenuItem("Alerts & Reminders", "Index", "AlertRule", icon: "ki-filled ki-notification-on"));
                    settings2.Add(SubMenuItem("Break Rules", "Index", "BreakRule", icon: "ki-filled ki-watch"));
                    settings2.Add(SubMenuItem("Device Locks", "Index", "DeviceLock", icon: "ki-filled ki-lock"));
                    settings2.Add(SubMenuItem("Geofences", "Index", "Geofence", icon: "ki-filled ki-map"));
                    settings2.Add(SubMenuItem("GPS Settings", "GpsSettings", "Account", icon: "ki-filled ki-telephone-geolocation"));
                    settings2.Add(SubMenuItem("IP Address Locks", "Index", "IpAddressLock", icon: "ki-filled ki-data"));
                    settings2.Add(SubMenuItem("Kiosk Settings", "KioskModeSettings", "Account", icon: "ki-filled ki-pad"));
                    settings2.Add(SubMenuItem("Overtime", "OvertimeSettings", "Account", icon: "ki-filled ki-calendar-add"));
                    settings2.Add(SubMenuItem("Pay Rates", "PayRatesSettings", "Account", icon: "ki-filled ki-trello"));

                    settings2.Add(SubMenuItem("Punch Limiting", "Index", "PunchLimitRule", icon: "ki-filled ki-timer"));
                    settings2.Add(SubMenuItem("Punch Rounding", "Index", "PunchRounding", icon: "ki-filled ki-arrow-circle-right"));

                    if (Model.SideBarViewModel.EnableQrCodeSettings)
                    {
                        settings2.Add(SubMenuItem("QR Code Settings", "QrCodeSettings", "Account", icon: "ki-filled ki-scan-barcode"));
                    }

                    settings2.Add(SubMenuItem("Time Card Approvals Settings", "TimesheetApprovalSettings", "Account", icon: "ki-filled ki-calendar-tick"));

                    @MainMenuItem("Settings", Model.SideBarViewModel.IsSettingsSectionActive,
                        new[] { settings, settings2 }, "javascript:;")
                }
            }
            @if (Model.SideBarViewModel.IsPunchInOutVisible)
            {
                @MainMenuItem("Time Entry", Model.SideBarViewModel.IsPunchInOutSectionActive,
                    new List<HelperResult>[] { }
                    , Url.Action("Punch", "Employee"))
            }
        </div>
        <!-- End of Mega Menu -->
    </div>
    <!-- End of Container -->
</div>
<!-- End of Navbar -->
