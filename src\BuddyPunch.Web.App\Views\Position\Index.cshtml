﻿@using BuddyPunch.Web.App.Helpers
@using BuddyPunch.Web.App.ViewModel
@model BuddyPunch.Web.App.ViewModel.PositionListViewModel
@{
    ViewBag.Title = "Positions";
}

@Html.Partial("_ErrorSuccessPartial")

<!-- BEGIN PAGE HEADER-->
<div class="m-subheader">
    <h3 class="m-subheader__title ">
        Positions
    </h3>
</div>
<!-- END PAGE HEADER-->
<!-- BEGIN PAGE CONTENT-->


<div class="alert alert-block alert-info">
    <span>
        Positions are created here and then assigned to employees in Employees -> View.<br />
        When employees go to punch in they can select a position to punch into.
    </span>
</div>

<p>
    <a href="@Url.Action("Create")" class="btn btn-default"><i class="fa fa-plus"></i> Add a New Position</a>
    
</p>

@helper RenderTabHeader()
{
    <li class="nav-item m-tabs__item">
        <a href="#tab_6_0" data-toggle="tab" class="nav-link m-tabs__link active active-positions-tab" role="tab" aria-expanded="false">Active (@Model.Positions.Count(e => e.Enabled))</a>
    </li>
    <li class="nav-item m-tabs__item">
        <a href="#tab_6_1" data-toggle="tab" class="nav-link m-tabs__link inactiv-positions-tab" role="tab" aria-expanded="false">Inactive (@Model.Positions.Count(e => !e.Enabled))</a>
    </li>
    <li class="nav-item m-tabs__item">
        <a href="#tab_6_2" data-toggle="tab" class="nav-link m-tabs__link all-positions-tab" role="tab" aria-expanded="false">All (@Model.Positions.Count())</a>
    </li>
}

@helper RenderPositions(IEnumerable<PositionViewModel> positions, string id)
{
    <div class="form-group m-form__group row align-items-center">
        <div class="col-md-4">
            <div class="m-input-icon m-input-icon--left">
                <input type="text" class="form-control m-input m-input--solid" placeholder="Search..." id="@(id + "_search")">
                <span class="m-input-icon__icon m-input-icon__icon--left">
                    <span>
                        <i class="la la-search"></i>
                    </span>
                </span>
            </div>
        </div>
        <div class="col-md-8 m--align-right">
            <a href="javascript:void(0);" class="activate-position btn btn-primary hide">Activate</a>
            <a href="javascript:void(0);" class="deactivate-position btn btn-primary hide">Deactivate</a>
        </div>
    </div>
    <table class="table table-striped table-hover table-fixed-header" id="@id">
        <thead>
            <tr>
                <th>Actions</th>
                <th>Name</th>
                <th>Employees Assigned</th>
                <th>Active</th>
                <th class="table-select-col no-sort">
                    <input type="checkbox" style="width: 18px; height: 18px" class="activate-position-cb-all" />
                </th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in positions)
            {
                <tr>

                    <td>
                        <a href="@Url.Action("Edit", new {id = item.Id})" class="btn btn-xs btn-sm btn-default"><i class="fa fa-edit"></i> Edit</a>
                    </td>

                    <td>
                        @item.Name
                    </td>

                    <td>@item.EmployeesAssigned</td>
                    <td>@(item.Enabled ? "Yes" : "No")</td>
                    <td class="table-select-col">
                        <input type="checkbox" class="activate-position-cb" style="width: 18px; height: 18px" data-id="@item.Id" />
                    </td>
                </tr>
            }
        </tbody>
    </table>
}

@using (Html.BeginMetronicTabPortlet(RenderTabHeader(), "Positions"))
{
    <div class="tab-content">
        <div class="tab-pane active" id="tab_6_0">
            @RenderPositions(Model.Positions.Where(j => j.Enabled).OrderBy(e => e.Name), "positions_active_table")
        </div>
        <div class="tab-pane" id="tab_6_1">
            @RenderPositions(Model.Positions.Where(j => !j.Enabled).OrderBy(e => e.Name), "positions_inactive_table")
        </div>
        <div class="tab-pane" id="tab_6_2">
            @RenderPositions(Model.Positions.OrderBy(e => e.Name), "positions_table")
        </div>
    </div>
}


@section PageHeadSection
{

}
@section PageBeforeEndBodySection
    {
    <script type="text/javascript">
        jQuery(document).ready(function () {

            initActionsDataTable('#positions_table', {
                "order": [[2, "asc"]],
                "columnDefs": [{
                    "targets": 'no-sort',
                    "orderable": false,
                    "order": []
                }]
            });
            initActionsDataTable('#positions_active_table', {
                "order": [[2, "asc"]],
                fixedHeader: {
                    headerOffset: $('.m-header').outerHeight(),
                },
                "columnDefs": [{
                    "targets": 'no-sort',
                    "orderable": false,
                    "order": []
                }]
            });
            initActionsDataTable('#positions_inactive_table', {
                "order": [[2, "asc"]],
                "columnDefs": [{
                    "targets": 'no-sort',
                    "orderable": false,
                    "order": []
                }]
            });

            var selectedArray = [];
            var selectedTab = 1;

            $(document).on('click', '.activate-position-cb', function () {
                var checked = $(this).prop('checked');
                var id = $(this).data('id');

                if (checked) {
                    if (!selectedArray.includes(id)) {
                        selectedArray.push(id);
                    }
                } else {
                    selectedArray = selectedArray.filter(empId => empId !== id);
                }

                updateActionButtonsVisibility();
            });

            $(document).on('click', '.active-positions-tab', function () {
                resetPositionSelect(1);
                $('.table-select-col.hide').each(function () {
                    $(this).removeClass('hide');
                });
            });

            $(document).on('click', '.inactiv-positions-tab', function () {
                resetPositionSelect(2);
                $('.table-select-col.hide').each(function () {
                    $(this).removeClass('hide');
                });
            });

            $(document).on('click', '.all-positions-tab', function () {
                resetPositionSelect(3);
                $('.table-select-col').addClass('hide');
            });

            $(document).on('change', '.activate-position-cb-all', function () {
                var isChecked = $(this).prop('checked');
                var checkboxes = $(this).closest('.tab-pane').find('.activate-position-cb');

                checkboxes.prop('checked', isChecked);

                selectedArray = isChecked
                    ? checkboxes.map(function () {
                        return $(this).data('id');
                    }).get()
                    : [];

                updateActionButtonsVisibility();
            });

            $(document).on('click', '.activate-position', function (e) {
                e.preventDefault();

                if (selectedArray.length === 0) {
                    return;
                }
                $.post("@Url.Action("ActivateAll", "Position")", {
                    ids: ArrayToString(selectedArray),
                    activate: true
                }, function () {
                    window.location.href = '@Url.Action("Index", "Position")';
                });

                resetPositionSelect(selectedTab);
            });

            $(document).on('click', '.deactivate-position', function (e) {
                e.preventDefault();

                if (selectedArray.length === 0) {
                    return;
                }
                $.post("@Url.Action("ActivateAll", "Position")", {
                    ids: ArrayToString(selectedArray),
                    activate: false
                }, function () {
                    window.location.href = '@Url.Action("Index", "Position")';
                });

                resetPositionSelect(selectedTab);
            });

            function updateActionButtonsVisibility() {
                if (selectedArray.length > 0) {
                    if (selectedTab === 1) {
                        $('.deactivate-position').removeClass('hide');
                    } else if (selectedTab === 2) {
                        $('.activate-position').removeClass('hide');
                    }
                } else {
                    if (selectedTab === 1) {
                        $('.deactivate-position').addClass('hide');
                    } else if (selectedTab === 2) {
                        $('.activate-position').addClass('hide');
                    }
                }
            }

            function resetPositionSelect(tab) {
                selectedTab = tab;
                selectedArray = [];
                $('.activate-position-cb').each(function () {
                    $(this).prop('checked', false);
                });
                $('.activate-position-cb-all').each(function () {
                    $(this).prop('checked', false);
                });
                if ($('.activate-position').hasClass('hide') == false) {
                    $('.activate-position').addClass('hide');
                }
                if ($('.deactivate-position').hasClass('hide') == false) {
                    $('.deactivate-position').addClass('hide');
                }
            }

            // converts an array of items to a concatenated semicolumn joined string for example: [1, 2, 3] => "1;2;3"
            function ArrayToString(arr) {
                if (arr.length == 0) {
                    return;
                }
                var r = "";
                for (var i = 0; i < arr.length; i++) {
                    r = r + arr[i] + ";";
                }
                r = r.substring(0, r.length - 1);
                return r;
            }
        });
    </script>
}