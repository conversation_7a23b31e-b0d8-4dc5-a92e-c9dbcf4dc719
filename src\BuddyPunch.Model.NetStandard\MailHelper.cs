﻿using System;
using System.Net;
using System.Net.Mail;

namespace BuddyPunch.Model.Interfaces
{
    public class MailHelper
    {
        private bool _isNewApi;

        public static void Initialize(bool isNewApi)
        {
            Client = new MailHelper(isNewApi);
        }

        public MailHelper(bool isNewApi)
        {
            _isNewApi = isNewApi;
        }

        public static MailHelper Client;

        public void SendEmail(MailMessage message)
        {
            using (var smtpClient = CreateSmtpClient())
            {
                if (_isNewApi)
                {
                    var deliveryMethod = ConfigurationHelper.Configuration["mailSettings:deliveryMethod"];
                    if (deliveryMethod.Equals("SpecifiedPickupDirectory", StringComparison.OrdinalIgnoreCase))
                    {
                        message = new MailMessage("<EMAIL>", message.To.ToString(), message.Subject, message.Body);
                    }
                    else
                    {
                        if (message.From == null)
                        {
                            message.From = new MailAddress(ConfigurationHelper.Configuration["mailSettings:from"]);
                        }
                    }
                }

                smtpClient.Send(message);
            }
        }

        public SmtpClient CreateSmtpClient()
        {
            var smtpClient = new SmtpClient();
            if (_isNewApi)
            {
                var deliveryMethod = ConfigurationHelper.Configuration["mailSettings:deliveryMethod"];
                if (deliveryMethod.Equals("SpecifiedPickupDirectory", System.StringComparison.OrdinalIgnoreCase))
                {
                    smtpClient.DeliveryMethod = SmtpDeliveryMethod.SpecifiedPickupDirectory;
                    smtpClient.Host = "localhost";
                    smtpClient.PickupDirectoryLocation = ConfigurationHelper.Configuration["mailSettings:pickupDirectoryLocation"];
                }
                else
                {
                    smtpClient.DeliveryMethod = SmtpDeliveryMethod.Network;
                    smtpClient.Host = ConfigurationHelper.Configuration["mailSettings:host"];
                    smtpClient.Port = Convert.ToInt32(ConfigurationHelper.Configuration["mailSettings:port"]);
                    smtpClient.EnableSsl = Convert.ToBoolean(ConfigurationHelper.Configuration["mailSettings:enableSsl"]);
                    var userName = ConfigurationHelper.Configuration["mailSettings:username"];
                    var password = ConfigurationHelper.Configuration["mailSettings:password"];
                    smtpClient.Credentials = new NetworkCredential(userName, password);
                }
            }

            return smtpClient;
        }
    }
}