﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\EntityFramework.6.4.4\build\EntityFramework.props" Condition="Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.props')" />
  <Import Project="..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props" Condition="Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" />
  <Import Project="..\packages\Microsoft.Net.Compilers.3.3.1\build\Microsoft.Net.Compilers.props" Condition="Exists('..\packages\Microsoft.Net.Compilers.3.3.1\build\Microsoft.Net.Compilers.props')" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.Default.props" Condition="Exists('$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.Default.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{F5324D3E-1F84-4A63-9EAB-04F8CBF1B5D2}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>BuddyPunch.Web.App</RootNamespace>
    <AssemblyName>BuddyPunch.Web.App</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <MvcBuildViews>false</MvcBuildViews>
    <UseIISExpress>true</UseIISExpress>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <OldToolsVersion>4.0</OldToolsVersion>
    <IISExpressSSLPort>44300</IISExpressSSLPort>
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <TargetFrameworkProfile />
    <MvcProjectUpgradeChecked>true</MvcProjectUpgradeChecked>
    <UseGlobalApplicationHostFile />
    <ApplicationInsightsResourceId>/subscriptions/5dcb1425-9c4a-42c1-8385-3c6125a52c31/resourcegroups/Default-ApplicationInsights-CentralUS/providers/microsoft.insights/components/BuddyPunch.Web.App</ApplicationInsightsResourceId>
    <Use64BitIISExpress />
    <TypeScriptToolsVersion>2.5</TypeScriptToolsVersion>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <ApplicationInsightsAnnotationResourceId>/subscriptions/5dcb1425-9c4a-42c1-8385-3c6125a52c31/resourcegroups/Default-ApplicationInsights-CentralUS/providers/microsoft.insights/components/BuddyPunch.Web.App</ApplicationInsightsAnnotationResourceId>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <PublishDatabases>false</PublishDatabases>
    <NoWarn>CS1998</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Altairis.Web.Security">
      <HintPath>..\packages\Altairis.Web.Security.2.4.4.0\lib\net40\Altairis.Web.Security.dll</HintPath>
    </Reference>
    <Reference Include="Antlr3.Runtime, Version=3.4.1.9004, Culture=neutral, PublicKeyToken=eb42632606e9261f, processorArchitecture=MSIL">
      <HintPath>..\packages\Antlr.3.4.1.9004\lib\Antlr3.Runtime.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Audit.Mvc, Version=13.0.0.0, Culture=neutral, PublicKeyToken=571d6b80b242c87e, processorArchitecture=MSIL">
      <HintPath>..\packages\Audit.Mvc.13.0.0\lib\net45\Audit.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="Audit.NET, Version=13.0.0.0, Culture=neutral, PublicKeyToken=571d6b80b242c87e, processorArchitecture=MSIL">
      <HintPath>..\packages\Audit.NET.13.0.0\lib\net45\Audit.NET.dll</HintPath>
    </Reference>
    <Reference Include="Audit.NET.SqlServer, Version=13.0.0.0, Culture=neutral, PublicKeyToken=571d6b80b242c87e, processorArchitecture=MSIL">
      <HintPath>..\packages\Audit.NET.SqlServer.13.0.0\lib\net45\Audit.NET.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="AutoMapper, Version=6.2.2.0, Culture=neutral, PublicKeyToken=be96cd2c38ef1005, processorArchitecture=MSIL">
      <HintPath>..\packages\AutoMapper.6.2.2\lib\net45\AutoMapper.dll</HintPath>
    </Reference>
    <Reference Include="BitArmory.ReCaptcha, Version=5.0.0.0, Culture=neutral, PublicKeyToken=599b7007c8f0cb6f, processorArchitecture=MSIL">
      <HintPath>..\packages\BitArmory.ReCaptcha.5.0.0\lib\net45\BitArmory.ReCaptcha.dll</HintPath>
    </Reference>
    <Reference Include="Common.Logging">
      <HintPath>..\packages\Common.Logging.2.0.0\lib\2.0\Common.Logging.dll</HintPath>
    </Reference>
    <Reference Include="CsvHelper, Version=1*******, Culture=neutral, PublicKeyToken=8c4959082be5c823, processorArchitecture=MSIL">
      <HintPath>..\packages\CsvHelper.12.1.2\lib\net45\CsvHelper.dll</HintPath>
    </Reference>
    <Reference Include="DataAnnotationsExtensions, Version=5.0.1.27, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\DataAnnotationsExtensions.5.0.1.27\lib\NETFramework40\DataAnnotationsExtensions.dll</HintPath>
    </Reference>
    <Reference Include="DataAnnotationsExtensions.ClientValidation, Version=5.0.1.27, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\DataAnnotationsExtensions.MVC3.5.0.1.27\lib\NETFramework40\DataAnnotationsExtensions.ClientValidation.dll</HintPath>
    </Reference>
    <Reference Include="DevDefined.OAuth, Version=*******, Culture=neutral, PublicKeyToken=a3261f5b4697e67d, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\DevDefined.OAuth.0.2\lib\DevDefined.OAuth.dll</HintPath>
    </Reference>
    <Reference Include="DotNetOpenAuth.Core">
      <HintPath>..\packages\DotNetOpenAuth.Core.4.3.4.13329\lib\net45-full\DotNetOpenAuth.Core.dll</HintPath>
    </Reference>
    <Reference Include="DotNetOpenAuth.Mvc5, Version=4.3.0.0, Culture=neutral, PublicKeyToken=2780ccd10d57b246, processorArchitecture=MSIL">
      <HintPath>..\packages\DotNetOpenAuth.Mvc5.4.3.4.13329\lib\net45-full\DotNetOpenAuth.Mvc5.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="DotNetOpenAuth.OpenId">
      <HintPath>..\packages\DotNetOpenAuth.OpenId.Core.4.3.4.13329\lib\net45-full\DotNetOpenAuth.OpenId.dll</HintPath>
    </Reference>
    <Reference Include="DotNetOpenAuth.OpenId.RelyingParty">
      <HintPath>..\packages\DotNetOpenAuth.OpenId.RelyingParty.4.3.4.13329\lib\net45-full\DotNetOpenAuth.OpenId.RelyingParty.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.4.4\lib\net45\EntityFramework.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.4.4\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="FileHelpers, Version=3.4.2.0, Culture=neutral, PublicKeyToken=3e0c08d59cc3d657, processorArchitecture=MSIL">
      <HintPath>..\packages\FileHelpers.3.4.2\lib\net45\FileHelpers.dll</HintPath>
    </Reference>
    <Reference Include="FlatFile.Core, Version=0.2.51.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\FlatFile.Core.0.2.51\lib\net45\FlatFile.Core.dll</HintPath>
    </Reference>
    <Reference Include="FlatFile.Core.Attributes, Version=0.2.51.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\FlatFile.Core.Attributes.0.2.51\lib\net45\FlatFile.Core.Attributes.dll</HintPath>
    </Reference>
    <Reference Include="FlatFile.Delimited, Version=0.2.51.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\FlatFile.Delimited.0.2.51\lib\net45\FlatFile.Delimited.dll</HintPath>
    </Reference>
    <Reference Include="FlatFile.Delimited.Attributes, Version=0.2.51.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\FlatFile.Delimited.Attributes.0.2.51\lib\net45\FlatFile.Delimited.Attributes.dll</HintPath>
    </Reference>
    <Reference Include="FlatFile.FixedLength, Version=0.2.51.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\FlatFile.FixedLength.0.2.51\lib\net45\FlatFile.FixedLength.dll</HintPath>
    </Reference>
    <Reference Include="FlatFile.FixedLength.Attributes, Version=0.2.51.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\FlatFile.FixedLength.Attributes.0.2.51\lib\net45\FlatFile.FixedLength.Attributes.dll</HintPath>
    </Reference>
    <Reference Include="FluentValidation, Version=7.0.0.0, Culture=neutral, PublicKeyToken=7de548da2fbae0f0, processorArchitecture=MSIL">
      <HintPath>..\packages\FluentValidation.7.3.4\lib\net45\FluentValidation.dll</HintPath>
    </Reference>
    <Reference Include="FluentValidation.Mvc, Version=7.0.0.0, Culture=neutral, PublicKeyToken=7de548da2fbae0f0, processorArchitecture=MSIL">
      <HintPath>..\packages\FluentValidation.Mvc5.7.2.1\lib\net45\FluentValidation.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="GoogleMeasurementProtocol, Version=2.1.0.0, Culture=neutral, PublicKeyToken=6379dfd9a81b378e, processorArchitecture=MSIL">
      <HintPath>..\packages\GoogleMeasurementProtocol.2.1.0\lib\netstandard2.0\GoogleMeasurementProtocol.dll</HintPath>
    </Reference>
    <Reference Include="HubSpot.NET, Version=1.5.2.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Chinchilla.HubSpot.NET.1.5.2\lib\net452\HubSpot.NET.dll</HintPath>
    </Reference>
    <Reference Include="Ical.Net, Version=4.1.8.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Ical.Net.4.1.9\lib\net46\Ical.Net.dll</HintPath>
    </Reference>
    <Reference Include="IdentityModel, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\IdentityModel.3.7.0\lib\net452\IdentityModel.dll</HintPath>
    </Reference>
    <Reference Include="Intercom, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Intercom.Dotnet.Client.2.1.1\lib\net452\Intercom.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Intuit.Ipp.Core, Version=********, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\IppDotNetSdkForQuickBooksApiV3.14.7.0\lib\net472\Intuit.Ipp.Core.dll</HintPath>
    </Reference>
    <Reference Include="Intuit.Ipp.Data, Version=********, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\IppDotNetSdkForQuickBooksApiV3.14.7.0\lib\net472\Intuit.Ipp.Data.dll</HintPath>
    </Reference>
    <Reference Include="Intuit.Ipp.DataService, Version=********, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\IppDotNetSdkForQuickBooksApiV3.14.7.0\lib\net472\Intuit.Ipp.DataService.dll</HintPath>
    </Reference>
    <Reference Include="Intuit.Ipp.Diagnostics, Version=********, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\IppDotNetSdkForQuickBooksApiV3.14.7.0\lib\net472\Intuit.Ipp.Diagnostics.dll</HintPath>
    </Reference>
    <Reference Include="Intuit.Ipp.EntitlementService, Version=********, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\IppDotNetSdkForQuickBooksApiV3.14.7.0\lib\net472\Intuit.Ipp.EntitlementService.dll</HintPath>
    </Reference>
    <Reference Include="Intuit.Ipp.Exception, Version=********, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\IppDotNetSdkForQuickBooksApiV3.14.7.0\lib\net472\Intuit.Ipp.Exception.dll</HintPath>
    </Reference>
    <Reference Include="Intuit.Ipp.GlobalTaxService, Version=********, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\IppDotNetSdkForQuickBooksApiV3.14.7.0\lib\net472\Intuit.Ipp.GlobalTaxService.dll</HintPath>
    </Reference>
    <Reference Include="Intuit.Ipp.LinqExtender, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\IppDotNetSdkForQuickBooksApiV3.7.5.0\lib\Intuit.Ipp.LinqExtender.dll</HintPath>
    </Reference>
    <Reference Include="Intuit.Ipp.OAuth2PlatformClient, Version=********, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\IppDotNetSdkForQuickBooksApiV3.14.7.0\lib\net472\Intuit.Ipp.OAuth2PlatformClient.dll</HintPath>
    </Reference>
    <Reference Include="Intuit.Ipp.OAuth2PlatformClient.Diagnostics, Version=********, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\IppDotNetSdkForQuickBooksApiV3.14.7.0\lib\net472\Intuit.Ipp.OAuth2PlatformClient.Diagnostics.dll</HintPath>
    </Reference>
    <Reference Include="Intuit.Ipp.PlatformService, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\IppDotNetSdkForQuickBooksApiV3.7.5.0\lib\Intuit.Ipp.PlatformService.dll</HintPath>
    </Reference>
    <Reference Include="Intuit.Ipp.QueryFilter, Version=********, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\IppDotNetSdkForQuickBooksApiV3.14.7.0\lib\net472\Intuit.Ipp.QueryFilter.dll</HintPath>
    </Reference>
    <Reference Include="Intuit.Ipp.ReportService, Version=********, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\IppDotNetSdkForQuickBooksApiV3.14.7.0\lib\net472\Intuit.Ipp.ReportService.dll</HintPath>
    </Reference>
    <Reference Include="Intuit.Ipp.Retry, Version=********, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\IppDotNetSdkForQuickBooksApiV3.14.7.0\lib\net472\Intuit.Ipp.Retry.dll</HintPath>
    </Reference>
    <Reference Include="Intuit.Ipp.Security, Version=********, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\IppDotNetSdkForQuickBooksApiV3.14.7.0\lib\net472\Intuit.Ipp.Security.dll</HintPath>
    </Reference>
    <Reference Include="Intuit.Ipp.Utility, Version=********, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\IppDotNetSdkForQuickBooksApiV3.14.7.0\lib\net472\Intuit.Ipp.Utility.dll</HintPath>
    </Reference>
    <Reference Include="Intuit.Ipp.WebHooksService, Version=********, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\IppDotNetSdkForQuickBooksApiV3.14.7.0\lib\net472\Intuit.Ipp.WebHooksService.dll</HintPath>
    </Reference>
    <Reference Include="IppDotNetSdkForQuickBooksApiV3, Version=********, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\IppDotNetSdkForQuickBooksApiV3.14.7.0\lib\net472\IppDotNetSdkForQuickBooksApiV3.dll</HintPath>
    </Reference>
    <Reference Include="itextsharp">
      <HintPath>..\packages\iTextSharp.4.1.2\lib\itextsharp.dll</HintPath>
    </Reference>
    <Reference Include="Kentor.OwinCookieSaver, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Kentor.OwinCookieSaver.1.1.1\lib\net452\Kentor.OwinCookieSaver.dll</HintPath>
    </Reference>
    <Reference Include="log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a, processorArchitecture=MSIL">
      <HintPath>..\packages\log4net.2.0.7\lib\net45-full\log4net.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AI.Agent.Intercept, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.Agent.Intercept.2.4.0\lib\net45\Microsoft.AI.Agent.Intercept.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AI.DependencyCollector, Version=2.23.0.29, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.DependencyCollector.2.23.0\lib\net452\Microsoft.AI.DependencyCollector.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AI.PerfCounterCollector, Version=2.23.0.29, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.PerfCounterCollector.2.23.0\lib\net452\Microsoft.AI.PerfCounterCollector.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AI.ServerTelemetryChannel, Version=2.23.0.29, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.2.23.0\lib\net452\Microsoft.AI.ServerTelemetryChannel.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AI.Web, Version=2.23.0.29, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.Web.2.23.0\lib\net452\Microsoft.AI.Web.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AI.WindowsServer, Version=2.23.0.29, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.WindowsServer.2.23.0\lib\net452\Microsoft.AI.WindowsServer.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ApplicationInsights, Version=2.23.0.29, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.2.23.0\lib\net46\Microsoft.ApplicationInsights.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.Identity.Core, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Identity.Core.2.2.3\lib\net45\Microsoft.AspNet.Identity.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.Identity.Owin, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Identity.Owin.2.2.3\lib\net45\Microsoft.AspNet.Identity.Owin.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.SessionState.SessionStateModule, Version=1.1.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.SessionState.SessionStateModule.1.1.0\lib\Net462\Microsoft.AspNet.SessionState.SessionStateModule.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.SessionState.SqlSessionStateProviderAsync, Version=1.1.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.SessionState.SqlSessionStateProviderAsync.1.1.0\lib\Net462\Microsoft.AspNet.SessionState.SqlSessionStateProviderAsync.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.TelemetryCorrelation, Version=1.0.8.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.TelemetryCorrelation.1.0.8\lib\net45\Microsoft.AspNet.TelemetryCorrelation.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Azure.KeyVault.Core, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Azure.KeyVault.Core.1.0.0\lib\net40\Microsoft.Azure.KeyVault.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=9.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.9.0.0\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=2.0.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\lib\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.Data.Edm, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Data.Edm.5.8.4\lib\net40\Microsoft.Data.Edm.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Data.OData, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Data.OData.5.8.4\lib\net40\Microsoft.Data.OData.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.DotNet.PlatformAbstractions, Version=2.0.4.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.DotNet.PlatformAbstractions.2.0.4\lib\net45\Microsoft.DotNet.PlatformAbstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration, Version=2.2.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.2.2.0\lib\netstandard2.0\Microsoft.Extensions.Configuration.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.Abstractions, Version=9.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.Abstractions.9.0.0\lib\net462\Microsoft.Extensions.Configuration.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.Binder, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.Binder.2.0.0\lib\netstandard2.0\Microsoft.Extensions.Configuration.Binder.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.FileExtensions, Version=2.2.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.FileExtensions.2.2.0\lib\netstandard2.0\Microsoft.Extensions.Configuration.FileExtensions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.Json, Version=2.2.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.Json.2.2.0\lib\netstandard2.0\Microsoft.Extensions.Configuration.Json.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.Xml, Version=2.2.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.Xml.2.2.0\lib\netstandard2.0\Microsoft.Extensions.Configuration.Xml.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection, Version=9.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.9.0.0\lib\net462\Microsoft.Extensions.DependencyInjection.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection.Abstractions, Version=9.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.Abstractions.9.0.0\lib\net462\Microsoft.Extensions.DependencyInjection.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyModel, Version=2.0.4.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.DependencyModel.2.0.4\lib\net451\Microsoft.Extensions.DependencyModel.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.FileProviders.Abstractions, Version=2.2.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.FileProviders.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.Extensions.FileProviders.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.FileProviders.Physical, Version=2.2.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.FileProviders.Physical.2.2.0\lib\netstandard2.0\Microsoft.Extensions.FileProviders.Physical.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.FileSystemGlobbing, Version=2.2.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.FileSystemGlobbing.2.2.0\lib\netstandard2.0\Microsoft.Extensions.FileSystemGlobbing.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Http, Version=9.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Http.9.0.0\lib\net462\Microsoft.Extensions.Http.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging, Version=9.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Logging.9.0.0\lib\net462\Microsoft.Extensions.Logging.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging.Abstractions, Version=9.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Logging.Abstractions.9.0.0\lib\net462\Microsoft.Extensions.Logging.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Options, Version=9.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Options.9.0.0\lib\net462\Microsoft.Extensions.Options.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Options.ConfigurationExtensions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Options.ConfigurationExtensions.2.0.0\lib\netstandard2.0\Microsoft.Extensions.Options.ConfigurationExtensions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Primitives, Version=9.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Primitives.9.0.0\lib\net462\Microsoft.Extensions.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.JsonWebTokens, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.JsonWebTokens.6.5.0\lib\net461\Microsoft.IdentityModel.JsonWebTokens.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Logging.6.5.0\lib\net461\Microsoft.IdentityModel.Logging.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Protocol.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Protocol.Extensions.1.0.0\lib\net45\Microsoft.IdentityModel.Protocol.Extensions.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Protocols, Version=5.3.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Protocols.5.3.0\lib\net461\Microsoft.IdentityModel.Protocols.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Protocols.OpenIdConnect, Version=5.3.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Protocols.OpenIdConnect.5.3.0\lib\net461\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Tokens, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Tokens.6.5.0\lib\net461\Microsoft.IdentityModel.Tokens.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.4.1.0\lib\net45\Microsoft.Owin.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Host.SystemWeb, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Host.SystemWeb.4.1.0\lib\net45\Microsoft.Owin.Host.SystemWeb.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.4.1.0\lib\net45\Microsoft.Owin.Security.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.Cookies, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.Cookies.4.1.0\lib\net45\Microsoft.Owin.Security.Cookies.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.Google, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.Google.4.1.0\lib\net45\Microsoft.Owin.Security.Google.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.OAuth, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.OAuth.4.1.0\lib\net45\Microsoft.Owin.Security.OAuth.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.OpenIdConnect, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.OpenIdConnect.4.1.0\lib\net45\Microsoft.Owin.Security.OpenIdConnect.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Practices.EnterpriseLibrary.WindowsAzure.TransientFaultHandling">
      <HintPath>..\packages\EnterpriseLibrary.WindowsAzure.TransientFaultHandling.5.1.1212.0\lib\NET4\Microsoft.Practices.EnterpriseLibrary.WindowsAzure.TransientFaultHandling.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Practices.TransientFaultHandling.Core">
      <HintPath>..\packages\TransientFaultHandling.Core.5.1.1209.1\lib\NET4\Microsoft.Practices.TransientFaultHandling.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Practices.Unity">
      <HintPath>..\packages\Unity.4.0.1\lib\net45\Microsoft.Practices.Unity.dll</HintPath>
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="Microsoft.Practices.Unity.Interception">
      <HintPath>..\packages\Unity.Interception.4.0.1\lib\Net45\Microsoft.Practices.Unity.Interception.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Practices.Unity.Interception.Configuration, Version=*******, Culture=neutral, PublicKeyToken=6d32ff45e0ccc69f, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Unity.Interception.4.0.1\lib\Net45\Microsoft.Practices.Unity.Interception.Configuration.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Practices.Unity.RegistrationByConvention">
      <HintPath>..\packages\Unity.4.0.1\lib\net45\Microsoft.Practices.Unity.RegistrationByConvention.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Web.Infrastructure, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Microsoft.Web.Infrastructure.*******\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Web.RedisSessionStateProvider, Version=4.0.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Web.RedisSessionStateProvider.4.0.1\lib\net462\Microsoft.Web.RedisSessionStateProvider.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.Diagnostics, Version=2.8.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL" />
    <Reference Include="Microsoft.WindowsAzure.ServiceRuntime, Version=2.7.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL" />
    <Reference Include="Mindscape.Raygun4Net, Version=8.0.0.0, Culture=neutral, PublicKeyToken=d595e487e4f9f950, processorArchitecture=MSIL">
      <HintPath>..\packages\Mindscape.Raygun4Net.Core.8.0.0\lib\net40\Mindscape.Raygun4Net.dll</HintPath>
    </Reference>
    <Reference Include="Mindscape.Raygun4Net.Common, Version=8.0.0.0, Culture=neutral, PublicKeyToken=d595e487e4f9f950, processorArchitecture=MSIL">
      <HintPath>..\packages\Mindscape.Raygun4Net.Mvc.8.0.0\lib\net40\Mindscape.Raygun4Net.Common.dll</HintPath>
    </Reference>
    <Reference Include="Mindscape.Raygun4Net.Mvc, Version=8.0.0.0, Culture=neutral, PublicKeyToken=d595e487e4f9f950, processorArchitecture=MSIL">
      <HintPath>..\packages\Mindscape.Raygun4Net.Mvc.8.0.0\lib\net40\Mindscape.Raygun4Net.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="Mindscape.Raygun4Net4, Version=8.0.0.0, Culture=neutral, PublicKeyToken=d595e487e4f9f950, processorArchitecture=MSIL">
      <HintPath>..\packages\Mindscape.Raygun4Net.Mvc.8.0.0\lib\net40\Mindscape.Raygun4Net4.dll</HintPath>
    </Reference>
    <Reference Include="MvcCheckBoxList, Version=1.4.4.3, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\MvcCheckBoxList.1.4.4.3-beta2\lib\net45\MvcCheckBoxList.dll</HintPath>
    </Reference>
    <Reference Include="MvcPaging, Version=2.1.11.44, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\MvcPaging.2.1.11\lib\net40\MvcPaging.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="MvcThrottle, Version=2.1.3.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\MvcThrottle.2.1.3\lib\net45\MvcThrottle.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Nito.AsyncEx, Version=3.0.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Nito.AsyncEx.3.0.1\lib\net45\Nito.AsyncEx.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Nito.AsyncEx.Concurrent, Version=3.0.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Nito.AsyncEx.3.0.1\lib\net45\Nito.AsyncEx.Concurrent.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Nito.AsyncEx.Enlightenment, Version=3.0.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Nito.AsyncEx.3.0.1\lib\net45\Nito.AsyncEx.Enlightenment.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="NodaTime, Version=2.0.2.0, Culture=neutral, PublicKeyToken=4226afe0d9b296d1, processorArchitecture=MSIL">
      <HintPath>..\packages\NodaTime.2.0.2\lib\net45\NodaTime.dll</HintPath>
    </Reference>
    <Reference Include="Omu.ValueInjecter">
      <HintPath>..\packages\ValueInjecter.2.3.3\lib\net35\Omu.ValueInjecter.dll</HintPath>
    </Reference>
    <Reference Include="Owin">
      <HintPath>..\packages\Owin.1.0\lib\net40\Owin.dll</HintPath>
    </Reference>
    <Reference Include="PhoneNumbers, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\libphonenumber-csharp.7.2.5\lib\PhoneNumbers.dll</HintPath>
    </Reference>
    <Reference Include="Pipelines.Sockets.Unofficial, Version=*******, Culture=neutral, PublicKeyToken=42ea0a778e13fbe2, processorArchitecture=MSIL">
      <HintPath>..\packages\Pipelines.Sockets.Unofficial.2.0.22\lib\net461\Pipelines.Sockets.Unofficial.dll</HintPath>
    </Reference>
    <Reference Include="RazorPDF">
      <HintPath>..\packages\RazorPDF.1.0.0\lib\net45\RazorPDF.dll</HintPath>
    </Reference>
    <Reference Include="RestSharp, Version=**********, Culture=neutral, PublicKeyToken=598062e77f915f75, processorArchitecture=MSIL">
      <HintPath>..\packages\RestSharp.106.15.0\lib\net452\RestSharp.dll</HintPath>
    </Reference>
    <Reference Include="ScheduleWidget, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\ScheduleWidget.2.4.0\lib\NET40\ScheduleWidget.dll</HintPath>
    </Reference>
    <Reference Include="Serilog, Version=*******, Culture=neutral, PublicKeyToken=24c2f752a8e58a10, processorArchitecture=MSIL">
      <HintPath>..\packages\Serilog.2.10.0\lib\net46\Serilog.dll</HintPath>
    </Reference>
    <Reference Include="Serilog.Enrichers.Environment, Version=*******, Culture=neutral, PublicKeyToken=24c2f752a8e58a10, processorArchitecture=MSIL">
      <HintPath>..\packages\Serilog.Enrichers.Environment.2.1.3\lib\net45\Serilog.Enrichers.Environment.dll</HintPath>
    </Reference>
    <Reference Include="Serilog.Enrichers.Thread, Version=*******, Culture=neutral, PublicKeyToken=24c2f752a8e58a10, processorArchitecture=MSIL">
      <HintPath>..\packages\Serilog.Enrichers.Thread.3.1.0\lib\net45\Serilog.Enrichers.Thread.dll</HintPath>
    </Reference>
    <Reference Include="Serilog.Extensions.Logging, Version=*******, Culture=neutral, PublicKeyToken=24c2f752a8e58a10, processorArchitecture=MSIL">
      <HintPath>..\packages\Serilog.Extensions.Logging.3.0.1\lib\netstandard2.0\Serilog.Extensions.Logging.dll</HintPath>
    </Reference>
    <Reference Include="Serilog.Settings.Configuration, Version=3.1.0.0, Culture=neutral, PublicKeyToken=24c2f752a8e58a10, processorArchitecture=MSIL">
      <HintPath>..\packages\Serilog.Settings.Configuration.3.1.0\lib\net461\Serilog.Settings.Configuration.dll</HintPath>
    </Reference>
    <Reference Include="Serilog.Sinks.Console, Version=3.1.1.0, Culture=neutral, PublicKeyToken=24c2f752a8e58a10, processorArchitecture=MSIL">
      <HintPath>..\packages\Serilog.Sinks.Console.3.1.1\lib\net45\Serilog.Sinks.Console.dll</HintPath>
    </Reference>
    <Reference Include="Serilog.Sinks.Debug, Version=*******, Culture=neutral, PublicKeyToken=24c2f752a8e58a10, processorArchitecture=MSIL">
      <HintPath>..\packages\Serilog.Sinks.Debug.2.0.0\lib\net46\Serilog.Sinks.Debug.dll</HintPath>
    </Reference>
    <Reference Include="Serilog.Sinks.File, Version=*******, Culture=neutral, PublicKeyToken=24c2f752a8e58a10, processorArchitecture=MSIL">
      <HintPath>..\packages\Serilog.Sinks.File.4.1.0\lib\net45\Serilog.Sinks.File.dll</HintPath>
    </Reference>
    <Reference Include="Serilog.Sinks.Trace, Version=*******, Culture=neutral, PublicKeyToken=24c2f752a8e58a10, processorArchitecture=MSIL">
      <HintPath>..\packages\Serilog.Sinks.Trace.2.1.0\lib\net45\Serilog.Sinks.Trace.dll</HintPath>
    </Reference>
    <Reference Include="SerilogTraceListener, Version=0.0.0.0, Culture=neutral, PublicKeyToken=9398e41289d9b801, processorArchitecture=MSIL">
      <HintPath>..\packages\SerilogTraceListener.3.2.0\lib\net46\SerilogTraceListener.dll</HintPath>
    </Reference>
    <Reference Include="StackExchange.Redis, Version=*******, Culture=neutral, PublicKeyToken=c219ff1ca8c2ce46, processorArchitecture=MSIL">
      <HintPath>..\packages\StackExchange.Redis.2.0.601\lib\net461\StackExchange.Redis.dll</HintPath>
    </Reference>
    <Reference Include="Stripe.net, Version=********, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Stripe.net.47.2.0\lib\net461\Stripe.net.dll</HintPath>
    </Reference>
    <Reference Include="Sustainsys.Saml2, Version=********, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Sustainsys.Saml2.0.24.0\lib\net45\Sustainsys.Saml2.dll</HintPath>
    </Reference>
    <Reference Include="Sustainsys.Saml2.Owin, Version=********, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Sustainsys.Saml2.Owin.0.24.0\lib\net45\Sustainsys.Saml2.Owin.dll</HintPath>
    </Reference>
    <Reference Include="System.Buffers, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Immutable, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Collections.Immutable.1.5.0\lib\netstandard2.0\System.Collections.Immutable.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.Configuration.ConfigurationManager, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Configuration.ConfigurationManager.4.6.0\lib\net461\System.Configuration.ConfigurationManager.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Data.Linq" />
    <Reference Include="System.Data.OracleClient" />
    <Reference Include="System.Data.Services.Client" />
    <Reference Include="System.Diagnostics.DiagnosticSource, Version=9.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.DiagnosticSource.9.0.0\lib\net462\System.Diagnostics.DiagnosticSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.PerformanceCounter, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.PerformanceCounter.4.5.0\lib\net461\System.Diagnostics.PerformanceCounter.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.TraceSource.4.3.0\lib\net46\System.Diagnostics.TraceSource.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tracing" />
    <Reference Include="System.IdentityModel" />
    <Reference Include="System.IdentityModel.Tokens.Jwt, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IdentityModel.Tokens.Jwt.6.5.0\lib\net461\System.IdentityModel.Tokens.Jwt.dll</HintPath>
    </Reference>
    <Reference Include="System.IO, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.4.3.0\lib\net462\System.IO.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.Compression" />
    <Reference Include="System.IO.Compression.FileSystem" />
    <Reference Include="System.IO.Pipelines, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Pipelines.4.5.1\lib\netstandard2.0\System.IO.Pipelines.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Linq.4.1.0\lib\net463\System.Linq.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Management" />
    <Reference Include="System.Memory, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net" />
    <Reference Include="System.Net.Http, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.Http.4.3.4\lib\net46\System.Net.Http.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Net.Http.Extensions, Version=2.2.29.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Net.Http.2.2.29\lib\net45\System.Net.Http.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.Formatting, Version=5.2.7.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Client.5.2.7\lib\net45\System.Net.Http.Formatting.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.Primitives, Version=********, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Net.Http.2.2.29\lib\net45\System.Net.Http.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.WebRequest" />
    <Reference Include="System.Net.IPNetwork, Version=*******, Culture=neutral, PublicKeyToken=764160545cf0d618, processorArchitecture=MSIL">
      <HintPath>..\packages\IPNetwork2.2.0.3\lib\net40\System.Net.IPNetwork.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.TypeExtensions, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Reflection.TypeExtensions.4.1.0\lib\net46\System.Reflection.TypeExtensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.4.3.0\lib\net462\System.Runtime.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.Caching" />
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.InteropServices.RuntimeInformation.4.3.0\lib\net45\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Runtime.Serialization.Primitives, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.Serialization.Primitives.4.3.0\lib\net46\System.Runtime.Serialization.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Security" />
    <Reference Include="System.Security.AccessControl, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.AccessControl.4.6.0\lib\net461\System.Security.AccessControl.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Algorithms.4.3.0\lib\net463\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Encoding.4.3.0\lib\net46\System.Security.Cryptography.Encoding.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Primitives.4.3.0\lib\net46\System.Security.Cryptography.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.X509Certificates.4.3.0\lib\net461\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Xml, Version=4.0.1.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Xml.4.5.0\lib\net461\System.Security.Cryptography.Xml.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Permissions, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Permissions.4.6.0\lib\net461\System.Security.Permissions.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Principal.Windows, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Principal.Windows.4.6.0\lib\net461\System.Security.Principal.Windows.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Spatial, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Spatial.5.8.4\lib\net40\System.Spatial.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Text.Encodings.Web, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encodings.Web.4.4.0\lib\netstandard2.0\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Channels, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Channels.4.5.0\lib\netstandard2.0\System.Threading.Channels.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Transactions" />
    <Reference Include="System.ValueTuple, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net47\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Abstractions" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Helpers, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.Helpers.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=5.2.7.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.2.7\lib\net45\System.Web.Mvc.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Optimization, Version=1.1.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Web.Optimization.1.1.3\lib\net40\System.Web.Optimization.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.2.7\lib\net45\System.Web.Razor.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Routing" />
    <Reference Include="System.Web.WebPages, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="Twilio, Version=5.73.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Twilio.5.73.0\lib\net451\Twilio.dll</HintPath>
    </Reference>
    <Reference Include="Validation, Version=*******, Culture=neutral, PublicKeyToken=2fc06f0d701809a7, processorArchitecture=MSIL">
      <HintPath>..\packages\Validation.2.0.2.13022\lib\portable-windows8+net40+sl5+windowsphone8\Validation.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="WebActivator, Version=1.2.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\WebActivator.1.2.0.0\lib\NETFramework40\WebActivator.dll</HintPath>
    </Reference>
    <Reference Include="WebActivatorEx, Version=*******, Culture=neutral, PublicKeyToken=7b26dc2a43f6a0d4, processorArchitecture=MSIL">
      <HintPath>..\packages\WebActivatorEx.2.1.0\lib\net40\WebActivatorEx.dll</HintPath>
    </Reference>
    <Reference Include="WebGrease, Version=1.5.2.14234, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\WebGrease.1.5.2\lib\WebGrease.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="WindowsBase" />
    <Reference Include="zxing">
      <HintPath>..\packages\ZXing.Net.0.14.0.1\lib\net40\zxing.dll</HintPath>
    </Reference>
    <Reference Include="zxing.presentation">
      <HintPath>..\packages\ZXing.Net.0.14.0.1\lib\net40\zxing.presentation.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ApiClient\GeofenceApiClient.cs" />
    <Compile Include="ApiClient\GroupApiClient.cs" />
    <Compile Include="ApiClient\JobCodeApiClient.cs" />
    <Compile Include="ApiClient\PositionApiClient.cs" />
    <Compile Include="ApiClient\PunchApiClient.cs" />
    <Compile Include="ApiClient\PTOApiClient.cs" />
    <Compile Include="ApiClient\LocationApiClient.cs" />
    <Compile Include="ApiClient\PayPeriodApiClient.cs" />
    <Compile Include="ApiClient\TimeCardApiClient.cs" />
    <Compile Include="ApiClient\FaceApiClient.cs" />
    <Compile Include="ApiClient\HttpError.cs" />
    <Compile Include="ApiClient\IdentityApiClient.cs" />
    <Compile Include="ApiClient\RestClient.cs" />
    <Compile Include="App_Start\AutomapperConfiguration.cs" />
    <Compile Include="App_Start\BundleConfig.cs" />
    <Compile Include="App_Start\RegisterClientValidationExtensions.cs" />
    <Compile Include="Constant\CompanyConstant.cs" />
    <Compile Include="Constant\BenefitConstant.cs" />
    <Compile Include="Constant\States.cs" />
    <Compile Include="Controllers\Configuration\AddonFeatureController.cs" />
    <Compile Include="Controllers\Configuration\AddonFeatureViewModel.cs" />
    <Compile Include="Controllers\EmployeeController.Modals.cs" />
    <Compile Include="Controllers\ChurnKeyWebHookController.cs" />
    <Compile Include="Controllers\GroupController.cs" />
    <Compile Include="Controllers\StripeIntegrationTestController.cs" />
    <Compile Include="Exceptions\NotFoundException.cs" />
    <Compile Include="Filters\RedirectControllerAttribute.cs" />
    <Compile Include="Helpers\ConditionalRequiredAttribute.cs" />
    <Compile Include="Helpers\Export\HeartlandFileExporter.cs" />
    <Compile Include="Helpers\GoogleAnalytics\GoogleTagManagerHelper.cs" />
    <Compile Include="Helpers\GoogleAnalytics\GA4Helper.cs" />
    <Compile Include="Helpers\HandleHttpRequestValidationExceptionAttribute.cs" />
    <Compile Include="Helpers\JsonStreamingResult.cs" />
    <Compile Include="Controllers\MyPayrollController.cs" />
    <Compile Include="Controllers\PayrollController.cs" />
    <Compile Include="Controllers\EmployeeUploadController.cs" />
    <Compile Include="Controllers\iCalController.cs" />
    <Compile Include="Controllers\DeviceLockController.cs" />
    <Compile Include="Controllers\IntuitLogInController.cs" />
    <Compile Include="Controllers\MessageLogController.cs" />
    <Compile Include="Controllers\NotificationController.cs" />
    <Compile Include="Controllers\AlertRuleController.cs" />
    <Compile Include="Controllers\OvertimeTypeController.cs" />
    <Compile Include="Controllers\PayrollEmployeeUploadController.cs" />
    <Compile Include="Controllers\PunchLimitRuleController.cs" />
    <Compile Include="Controllers\ShiftTradeController.cs" />
    <Compile Include="Controllers\StripeController.cs" />
    <Compile Include="Controllers\CheckWebhookController.cs" />
    <Compile Include="Controllers\ZapierController.cs" />
    <Compile Include="Dto\StaffCreateInput.cs" />
    <Compile Include="Dto\StaffEditInput.cs" />
    <Compile Include="Exceptions\SamlException.cs" />
    <Compile Include="Helpers\CheckHelper.cs" />
    <Compile Include="Helpers\ConfigurationProvider.cs" />
    <Compile Include="Helpers\CsvService.cs" />
    <Compile Include="Helpers\HttpContextProvider.cs" />
    <Compile Include="Helpers\PartnerStackHelper.cs" />
    <Compile Include="Helpers\QuickBooksHelper.cs" />
    <Compile Include="Helpers\SetupMFAActionFilter.cs" />
    <Compile Include="Helpers\MFARequiredActionFilter.cs" />
    <Compile Include="Helpers\AjaxAuthorizeAttribute.cs" />
    <Compile Include="Helpers\AppleTokenResponse.cs" />
    <Compile Include="Helpers\AuthContextFilter.cs" />
    <Compile Include="Helpers\AuthorizeRoleAttribute.cs" />
    <Compile Include="Helpers\Export\PaycorFileExporter.cs" />
    <Compile Include="Helpers\ModelBinder\DateTimeModelBinder.cs" />
    <Compile Include="Helpers\ModelBinder\NullableDateTimeModelBinder.cs" />
    <Compile Include="Helpers\PhoneNumberHelper.cs" />
    <Compile Include="Helpers\UrlHelperExtensions.cs" />
    <Compile Include="Helpers\CollectionExtensions.cs" />
    <Compile Include="Helpers\MvcThrottleCustomFilter.cs" />
    <Compile Include="Helpers\EnsureMinimumElementsAttribute.cs" />
    <Compile Include="Helpers\EnumHelpers.cs" />
    <Compile Include="Helpers\Export\WePayFileExporter.cs" />
    <Compile Include="Helpers\QuickBooks\ClassQuery.cs" />
    <Compile Include="Helpers\QuickBooks\PreferencesQuery.cs" />
    <Compile Include="Helpers\SamlLoggerAdapter.cs" />
    <Compile Include="App_Start\WebMappingProfile.cs" />
    <Compile Include="App_Start\Startup.cs" />
    <Compile Include="Constants.cs" />
    <Compile Include="Controllers\AccountController.cs" />
    <Compile Include="Controllers\AccountPTOEarningCodeController.cs" />
    <Compile Include="Controllers\ApiManagementController.cs" />
    <Compile Include="Controllers\BaseController.cs" />
    <Compile Include="Controllers\AuthorTrackingController.cs" />
    <Compile Include="Controllers\BaseControllerBase.cs" />
    <Compile Include="Controllers\BreakRuleController.cs" />
    <Compile Include="BuddyPunchBackgroundException.cs" />
    <Compile Include="Controllers\CleanupOnDisconnectController.cs" />
    <Compile Include="Controllers\Configuration\ConfigurationController.cs" />
    <Compile Include="Controllers\AuthenticatedController.cs" />
    <Compile Include="Controllers\DataSetupController.cs" />
    <Compile Include="Controllers\EmployeePinController.cs" />
    <Compile Include="Controllers\ExportController.cs" />
    <Compile Include="Controllers\FaceController.cs" />
    <Compile Include="Controllers\IntegrationController.cs" />
    <Compile Include="Controllers\GeofenceController.cs" />
    <Compile Include="Controllers\PositionController.cs" />
    <Compile Include="Controllers\OvertimeAlertRuleController.cs" />
    <Compile Include="Controllers\PTOBankController.cs" />
    <Compile Include="Controllers\ScheduleController.cs" />
    <Compile Include="Controllers\PTOController.cs" />
    <Compile Include="Controllers\PunchRoundingController.cs" />
    <Compile Include="Controllers\QBWebConnectorController.cs" />
    <Compile Include="Controllers\QuickbooksController.cs" />
    <Compile Include="Controllers\QuickbooksOnlineController.cs" />
    <Compile Include="Controllers\QuickBooksTimeController.cs" />
    <Compile Include="Controllers\DirectConnectToIntuitController.cs" />
    <Compile Include="Controllers\DisconnectController.cs" />
    <Compile Include="Controllers\IpAddressLockController.cs" />
    <Compile Include="Controllers\MenuProxyController.cs" />
    <Compile Include="Controllers\NexmoController.cs" />
    <Compile Include="Controllers\OauthGrantController.cs" />
    <Compile Include="Controllers\OauthResponseController.cs" />
    <Compile Include="Controllers\OpenIdController.cs" />
    <Compile Include="Controllers\PublicLayoutController.cs" />
    <Compile Include="Controllers\LocationController.cs" />
    <Compile Include="Controllers\JobCodeController.cs" />
    <Compile Include="Controllers\PayPeriodController.cs" />
    <Compile Include="Controllers\EmployeeController.cs" />
    <Compile Include="Controllers\DiagnosticController.cs" />
    <Compile Include="Controllers\HelpController.cs" />
    <Compile Include="Controllers\MasterController.cs" />
    <Compile Include="Controllers\ProfileController.cs" />
    <Compile Include="Controllers\ShiftController.cs" />
    <Compile Include="Controllers\TerminalController.cs" />
    <Compile Include="Controllers\TimeCardController.cs" />
    <Compile Include="CsvExport.cs" />
    <Compile Include="Dto\EmployeeDto.cs" />
    <Compile Include="Helpers\BrowserInfo.cs" />
    <Compile Include="Helpers\ExcelXml\Workbook.cs" />
    <Compile Include="Helpers\AjaxErrorAttribute.cs" />
    <Compile Include="Helpers\CoupleSessionAndFormsAuth.cs" />
    <Compile Include="Helpers\CsvRenderer.cs" />
    <Compile Include="Helpers\CsvFieldHelper.cs" />
    <Compile Include="Helpers\ExcelXml\WorkbookWorksheetTableRowCell.cs" />
    <Compile Include="Helpers\Export\GustoFileExporter.cs" />
    <Compile Include="Helpers\Export\WorkdayFileExporter.cs" />
    <Compile Include="Helpers\Fingerprint.cs" />
    <Compile Include="Helpers\IdentityHelper.cs" />
    <Compile Include="Helpers\MetronicHelper.cs" />
    <Compile Include="Helpers\QuickBooks\ItemServiceQuery.cs" />
    <Compile Include="Helpers\QuickBooks\TimeTrackingAdd.cs" />
    <Compile Include="Helpers\QuickBooks\PayrollItemWageQuery.cs" />
    <Compile Include="Helpers\QuickBooks\EmployeeQuery.cs" />
    <Compile Include="Helpers\QuickBooks\CustomerQuery.cs" />
    <Compile Include="Helpers\QuickBooks\QuickBooksAuthenticator.cs" />
    <Compile Include="Helpers\QuickBooks\QuickBooksManager.cs" />
    <Compile Include="Helpers\QuickBooks\VersionValidator.cs" />
    <Compile Include="Helpers\ApprovalStatusHelper.cs" />
    <Compile Include="Helpers\StringHelper.cs" />
    <Compile Include="Helpers\WePayXml\WePayXmlSchema.cs" />
    <Compile Include="IoC\FluentValidatorFactory.cs" />
    <Compile Include="Managers\SessionManager.cs" />
    <Compile Include="Mappers\AccountPTOEarningCodeMapper.cs" />
    <Compile Include="Mappers\DeviceLockMapper.cs" />
    <Compile Include="Mappers\AlertRuleMapper.cs" />
    <Compile Include="Mappers\OvertimeAlertRuleMapper.cs" />
    <Compile Include="Mappers\OvertimeTypeRuleMapper.cs" />
    <Compile Include="Mappers\PositionMapper.cs" />
    <Compile Include="Mappers\PunchRoundingRuleMapper.cs" />
    <Compile Include="Mappers\TimeCardMapper.cs" />
    <Compile Include="Models\ActivateDeactivateResult.cs" />
    <Compile Include="Models\EmployeeCsvExport.cs" />
    <Compile Include="Models\JobCodeQbObjectGroup.cs" />
    <Compile Include="Models\LocationQbObjectGroup.cs" />
    <Compile Include="ViewModel\AccountRipplingModel.cs" />
    <Compile Include="ViewModel\AccountPaymentEvolutionModel.cs" />
    <Compile Include="ViewModel\AccountJustworksModel.cs" />
    <Compile Include="ViewModel\AccountWagepointModel.cs" />
    <Compile Include="ViewModel\Addons\AddonActionType.cs" />
    <Compile Include="ViewModel\Addons\AddonActionViewModel.cs" />
    <Compile Include="ViewModel\Addons\AddonPlanStatus.cs" />
    <Compile Include="ViewModel\Addons\AddonPlanViewModel.cs" />
    <Compile Include="ViewModel\Configuration\BillingHistoryViewModel.cs" />
    <Compile Include="ViewModel\CreateOffCyclePayrollViewModel.cs" />
    <Compile Include="ViewModel\Integration\HeartlandMappingViewModel.cs" />
    <Compile Include="ViewModel\GroupListViewModel.cs" />
    <Compile Include="ViewModel\EditGroupViewModel.cs" />
    <Compile Include="ViewModel\PayPlusMapping\NGPayrollExportViewModel.cs" />
    <Compile Include="ViewModel\PayrollCashRequirementViewModel.cs" />
    <Compile Include="ViewModel\QBMapObjectViewModel.cs" />
    <Compile Include="ViewModel\DeltekMappingViewModel.cs" />
    <Compile Include="ViewModel\DeltekExportViewModel.cs" />
    <Compile Include="ViewModel\ViewPointExportViewModel.cs" />
    <Compile Include="ViewModel\ViewPointMappingViewModel.cs" />
    <Compile Include="ViewModel\TriNetPayrollExportViewModel.cs" />
    <Compile Include="ViewModel\DeluxePayrollExportViewModel.cs" />
    <Compile Include="ViewModel\TriNetPayrollMappingViewModel.cs" />
    <Compile Include="ViewModel\DeluxePayrollMappingViewModel.cs" />
    <Compile Include="ViewModel\EarningCodeJobLocationHoursViewModel.cs" />
    <Compile Include="ViewModel\PayrollEmployeeUploadViewModel.cs" />
    <Compile Include="ViewModel\PayrollPeriodViewModel.cs" />
    <Compile Include="ViewModel\Payroll\ContractorPaymentsReportViewModel.cs" />
    <Compile Include="ViewModel\Payroll\EmployeeOnboardResultViewModel.cs" />
    <Compile Include="ViewModel\Payroll\EmployeeOnboardViewModel.cs" />
    <Compile Include="ViewModel\Payroll\BenefitViewModel.cs" />
    <Compile Include="ViewModel\Payroll\ContractorViewModel.cs" />
    <Compile Include="ViewModel\Payroll\PaystubsViewModel.cs" />
    <Compile Include="ViewModel\Payroll\StartDateEditViewModel.cs" />
    <Compile Include="ViewModel\Payroll\ReportByYearViewModel.cs" />
    <Compile Include="ViewModel\PreparePayrollViewModel.cs" />
    <Compile Include="ViewModel\PreviewPayrollViewModel.cs" />
    <Compile Include="ViewModel\RunPayrollViewModel.cs" />
    <Compile Include="ViewModel\SelectConfiguratorPhoneNumberViewModel.cs" />
    <Compile Include="ViewModel\VerifyCodeViewModel.cs" />
    <Compile Include="ViewModel\SendCodeViewModel.cs" />
    <Compile Include="ViewModel\BreakMapViewModel.cs" />
    <Compile Include="ViewModel\AccountISolvedModel.cs" />
    <Compile Include="ViewModel\Configuration\DeleteEmployeeTimesheetViewModel.cs" />
    <Compile Include="ViewModel\CustomOvertimeRuleViewModel.cs" />
    <Compile Include="ViewModel\EmployeeChangeOvertimeTypeViewModel.cs" />
    <Compile Include="ViewModel\EmployeeErrorLogReportViewModel.cs" />
    <Compile Include="ViewModel\AccountCancelModel.cs" />
    <Compile Include="ViewModel\AccountKioskModeSettingsModel.cs" />
    <Compile Include="ViewModel\AccountTimeEntryOptionsModel.cs" />
    <Compile Include="ViewModel\AdminChangeBillingEmailViewModel.cs" />
    <Compile Include="ViewModel\AbsenceReportViewModel.cs" />
    <Compile Include="ViewModel\AuditLogViewModel.cs" />
    <Compile Include="ViewModel\AutoPunchViewModel.cs" />
    <Compile Include="ViewModel\ChangePlanViewModel.cs" />
    <Compile Include="ViewModel\Configuration\CreateEmployeeTimesheetViewModel.cs" />
    <Compile Include="ViewModel\DeviceLockIndexViewModel.cs" />
    <Compile Include="ViewModel\EditPtoRequestViewModel.cs" />
    <Compile Include="ViewModel\AdminChangeUsernameViewModel.cs" />
    <Compile Include="ViewModel\EmployeeEditGeofencesViewModel.cs" />
    <Compile Include="ViewModel\EmployeeTimeEntryModeViewModel.cs" />
    <Compile Include="ViewModel\EmployeesEditSettingsViewModel.cs" />
    <Compile Include="ViewModel\EmployeeUploadViewModel.cs" />
    <Compile Include="ViewModel\GPSActivityViewModel.cs" />
    <Compile Include="ViewModel\GroupPunchViewModel.cs" />
    <Compile Include="ViewModel\Integration\GustoMappingViewModel.cs" />
    <Compile Include="ViewModel\Integration\MappingLineViewModel.cs" />
    <Compile Include="ViewModel\Integration\MappingViewModel.cs" />
    <Compile Include="ViewModel\GeofenceViewModel.cs" />
    <Compile Include="ViewModel\DeviceLockViewModel.cs" />
    <Compile Include="ViewModel\KioskPunchViewModel.cs" />
    <Compile Include="ViewModel\ManageQuickBooksPayrollItemsViewModel.cs" />
    <Compile Include="ViewModel\NameIdViewModel.cs" />
    <Compile Include="ViewModel\MessageLogViewModel.cs" />
    <Compile Include="ViewModel\Notification\StaffNotificationViewModel.cs" />
    <Compile Include="ViewModel\Notification\SendNotificationViewModel.cs" />
    <Compile Include="ViewModel\AlertRuleViewModel.cs" />
    <Compile Include="ViewModel\PayAmountViewModel.cs" />
    <Compile Include="ViewModel\ISolvedExportViewModel.cs" />
    <Compile Include="ViewModel\Paycor\PaycorMappingLineViewModel.cs" />
    <Compile Include="ViewModel\Paycor\PaycorMappingViewModel.cs" />
    <Compile Include="ViewModel\PaymentEvolutionExportViewModel.cs" />
    <Compile Include="ViewModel\PayRateAmountViewModel.cs" />
    <Compile Include="ViewModel\PayRateEditViewModel.cs" />
    <Compile Include="ViewModel\PayRateAddViewModel.cs" />
    <Compile Include="ViewModel\PayRateHistoryViewModel.cs" />
    <Compile Include="ViewModel\PayRatesSettingsViewModel.cs" />
    <Compile Include="ViewModel\PayRateViewModel.cs" />
    <Compile Include="ViewModel\Payroll\GetPaystubViewModel.cs" />
    <Compile Include="ViewModel\Payroll\PayrollAddCompanyViewModel.cs" />
    <Compile Include="ViewModel\Payroll\PayrollCompanyTaxDocumentsViewModel.cs" />
    <Compile Include="ViewModel\Payroll\PayrollCompanyViewModel.cs" />
    <Compile Include="ViewModel\Payroll\PayrollEmployeeViewModel.cs" />
    <Compile Include="ViewModel\Payroll\PayrollFilingAuthorizationDocumentsViewModel.cs" />
    <Compile Include="ViewModel\Payroll\PayrollJournalViewModel.cs" />
    <Compile Include="ViewModel\Payroll\PayrollPeopleListViewModel.cs" />
    <Compile Include="ViewModel\Payroll\PayrollPerson.cs" />
    <Compile Include="ViewModel\Payroll\PayrollSummaryViewModel.cs" />
    <Compile Include="ViewModel\Payroll\PayrollsViewModel.cs" />
    <Compile Include="ViewModel\Payroll\SignerInfoViewModel.cs" />
    <Compile Include="ViewModel\PlanViewModel.cs" />
    <Compile Include="ViewModel\PositionViewModel.cs" />
    <Compile Include="ViewModel\LocationJobCodesViewModel.cs" />
    <Compile Include="ViewModel\Paylocity\AccountPaylocityViewModel.cs" />
    <Compile Include="ViewModel\AdpRun\AccountAdpRunViewModel.cs" />
    <Compile Include="ViewModel\AdpRun\AdpRunMappingHourGroup.cs" />
    <Compile Include="ViewModel\AdpRun\AdpRunMappingLineViewModel.cs" />
    <Compile Include="ViewModel\AdpRun\AdpRunPayrollExportLine.cs" />
    <Compile Include="ViewModel\AdpRun\AdpRunPayrollExportViewModel.cs" />
    <Compile Include="ViewModel\AdpRun\AdpRunSetupException.cs" />
    <Compile Include="ViewModel\Adp\AccountAdpViewModel.cs" />
    <Compile Include="ViewModel\AccountPTOEarningCodeViewModel.cs" />
    <Compile Include="ViewModel\AccountTimesheetApprovalSettingsModel.cs" />
    <Compile Include="ViewModel\AccountWebcamSettingsModel.cs" />
    <Compile Include="ViewModel\AccountPaychexModel.cs" />
    <Compile Include="ViewModel\Adp\AdpHourGroupType.cs" />
    <Compile Include="ViewModel\Adp\AdpHourType.cs" />
    <Compile Include="ViewModel\Adp\AdpMappingHourGroup.cs" />
    <Compile Include="ViewModel\Adp\AdpMappingLineViewModel.cs" />
    <Compile Include="ViewModel\Adp\AdpPayrollExportLine.cs" />
    <Compile Include="ViewModel\Adp\AdpPayrollExportViewModel.cs" />
    <Compile Include="ViewModel\Adp\AdpSetupException.cs" />
    <Compile Include="ViewModel\DeleteReportViewModel.cs" />
    <Compile Include="ViewModel\EarningCodeJobHoursViewModel.cs" />
    <Compile Include="ViewModel\EmployeePTOBankBalanceViewModel.cs" />
    <Compile Include="ViewModel\EmployeePTOBankHistoryViewModel.cs" />
    <Compile Include="ViewModel\EmployeePTOBankSetupViewModel.cs" />
    <Compile Include="ViewModel\EmployeePTOBankTransactionViewModel.cs" />
    <Compile Include="ViewModel\EmployeeListEmployeeViewModel.cs" />
    <Compile Include="ViewModel\EmployeeWebcamPermissionViewModel.cs" />
    <Compile Include="ViewModel\FaceLoginRequestViewModel.cs" />
    <Compile Include="ViewModel\FaceLoginViewModel.cs" />
    <Compile Include="ViewModel\FaceProfilePictureViewModel.cs" />
    <Compile Include="ViewModel\OvertimeAlertRuleViewModel.cs" />
    <Compile Include="ViewModel\MappingType.cs" />
    <Compile Include="ViewModel\Paylocity\PaylocityMappingHourGroup.cs" />
    <Compile Include="ViewModel\Paylocity\PaylocitySetupException.cs" />
    <Compile Include="ViewModel\Paylocity\PaylocityPayrollExportLine.cs" />
    <Compile Include="ViewModel\Paylocity\PaylocityPayrollExportViewModel.cs" />
    <Compile Include="ViewModel\Paylocity\PaylocityMappingLineViewModel.cs" />
    <Compile Include="ViewModel\PayPlusMapping\PayPlusExportViewModel.cs" />
    <Compile Include="ViewModel\PayPlusMapping\PayPlusMappingViewModel.cs" />
    <Compile Include="ViewModel\PTOAccrualRuleViewModel.cs" />
    <Compile Include="ViewModel\PTOBankTransactionViewModel.cs" />
    <Compile Include="ViewModel\PTOCompanyCalendarViewModel.cs" />
    <Compile Include="ViewModel\PTOBlackoutDateViewModel.cs" />
    <Compile Include="ViewModel\PTOCalendar\PTOCalendarViewModel.cs" />
    <Compile Include="ViewModel\PTOTypeBankForEmployeeViewModel.cs" />
    <Compile Include="ViewModel\InOutActivityViewModel2.cs" />
    <Compile Include="ViewModel\PunchLimitRuleViewModel.cs" />
    <Compile Include="ViewModel\PurchasePlanViewModel.cs" />
    <Compile Include="ViewModel\QBMapObjectsViewModel.cs" />
    <Compile Include="ViewModel\QuickBooksOnlineIntegrationViewModel.cs" />
    <Compile Include="ViewModel\QuickSearchViewModel.cs" />
    <Compile Include="ViewModel\ReActivateAccountViewModel.cs" />
    <Compile Include="ViewModel\ReportBaseViewModel.cs" />
    <Compile Include="ViewModel\ReportByLineViewModel.cs" />
    <Compile Include="ViewModel\ScheduleSettingsViewModel.cs" />
    <Compile Include="ViewModel\Schedule\LogViewModel.cs" />
    <Compile Include="ViewModel\Schedule\ScheduleViewModel.cs" />
    <Compile Include="ViewModel\Schedule\AvailabilityViewModel.cs" />
    <Compile Include="ViewModel\Schedule\StaffAvailabilityPermissionsViewModel.cs" />
    <Compile Include="ViewModel\Schedule\StaffTradeCoverPermissionsViewModel.cs" />
    <Compile Include="ViewModel\SendMessageModalViewModel.cs" />
    <Compile Include="ViewModel\AvailabilityDto\AvailabilityDtoViewModel.cs" />
    <Compile Include="ViewModel\ShiftListViewModel.cs" />
    <Compile Include="ViewModel\ShiftTradeListViewModel.cs" />
    <Compile Include="ViewModel\Shift\ShiftViewModel.cs" />
    <Compile Include="ViewModel\Notification\NotificationLogViewModel.cs" />
    <Compile Include="ViewModel\SimpleTimesheetPunchViewModel.cs" />
    <Compile Include="ViewModel\StaffAccountPTOEarningCodeViewModel.cs" />
    <Compile Include="ViewModel\ReportByViewModel.cs" />
    <Compile Include="ViewModel\TimeEntry\TimeEntryEditExtViewModel.cs" />
    <Compile Include="ViewModel\TimeEntry\TimeEntryRowViewModel.cs" />
    <Compile Include="ViewModel\TimeEntry\CopyTimeCardViewModel.cs" />
    <Compile Include="ViewModel\TimeEntry\TimeEntryViewModel.cs" />
    <Compile Include="ViewModel\SapPayrollExportViewModel.cs" />
    <Compile Include="ViewModel\EarningCodesMappingViewModel.cs" />
    <Compile Include="ViewModel\VeevaReportViewModel.cs" />
    <Compile Include="ViewModel\SurePayrollExportViewModel.cs" />
    <Compile Include="ViewModel\SurePayrollMappingViewModel.cs" />
    <Compile Include="ViewModel\TimeSheetPunchAllocationViewModel.cs" />
    <Compile Include="ViewModel\TimeCardReportViewModel.cs" />
    <Compile Include="ViewModel\QbEmployeeViewModel.cs" />
    <Compile Include="ViewModel\QbEmployeesListViewModel.cs" />
    <Compile Include="ViewModel\QbTransactionViewModel.cs" />
    <Compile Include="ViewModel\QuickBooksDesktopIntegrationViewModel.cs" />
    <Compile Include="QBWebConnector.asmx.cs">
      <DependentUpon>QBWebConnector.asmx</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="QBWebConnectorBase.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ViewModel\AccountTerminalSettingsModel.cs" />
    <Compile Include="ViewModel\EmployeeChangePasswordViewModel.cs" />
    <Compile Include="ViewModel\EmployeePinListViewModel.cs" />
    <Compile Include="ViewModel\AdminListViewModel.cs" />
    <Compile Include="Startup.cs" />
    <Compile Include="ViewModel\AccountGpsSettingsModel.cs" />
    <Compile Include="ViewModel\AccountOvertimeModel.cs" />
    <Compile Include="ViewModel\AccountPTOModel.cs" />
    <Compile Include="ViewModel\EmployeeChangeUsernameViewModel.cs" />
    <Compile Include="ViewModel\EmployeeGpsPermissionViewModel.cs" />
    <Compile Include="ViewModel\GoogleMapViewModel.cs" />
    <Compile Include="ViewModel\HeaderViewModel.cs" />
    <Compile Include="ViewModel\PunchMapViewModel.cs" />
    <Compile Include="ViewModel\PunchRoundingRuleSummaryViewModel.cs" />
    <Compile Include="ViewModel\QboTransactionViewModel.cs" />
    <Compile Include="ViewModel\QuickbooksOnlineExportHistory.cs" />
    <Compile Include="ViewModel\QuickbooksTimeExportViewModel.cs" />
    <Compile Include="ViewModel\QboEmployeesListViewModel.cs" />
    <Compile Include="Controllers\QuickBooksEmployeesController.cs" />
    <Compile Include="Controllers\ReportController.cs" />
    <Compile Include="Controllers\StripeWebHookController.cs" />
    <Compile Include="Controllers\TrackingController.cs" />
    <Compile Include="Controllers\CrudBaseController.cs" />
    <Compile Include="Controllers\ErrorController.cs" />
    <Compile Include="Controllers\HomeController.cs" />
    <Compile Include="Exceptions\ClientNotFoundException.cs" />
    <Compile Include="Dto\ErrorDisplay.cs" />
    <Compile Include="Dto\Input.cs" />
    <Compile Include="Dto\LoginUniqueAttribute.cs" />
    <Compile Include="Dto\StrLenAttribute.cs" />
    <Compile Include="Exceptions\StaffNotFoundException.cs" />
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Helpers\AntiForgeryTokenFilterProvider.cs" />
    <Compile Include="Helpers\ApplicationConstants.cs" />
    <Compile Include="Helpers\CryptographyHelper.cs" />
    <Compile Include="Helpers\CustomerData.cs" />
    <Compile Include="Helpers\oAuth.cs" />
    <Compile Include="Helpers\OauthAccessTokenStorageHelper.cs" />
    <Compile Include="Helpers\openid.cs" />
    <Compile Include="Helpers\QrCodeHelper.cs" />
    <Compile Include="Helpers\ParameterNameMapAttribute.cs" />
    <Compile Include="Helpers\NavigationHelper.cs" />
    <Compile Include="Helpers\NoCacheAttribute.cs" />
    <Compile Include="Helpers\CompressResponseAttribute .cs" />
    <Compile Include="Helpers\ControllerExtension.cs" />
    <Compile Include="Helpers\ControllerHelper.cs" />
    <Compile Include="Helpers\DatePickerHelper.cs" />
    <Compile Include="Helpers\HandleErrorWithELMAHAttribute .cs" />
    <Compile Include="Helpers\ImageActionLink.cs" />
    <Compile Include="Helpers\RedirectMobileDevicesToMobileAreaAttribute.cs" />
    <Compile Include="Helpers\SmsSender.cs" />
    <Compile Include="Helpers\TimeZoneHelper.cs" />
    <Compile Include="Helpers\ToSelectListItems.cs" />
    <Compile Include="Helpers\UserTracker.cs" />
    <Compile Include="IoC\CustomControllerActivator.cs" />
    <Compile Include="IoC\UnityControllerFactory .cs" />
    <Compile Include="IoC\UnityDependencyResolver.cs" />
    <Compile Include="Log\LoggingService.cs" />
    <Compile Include="Managers\CacheManager.cs" />
    <Compile Include="Mappers\BreakRuleMapper.cs" />
    <Compile Include="Mappers\IpAddressLockMapper.cs" />
    <Compile Include="Mappers\EmployeeMapper.cs" />
    <Compile Include="Mappers\LocationMapper.cs" />
    <Compile Include="Mappers\EntitiesToInts.cs" />
    <Compile Include="Mappers\IMapper.cs" />
    <Compile Include="Mappers\IntsToEntities.cs" />
    <Compile Include="Mappers\JobCodeMapper.cs" />
    <Compile Include="Mappers\Mapper.cs" />
    <Compile Include="Mappers\NormalToNullables.cs" />
    <Compile Include="Mappers\NullablesToNormal.cs" />
    <Compile Include="Mappers\TimesheetMapper.cs" />
    <Compile Include="Mappers\UserMapper.cs" />
    <Compile Include="Models\AccountModels.cs" />
    <Compile Include="ViewModel\AccountQrCodeSettingsModel.cs" />
    <Compile Include="ViewModel\BreakRuleViewModel.cs" />
    <Compile Include="ViewModel\QboEmployeeViewModel.cs" />
    <Compile Include="ViewModel\AddPunchViewModel.cs" />
    <Compile Include="ViewModel\IpAddressLockViewModel.cs" />
    <Compile Include="ViewModel\DeleteShiftViewModel.cs" />
    <Compile Include="ViewModel\InOutDetailReportViewModel.cs" />
    <Compile Include="ViewModel\LocationHoursViewModel.cs" />
    <Compile Include="ViewModel\ProfileViewModel.cs" />
    <Compile Include="ViewModel\SummaryHoursViewModel.cs" />
    <Compile Include="ViewModel\PayAmountHistoryViewModel.cs" />
    <Compile Include="ViewModel\EditBreakModel.cs" />
    <Compile Include="ViewModel\PTODetailViewModel.cs" />
    <Compile Include="ViewModel\PTOSummaryViewModel.cs" />
    <Compile Include="ViewModel\DailyHoursViewModel.cs" />
    <Compile Include="ViewModel\InOutActivityViewModel.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Providers\ExtensionMethods.cs" />
    <Compile Include="Providers\DatabaseExtensionMethods.cs" />
    <Compile Include="Providers\TableMembershipProvider.cs" />
    <Compile Include="Providers\ConfigurationExtensionMethods.cs" />
    <Compile Include="Settings.cs" />
    <Compile Include="BuddyPunchWebException.cs" />
    <Compile Include="UnityControllerFactory.cs" />
    <Compile Include="ViewModel\AccountEditModel.cs" />
    <Compile Include="ViewModel\AccountSettingsModel.cs" />
    <Compile Include="ViewModel\AddBreakViewModel.cs" />
    <Compile Include="ViewModel\AccountSetupModel.cs" />
    <Compile Include="ViewModel\LocationViewModel.cs" />
    <Compile Include="ViewModel\EarningCodeHoursViewModel.cs" />
    <Compile Include="ViewModel\EmployeeListViewModel.cs" />
    <Compile Include="ViewModel\JobCodeViewModel.cs" />
    <Compile Include="ViewModel\ManagerEmployeePermissionViewModel.cs" />
    <Compile Include="ViewModel\FooterViewModel.cs" />
    <Compile Include="ViewModel\PayPeriodsViewModel.cs" />
    <Compile Include="ViewModel\PayPeriodViewModel.cs" />
    <Compile Include="ViewModel\PTOViewModel.cs" />
    <Compile Include="ViewModel\SetupEmployeesModel.cs" />
    <Compile Include="ViewModel\SideBarViewModel.cs" />
    <Compile Include="ViewModel\AccountingCsExportViewModel.cs" />
    <Compile Include="ViewModel\PaychexExportViewModel.cs" />
    <Compile Include="ViewModel\SwitchAccountViewModel.cs" />
    <Compile Include="ViewModel\TerminalViewModel.cs" />
    <Compile Include="ViewModel\TimeCardsViewModel.cs" />
    <Compile Include="ViewModel\DashboardViewModel.cs" />
    <Compile Include="ViewModel\EditPunchModel.cs" />
    <Compile Include="ViewModel\CurrentActivityViewModel.cs" />
    <Compile Include="ViewModel\EmployeePunchViewModel.cs" />
    <Compile Include="ViewModel\PayPeriodSummaryViewModel.cs" />
    <Compile Include="ViewModel\EmployeeViewModel.cs" />
    <Compile Include="ViewModel\TimeCardViewModel.cs" />
    <Compile Include="ViewModel\TimesheetPunchViewModel.cs" />
    <Compile Include="ViewModel\TopNavViewModel.cs" />
    <Compile Include="ViewModel\UserViewModel.cs" />
    <Compile Include="ViewModel\WorkdayMapping\WorkdayMappingViewModel.cs" />
    <Content Include="Content\images\buddypunch_logo_black.svg" />
    <Content Include="Content\images\buddypunch_logo_white.svg" />
    <Content Include="ILLink\ILLink.Descriptors.LibraryBuild.xml" />
    <Content Include="src\images\integration\logos\heartland.png" />
    <Content Include="Views\Geofence\GeofenceIntro.cshtml" />
    <Content Include="Views\Account\QrCodeIntro.cshtml" />
    <Content Include="Views\Account\WebcamIntro.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="337614aa27d308aecdcc5436f6b11e8752f8cb5c87fc274bc5673a5701e67b17.html" />
    <Content Include="apple-touch-icon-114x114.png" />
    <Content Include="apple-touch-icon-120x120.png" />
    <Content Include="apple-touch-icon-144x144.png" />
    <Content Include="apple-touch-icon-152x152.png" />
    <Content Include="apple-touch-icon-57x57.png" />
    <Content Include="apple-touch-icon-72x72.png" />
    <Content Include="apple-touch-icon-76x76.png" />
    <Content Include="apple-touch-icon.png" />
    <EmbeddedResource Include="ExportTemplates\WorkdayTemplate.xml" />
    <Content Include="Content\css\calendar_print.css" />
    <Content Include="Content\images\PunchRoundingRules\after.png" />
    <Content Include="Content\images\PunchRoundingRules\after_out.png" />
    <Content Include="Content\images\PunchRoundingRules\before.png" />
    <Content Include="Content\images\PunchRoundingRules\before_out.png" />
    <Content Include="Content\images\Setup\left2.png" />
    <Content Include="Content\images\Setup\left_sch.png" />
    <Content Include="Content\images\wizard-step-1.png" />
    <Content Include="Content\images\wizard-step-3.png" />
    <Content Include="Content\images\wizard-step-4.png" />
    <Content Include="Content\images\wizard-step-5.png" />
    <Content Include="Content\images\Setup\right2.png" />
    <EmbeddedResource Include="ExportTemplates\WePayExportTemplate.xml" />
    <Content Include="Content\images\Setup\right_sch.png" />
    <Content Include="Content\images\Setup\right_time.png" />
    <Content Include="Content\sessionTimeoutWorker.js" />
    <Content Include="gulpfile.js" />
    <Content Include="bin\Altairis.Web.Security.dll" />
    <Content Include="bin\Common.Logging.dll" />
    <Content Include="bin\Microsoft.Data.Edm.dll" />
    <Content Include="bin\Microsoft.Data.OData.dll" />
    <Content Include="bin\Microsoft.Practices.Unity.dll" />
    <Content Include="bin\Microsoft.Web.Infrastructure.dll" />
    <Content Include="bin\MvcCheckBoxList.dll" />
    <Content Include="bin\MvcPaging.dll" />
    <Content Include="bin\Omu.ValueInjecter.dll" />
    <Content Include="bin\System.Spatial.dll" />
    <Content Include="bin\System.Web.Helpers.dll" />
    <Content Include="bin\System.Web.Mvc.dll" />
    <Content Include="bin\System.Web.Razor.dll" />
    <Content Include="bin\System.Web.WebPages.Deployment.dll" />
    <Content Include="bin\System.Web.WebPages.dll" />
    <Content Include="bin\System.Web.WebPages.Razor.dll" />
    <Content Include="bin\WebActivator.dll" />
    <Content Include="browserconfig.xml" />
    <Content Include="Content\images\GettingStarted\Account_settings.png" />
    <Content Include="Content\images\GettingStarted\billing.png" />
    <Content Include="Content\images\GettingStarted\Dashboard.png" />
    <Content Include="Content\images\GettingStarted\Employees.png" />
    <Content Include="Content\images\GettingStarted\Employees_add.png" />
    <Content Include="Content\images\GettingStarted\Employee\iforgot_1.00.png" />
    <Content Include="Content\images\GettingStarted\Employee\I_forgot_ee_1.00.png" />
    <Content Include="Content\images\GettingStarted\Employee\punchinout_1.png" />
    <Content Include="Content\images\GettingStarted\Employee\Welcome1.02ee.png" />
    <Content Include="Content\images\GettingStarted\payperiods.png" />
    <Content Include="Content\images\GettingStarted\TimeCards.png" />
    <Content Include="Content\images\GettingStarted\Welcome.png" />
    <Content Include="Content\images\Setup\admin_view.fw.png" />
    <Content Include="Content\images\Setup\ee_view.fw.png" />
    <Content Include="Content\images\Setup\left.png" />
    <Content Include="Content\images\Setup\right.png" />
    <Content Include="Content\Reveal\css\gh-fork-ribbon.css" />
    <Content Include="Content\Reveal\css\print\paper.css" />
    <Content Include="Content\Reveal\css\print\pdf.css" />
    <Content Include="Content\Reveal\css\reveal.css" />
    <Content Include="Content\Reveal\css\reveal.min.css" />
    <Content Include="Content\Reveal\css\theme\adobe-theme.css" />
    <Content Include="Content\Reveal\css\theme\beige.css" />
    <Content Include="Content\Reveal\css\theme\blood.css" />
    <Content Include="Content\Reveal\css\theme\default.css" />
    <Content Include="Content\Reveal\css\theme\moon.css" />
    <Content Include="Content\Reveal\css\theme\night.css" />
    <Content Include="Content\Reveal\css\theme\serif.css" />
    <Content Include="Content\Reveal\css\theme\simple.css" />
    <Content Include="Content\Reveal\css\theme\sky.css" />
    <Content Include="Content\Reveal\css\theme\solarized.css" />
    <Content Include="Content\Reveal\exit.html" />
    <Content Include="Content\Reveal\index2.html" />
    <Content Include="Content\Reveal\index_ie.html" />
    <Content Include="Content\Reveal\js\bangers.js" />
    <Content Include="Content\Reveal\js\html5shiv-printshiv.js" />
    <Content Include="Content\Reveal\js\html5shiv-printshiv.min.js" />
    <Content Include="Content\Reveal\js\html5shiv.js" />
    <Content Include="Content\Reveal\js\html5shiv.min.js" />
    <Content Include="Content\Reveal\js\reveal.js" />
    <Content Include="Content\Reveal\js\reveal.min.js" />
    <Content Include="Content\Reveal\lib\css\zenburn.css" />
    <Content Include="Content\Reveal\lib\font\league_gothic-webfont.svg" />
    <Content Include="Content\Reveal\lib\js\classList.js" />
    <Content Include="Content\Reveal\lib\js\head.min.js" />
    <Content Include="Content\Reveal\lib\js\html5shiv.js" />
    <Content Include="Content\Reveal\plugin\highlight\highlight.js" />
    <Content Include="Content\Reveal\plugin\leap\leap.js" />
    <Content Include="Content\Reveal\plugin\markdown\example.html" />
    <Content Include="Content\Reveal\plugin\markdown\markdown.js" />
    <Content Include="Content\Reveal\plugin\markdown\marked.js" />
    <Content Include="Content\Reveal\plugin\math\math.js" />
    <Content Include="Content\Reveal\plugin\multiplex\client.js" />
    <Content Include="Content\Reveal\plugin\multiplex\index.js" />
    <Content Include="Content\Reveal\plugin\multiplex\master.js" />
    <Content Include="Content\Reveal\plugin\notes-server\client.js" />
    <Content Include="Content\Reveal\plugin\notes-server\index.js" />
    <Content Include="Content\Reveal\plugin\notes-server\notes.html" />
    <Content Include="Content\Reveal\plugin\notes\notes.html" />
    <Content Include="Content\Reveal\plugin\notes\notes.js" />
    <Content Include="Content\Reveal\plugin\postmessage\example.html" />
    <Content Include="Content\Reveal\plugin\postmessage\postmessage.js" />
    <Content Include="Content\Reveal\plugin\print-pdf\print-pdf.js" />
    <Content Include="Content\Reveal\plugin\remotes\remotes.js" />
    <Content Include="Content\Reveal\plugin\search\search.js" />
    <Content Include="Content\Reveal\plugin\zoom-js\zoom.js" />
    <Content Include="Content\Reveal\resources\1.01_final.png" />
    <Content Include="Content\Reveal\resources\1.02_final.png" />
    <Content Include="Content\Reveal\resources\1.a.png" />
    <Content Include="Content\Reveal\resources\1.b.png" />
    <Content Include="Content\Reveal\resources\1.c.png" />
    <Content Include="Content\Reveal\resources\10.01_final.png" />
    <Content Include="Content\Reveal\resources\11.01_final.png" />
    <Content Include="Content\Reveal\resources\2.01_final.png" />
    <Content Include="Content\Reveal\resources\2.02_final.png" />
    <Content Include="Content\Reveal\resources\3.01_final.png" />
    <Content Include="Content\Reveal\resources\3.02_final.png" />
    <Content Include="Content\Reveal\resources\4.01_final.png" />
    <Content Include="Content\Reveal\resources\5.01_final.png" />
    <Content Include="Content\Reveal\resources\5.02_final.png" />
    <Content Include="Content\Reveal\resources\5.03_final.png" />
    <Content Include="Content\Reveal\resources\6.01_final.png" />
    <Content Include="Content\Reveal\resources\7.01_final.png" />
    <Content Include="Content\Reveal\resources\8.01_final.png" />
    <Content Include="Content\Reveal\resources\9.01_final.png" />
    <Content Include="Content\Reveal\resources\9.02_final.png" />
    <Content Include="Content\Reveal\resources\a.128.png" />
    <Content Include="Content\Reveal\resources\a.24.png" />
    <Content Include="Content\Reveal\resources\a.35.png" />
    <Content Include="Content\Reveal\resources\a.png" />
    <Content Include="Content\Reveal\resources\see.png" />
    <Content Include="Content\Time\payments.html" />
    <Content Include="EmployeeQuery.xml" />
    <Content Include="favicon.ico" />
    <Content Include="iFrameTest.html" />
    <Content Include="large.png" />
    <Content Include="OauthAccessTokenStorage.xml" />
    <Content Include="parameters.xml" />
    <Content Include="QBWebConnector.asmx" />
    <Content Include="README.jQuery.vsdoc.txt" />
    <Content Include="Scripts\text-mask-addons\createNumberMask.js" />
    <Content Include="src\images\fav-icon-buddy-punch-150x150.png" />
    <Content Include="src\images\fav-icon-buddy-punch.png" />
    <Content Include="src\images\integration\logos\adp_workforce_now.png" />
    <Content Include="src\images\integration\logos\deltek_logo.png" />
    <Content Include="src\images\integration\logos\deluxepayroll.png" />
    <Content Include="src\images\integration\logos\gusto.png" />
    <Content Include="src\images\integration\logos\isolved.png" />
    <Content Include="src\images\integration\logos\justworks.png" />
    <Content Include="src\images\integration\logos\paychex.png" />
    <Content Include="src\images\integration\logos\paycor.png" />
    <Content Include="src\images\integration\logos\paylocity.png" />
    <Content Include="src\images\integration\logos\paymentevolution.png" />
    <Content Include="src\images\integration\logos\payplus.png" />
    <Content Include="src\images\integration\logos\quickbooks-brand.png" />
    <Content Include="src\images\integration\logos\quickbooks-brand-d.png" />
    <Content Include="src\images\integration\logos\surepayroll.png" />
    <Content Include="src\images\integration\logos\trinetpayroll.png" />
    <Content Include="src\images\integration\logos\viewpoint-logo.png" />
    <Content Include="src\images\integration\logos\wagepoint.png" />
    <Content Include="src\images\integration\logos\workday.png" />
    <Content Include="src\images\integration\logos\zapier.png" />
    <Content Include="src\images\intuit-signin\C2QB_green_btn_med_default.png" />
    <Content Include="src\images\intuit-signin\C2QB_green_btn_med_hover.png" />
    <Content Include="src\images\isolved_logo.png" />
    <Content Include="src\images\justworks_logo.png" />
    <Content Include="src\images\PayEvo.png" />
    <Content Include="src\images\integration\logos\rippling.png" />
    <Content Include="src\images\wagepoint_logo.png" />
    <Content Include="src\plugins\markerclusterer.js" />
    <Content Include="src\scripts\elementsModal.js" />
    <Content Include="Scripts\jquery.smartbanner\jquery.smartbanner.css" />
    <Content Include="Scripts\jquery.smartbanner\jquery.smartbanner.js" />
    <Content Include="Scripts\utm_form\utm_form-1.1.0.min.js" />
    <Content Include="Scripts\utm_form\utm_form-1.0.2.min.js" />
    <Content Include="Scripts\webcamjs\webcam-modified.js" />
    <Content Include="Scripts\webcamjs\webcam-modified.min.js" />
    <Content Include="Scripts\webcamjs\webcam.js" />
    <Content Include="Scripts\webcamjs\webcam.swf" />
    <Content Include="src\css\custom.css" />
    <Content Include="src\css\elementsModal.css" />
    <Content Include="src\css\error.css" />
    <Content Include="src\css\facelogin.css" />
    <Content Include="src\css\profile.css" />
    <Content Include="src\css\qrscanner.css" />
    <Content Include="Scripts\scheduler.min.css" />
    <Content Include="src\images\ADP-workforce-now-logo.png" />
    <Content Include="src\images\apple-signin\apple-icon.png" />
    <Content Include="src\images\apple-signin\appleid_button%401x.png" />
    <Content Include="src\images\app_white.png" />
    <Content Include="src\images\app_white_154.png" />
    <Content Include="src\images\Buddy-Punch_41.png" />
    <Content Include="src\images\bp_help.png" />
    <Content Include="src\images\bp_logo3.fw.png" />
    <Content Include="src\images\bp_user.png" />
    <Content Include="src\images\Buddy Punch Login.png" />
    <Content Include="src\images\Buddy Punch Logo2 White.png" />
    <Content Include="src\images\Buddy Punch purple dash.fw.png" />
    <Content Include="src\images\Buddy Punch purple dash2.fw.png" />
    <Content Include="src\images\Buddy Punch White_small.fw.png" />
    <Content Include="src\images\Buddy Punch.png" />
    <Content Include="src\images\Buddy-Punch-White35.fw_.fw.png" />
    <Content Include="src\images\Buddy-Punch_53.png" />
    <Content Include="src\images\Buddy-Punch.png" />
    <Content Include="src\images\Buddy-Punch_Header_Logo.png" />
    <Content Include="src\images\buddy_punch_timeclock_software_logo.png" />
    <Content Include="src\images\signup\feedback-rate-icon.png" />
    <Content Include="src\images\signup\feedback-user-1.png" />
    <Content Include="src\images\signup\feedback-user-2.png" />
    <Content Include="src\images\signup\feedback-user-3.png" />
    <Content Include="src\images\signup\feedback-company-1.png" />
    <Content Include="src\images\signup\feedback-company-2.png" />
    <Content Include="src\images\signup\feedback-company-3.png" />
    <Content Include="src\images\signup\info-icon-1.png" />
    <Content Include="src\images\signup\info-icon-2.png" />
    <Content Include="src\images\signup\info-icon-3.png" />
    <Content Include="src\images\signup\info-icon-4.png" />
    <Content Include="src\images\signup\info-icon-5.png" />
    <Content Include="src\images\signup\award-img-1.png" />
    <Content Include="src\images\signup\award-img-2.png" />
    <Content Include="src\images\signup\award-img-3.png" />
    <Content Include="Content\images\buddypunch_logo.svg" />
    <Content Include="Content\icons\regulation.svg" />
    <Content Include="src\images\buddy_punch_timeclock_software_logo_70_blue.png" />
    <Content Include="src\images\integration\paycor.png" />
    <Content Include="src\images\paycor.png" />
    <Content Include="src\images\payment-cards\cc_1.png" />
    <Content Include="src\images\payment-cards\american-express.jpg" />
    <Content Include="src\images\payment-cards\diners-club.png" />
    <Content Include="src\images\payment-cards\discover.jpg" />
    <Content Include="src\images\payment-cards\jcb.png" />
    <Content Include="src\images\payment-cards\master-card.png" />
    <Content Include="src\images\payment-cards\union-pay.png" />
    <Content Include="src\images\payment-cards\visa.png" />
    <Content Include="src\images\default_qrcode.png" />
    <Content Include="src\images\email_logo.png" />
    <Content Include="src\images\facial_recognition_login.png" />
    <Content Include="Content\images\google-signup-button.svg" />
    <Content Include="Content\images\google-signup-button-hover.svg" />
    <Content Include="Content\images\dashboard\info.svg" />
    <Content Include="Content\images\dashboard\info-active.svg" />
    <Content Include="Content\images\dashboard\add.svg" />
    <Content Include="Content\images\dashboard\remove.svg" />
    <Content Include="Content\images\dashboard\clock.svg" />
    <Content Include="Content\images\dashboard\people.svg" />
    <Content Include="Content\images\dashboard\people-red.svg" />
    <Content Include="Content\images\dashboard\people-green.svg" />
    <Content Include="Content\images\dashboard\timecard.svg" />
    <Content Include="Content\images\wizard\Info.svg" />
    <Content Include="src\images\google-signin\btn_google_signin_dark_normal_web.png" />
    <Content Include="src\images\google-signin\btn_google_signin_light_normal_web.png" />
    <Content Include="src\images\google-signin\g-logo.png" />
    <Content Include="src\images\gusto.png" />
    <Content Include="src\images\help.png" />
    <Content Include="src\images\integration\gusto.png" />
    <Content Include="src\images\intuit-signin\Sign_in_blue_btn_lg_default.png" />
    <Content Include="src\images\intuit-signin\Sign_in_blue_btn_lg_hover.png" />
    <Content Include="src\images\intuit-signin\Sign_in_blue_btn_med_default.png" />
    <Content Include="src\images\intuit-signin\Sign_in_blue_btn_med_hover.png" />
    <Content Include="src\images\intuit-signin\Sign_in_blue_btn_sm_default.png" />
    <Content Include="src\images\intuit-signin\Sign_in_blue_btn_sm_hover.png" />
    <Content Include="src\images\intuit-signin\Sign_in_white_btn_lg_default.png" />
    <Content Include="src\images\intuit-signin\Sign_in_white_btn_lg_hover.png" />
    <Content Include="src\images\intuit-signin\Sign_in_white_btn_med_default.png" />
    <Content Include="src\images\intuit-signin\Sign_in_white_btn_med_hover.png" />
    <Content Include="src\images\intuit-signin\Sign_in_white_btn_sm_default.png" />
    <Content Include="src\images\intuit-signin\Sign_in_white_btn_sm_hover.png" />
    <Content Include="src\images\intuit-signin\Sign_in_blue_btn_med_default2x.png" />
    <Content Include="src\images\intuit-signin\Sign_in_blue_btn_med_hover2x.png" />
    <Content Include="src\images\apple\<EMAIL>" />
    <Content Include="src\images\loading-spinner-grey.gif" />
    <Content Include="src\images\logo.png" />
    <Content Include="src\images\new_employee_face2.jpg" />
    <Content Include="src\images\nick_300.png" />
    <Content Include="src\images\nick_35_height.png" />
    <Content Include="src\images\nick_600.png" />
    <Content Include="src\images\Paychex-logo.jpg" />
    <Content Include="src\images\paylocity-logo.png" />
    <Content Include="src\images\payplus-software-inc-logo.png" />
    <Content Include="src\images\qb16x16.png" />
    <Content Include="src\images\qb32x32.png" />
    <Content Include="src\images\qb64x64.png" />
    <Content Include="src\images\qb_desktop_logo.png" />
    <Content Include="src\images\qb_online_logo.png" />
    <Content Include="src\images\quickbooks_logo_horz.png" />
    <Content Include="src\images\quickbooks_logo_horz_2016.png" />
    <Content Include="src\images\reseller\aaib_front.jpg" />
    <Content Include="src\images\run_apd_buddy_punch.png" />
    <Content Include="src\images\SurePayroll-logo.png" />
    <Content Include="src\images\upgrade.png" />
    <Content Include="src\images\user58.png" />
    <Content Include="src\images\workday_logo.png" />
    <Content Include="src\images\zapier-logo.png" />
    <Content Include="src\plugins\countdown\countdownBasic.html" />
    <Content Include="src\plugins\countdown\countdownGlowing.gif" />
    <Content Include="src\plugins\countdown\countdownLED.png" />
    <Content Include="src\plugins\countdown\jquery.countdown.css" />
    <Content Include="src\plugins\countdown\jquery.countdown.js" />
    <Content Include="src\plugins\jsqrcode\llqrcode.js" />
    <Content Include="src\plugins\jsqrcode\webqr.js" />
    <Content Include="src\scriptsIE8\excanvas.min.js" />
    <Content Include="src\scriptsIE8\respond.min.js" />
    <Content Include="src\scripts\DataTables.2.1.0.js" />
    <Content Include="src\scripts\login.js" />
    <Content Include="src\scripts\loginPin.js" />
    <Content Include="src\scripts\pages\availability.js" />
    <Content Include="src\scripts\pages\dashboardtimesheets.js" />
    <Content Include="src\scripts\pages\payrolls-table.js" />
    <Content Include="src\scripts\pages\shiftrequests.js" />
    <Content Include="src\scripts\pages\pendingapproval.js" />
    <Content Include="src\scripts\pages\schedule.js" />
    <Content Include="src\scripts\pages\ptocalendar.js" />
    <Content Include="src\scripts\pages\timecard.page.js" />
    <Content Include="src\scripts\freezeTable.js" />
    <Content Include="src\scripts\polyfills.js" />
    <Content Include="src\scripts\quick-search.js" />
    <Content Include="Scripts\scheduler.min.js" />
    <Content Include="src\scripts\select-all-table.js" />
    <Content Include="src\scripts\sendMessageModal.js" />
    <Content Include="src\scripts\services\notification.service.js" />
    <Content Include="src\scripts\services\pto.service.js" />
    <Content Include="src\scripts\services\availability.service.js" />
    <Content Include="src\scripts\services\schedule.service.js" />
    <Content Include="src\scripts\services\shiftrequest.service.js" />
    <Content Include="src\scripts\services\timecard.service.js" />
    <Content Include="src\plugins\decoder.min.js" />
    <Content Include="src\scripts\jquery.sticky.js" />
    <Content Include="src\scripts\utils\fullCalendarJumpToDateWidget.js" />
    <Content Include="src\scripts\utils\commonUtils.js" />
    <Content Include="src\scripts\utils\dynamicTable.js" />
    <Content Include="src\scripts\utils\maps-config.js" />
    <Content Include="src\scripts\utils\qrreader.js" />
    <Content Include="src\scripts\utils\qrscan.page.js" />
    <Content Include="src\scripts\utils\bpUIBlock.js" />
    <Content Include="src\scripts\utils\baseLoader.js" />
    <Content Include="src\scripts\utils\select2-custom-matcher.js" />
    <Content Include="src\scripts\validation.js" />
    <Content Include="src\scripts\vue\timecard.vue.js" />
    <Content Include="src\scss\buttons.scss" />
    <Content Include="src\scss\custom.scss" />
    <Content Include="src\theme\demo\demo5\base\scripts.bundle.js" />
    <Content Include="src\theme\demo\demo5\base\style.bundle.css" />
    <Content Include="src\theme\demo\demo5\media\img\logo\favicon.ico" />
    <Content Include="src\theme\demo\demo5\media\img\logo\logo.png" />
    <Content Include="src\theme\vendors\base\fonts\font-awesome\fontawesome-webfont.eot" />
    <Content Include="src\theme\vendors\base\fonts\font-awesome\fontawesome-webfont.svg" />
    <Content Include="src\theme\vendors\base\fonts\font-awesome\fontawesome-webfont.ttf" />
    <Content Include="src\theme\vendors\base\fonts\font-awesome\fontawesome-webfont.woff" />
    <Content Include="src\theme\vendors\base\fonts\font-awesome\fontawesome-webfont.woff2" />
    <Content Include="src\theme\vendors\base\fonts\font-awesome\FontAwesome.otf" />
    <Content Include="src\theme\vendors\base\fonts\line-awesome\line-awesome.eot" />
    <Content Include="src\theme\vendors\base\fonts\line-awesome\line-awesome.svg" />
    <Content Include="src\theme\vendors\base\fonts\line-awesome\line-awesome.ttf" />
    <Content Include="src\theme\vendors\base\fonts\line-awesome\line-awesome.woff" />
    <Content Include="src\theme\vendors\base\fonts\line-awesome\line-awesome.woff2" />
    <Content Include="src\theme\vendors\base\fonts\metronic\Metronic_fda1334c35d0f5fe2afb3afebbb6774a.eot" />
    <Content Include="src\theme\vendors\base\fonts\metronic\Metronic_fda1334c35d0f5fe2afb3afebbb6774a.svg" />
    <Content Include="src\theme\vendors\base\fonts\metronic\Metronic_fda1334c35d0f5fe2afb3afebbb6774a.ttf" />
    <Content Include="src\theme\vendors\base\fonts\metronic\Metronic_fda1334c35d0f5fe2afb3afebbb6774a.woff" />
    <Content Include="src\theme\vendors\base\fonts\metronic\Metronic_fda1334c35d0f5fe2afb3afebbb6774a.woff2" />
    <Content Include="src\theme\vendors\base\fonts\avenir\AvenirLTStd-Medium.otf" />
    <Content Include="src\theme\vendors\base\vendors.bundle.css" />
    <Content Include="src\theme\vendors\base\vendors.bundle.js" />
    <Content Include="src\theme\vendors\custom\datatables\datatables.bundle.css" />
    <Content Include="src\theme\vendors\custom\fonts\fonts.custom.css" />
    <Content Include="src\theme\vendors\custom\datatables\datatables.custom.css" />
    <Content Include="src\theme\vendors\custom\datatables\datatables.bundle.js" />
    <Content Include="src\theme\vendors\custom\fullcalendar\fullcalendar.bundle.css" />
    <Content Include="src\theme\vendors\custom\fullcalendar\fullcalendar.bundle.js" />
    <Content Include="loaderio-bb9c2a7270c7b008587488673cda1b99.txt" />
    <Content Include="TimeActivityAdd.xml" />
    <Content Include="wide.png" />
    <Content Include="Content\Reveal\css\theme\README.md" />
    <Content Include="Content\Reveal\css\theme\source\beige.scss" />
    <Content Include="Content\Reveal\css\theme\source\blood.scss" />
    <Content Include="Content\Reveal\css\theme\source\default.scss" />
    <Content Include="Content\Reveal\css\theme\source\moon.scss" />
    <Content Include="Content\Reveal\css\theme\source\night.scss" />
    <Content Include="Content\Reveal\css\theme\source\serif.scss" />
    <Content Include="Content\Reveal\css\theme\source\simple.scss" />
    <Content Include="Content\Reveal\css\theme\source\sky.scss" />
    <Content Include="Content\Reveal\css\theme\source\solarized.scss" />
    <Content Include="Content\Reveal\css\theme\template\mixins.scss" />
    <Content Include="Content\Reveal\css\theme\template\settings.scss" />
    <Content Include="Content\Reveal\css\theme\template\theme.scss" />
    <Content Include="Content\Reveal\lib\font\league_gothic-webfont.eot" />
    <Content Include="Content\Reveal\lib\font\league_gothic-webfont.ttf" />
    <Content Include="Content\Reveal\lib\font\league_gothic-webfont.woff" />
    <Content Include="Content\Reveal\lib\font\league_gothic_license" />
    <Content Include="Content\Reveal\plugin\markdown\example.md" />
    <Content Include="src\scripts\pages\ptoaccrual.page.js" />
    <Content Include="App_Data\buddypunch-saml-cert.pfx" />
    <Content Include="App_Data\buddypunch-saml-cert-converted.pfx" />
    <Content Include="Content\EmployeeUpload\EmployeeList.csv" />
    <Content Include="Content\GeofenceUpload\geofencedata.json" />
    <Content Include="Content\plans.json" />
    <Content Include="ApplicationInsights.config" />
    <None Include="Deploy\SetAzureWebsite.ps1">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="Deploy\SwapSlots.ps1">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <Content Include="package.json" />
    <None Include="Properties\PublishProfiles\VSTS.pubxml" />
    <None Include="Scripts\jquery-1.8.2.intellisense.js" />
    <Content Include="robots.txt" />
    <Content Include="Scripts\reliableGeoLocator.js" />
    <Content Include="Scripts\jquery-1.8.2.js" />
    <Content Include="Scripts\jquery-1.8.2.min.js" />
    <None Include="Scripts\jquery.validate-vsdoc.js" />
    <Content Include="Scripts\jquery-ui-1.9.0.js" />
    <Content Include="Scripts\jquery-ui-1.9.0.min.js" />
    <Content Include="Scripts\jquery.validate.js" />
    <Content Include="Scripts\jquery.validate.min.js" />
    <Content Include="Scripts\modernizr-2.6.2.js" />
    <Content Include="Scripts\therapycrm.js" />
    <Content Include="Global.asax" />
    <Content Include="Scripts\webcamjs\webcam.min.js" />
    <Content Include="square.png" />
    <Content Include="tiny.png" />
    <Content Include="Web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Scripts\jquery.unobtrusive-ajax.js" />
    <Content Include="Scripts\jquery.unobtrusive-ajax.min.js" />
    <Content Include="Scripts\jquery.validate.unobtrusive.js" />
    <Content Include="Scripts\jquery.validate.unobtrusive.min.js" />
    <Content Include="Scripts\MicrosoftAjax.js" />
    <Content Include="Scripts\MicrosoftAjax.debug.js" />
    <Content Include="Scripts\MicrosoftMvcAjax.js" />
    <Content Include="Scripts\MicrosoftMvcAjax.debug.js" />
    <Content Include="Scripts\MicrosoftMvcValidation.js" />
    <Content Include="Scripts\MicrosoftMvcValidation.debug.js" />
    <Content Include="Views\_ViewStart.cshtml" />
    <Content Include="Views\Account\ChangePassword.cshtml" />
    <Content Include="Views\Account\SignUpWizard.cshtml" />
    <Content Include="Views\Account\ChangePasswordSuccess.cshtml" />
    <Content Include="Views\Account\LogOn.cshtml" />
    <Content Include="Views\Home\About.cshtml" />
    <Content Include="Views\Home\Index.cshtml" />
    <Content Include="Views\Shared\Error.cshtml" />
    <Content Include="Views\Shared\_LogOnPartial.cshtml" />
    <Content Include="Views\Shared\_Layout.cshtml" />
    <Content Include="Views\Web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Views\StripeWebhook\Index.cshtml" />
    <Content Include="Views\Configuration\Index.cshtml" />
    <Content Include="Views\Employee\EmployeeList.cshtml" />
    <Content Include="Views\Employee\Create.cshtml" />
    <Content Include="Views\Employee\Edit.cshtml" />
    <Content Include="Views\Employee\Index.cshtml" />
    <Content Include="Views\Employee\QuickSearchResults.cshtml" />
    <Content Include="Views\Employee\SearchResults.cshtml" />
    <Content Include="Views\Employee\View.cshtml" />
    <Content Include="Views\Shared\_LayoutSideBar.cshtml" />
    <Content Include="Views\Account\Register.cshtml" />
    <Content Include="Views\Shared\_PublicLayout.cshtml" />
    <Content Include="Views\Shared\_LoginPublicLayout.cshtml" />
    <Content Include="Views\Shared\_RegistrationLayout.cshtml" />
    <Content Include="Views\Shared\_SignUpLayout.cshtml" />
    <Content Include="Views\Shared\_LayoutTopNav.cshtml" />
    <Content Include="Views\Employee\Punch.cshtml" />
    <Content Include="Views\Shared\_LayoutFooter.cshtml" />
    <Content Include="Views\PayPeriod\Index.cshtml" />
    <Content Include="Views\Employee\CurrentActivity.cshtml" />
    <Content Include="Views\PayPeriod\Edit.cshtml" />
    <Content Include="Views\PayPeriod\Create.cshtml" />
    <Content Include="Views\Report\PayPeriodSummary.cshtml" />
    <Content Include="Views\Employee\ApprovalList.cshtml" />
    <Content Include="Views\Employee\EditPunch.cshtml" />
    <Content Include="Views\Account\ResetPasswordSuccess.cshtml" />
    <Content Include="Views\Account\ResetPasswordFailure.cshtml" />
    <Content Include="Views\Report\PayPeriods.cshtml" />
    <Content Include="Views\Employee\AddPunch.cshtml" />
    <Content Include="Views\Report\TimeSheets.cshtml" />
    <Content Include="Views\Report\TimeCards.cshtml" />
    <Content Include="Views\Report\TimeCard.cshtml" />
    <Content Include="Views\Employee\AddBreak.cshtml" />
    <Content Include="Views\Account\KeepAlive.cshtml" />
    <Content Include="Views\Account\Edit.cshtml" />
    <Content Include="Views\Shared\_RegistrationWizardLayout.cshtml" />
    <Content Include="Views\Account\Confirmation.cshtml" />
    <Content Include="Views\Help\EmployeeGettingStarted.cshtml" />
    <Content Include="Views\Account\Introduction.cshtml" />
    <Content Include="Views\Account\SetupEmployees.cshtml" />
    <Content Include="Views\Account\Cancelled.cshtml" />
    <Content Include="Views\Employee\EditPermissions.cshtml" />
    <Content Include="Views\JobCode\Create.cshtml" />
    <Content Include="Views\JobCode\Edit.cshtml" />
    <Content Include="Views\JobCode\Index.cshtml" />
    <Content Include="Views\Employee\EditJobCodes.cshtml" />
    <Content Include="Views\Location\Create.cshtml" />
    <Content Include="Views\Location\Edit.cshtml" />
    <Content Include="Views\Location\Index.cshtml" />
    <Content Include="Views\Employee\EditLocations.cshtml" />
    <Content Include="Views\Shared\PublicLayoutLogo.cshtml" />
    <Content Include="Views\Account\OverTimeSettings.cshtml" />
    <Content Include="Views\Account\PayRatesSettings.cshtml" />
    <Content Include="Views\Help\GettingStartedFrame.cshtml" />
    <Content Include="Views\PayPeriod\KrkSchedule.cshtml" />
    <Content Include="Views\Employee\CreateABVending.cshtml" />
    <Content Include="Views\Employee\AddPTO.cshtml" />
    <Content Include="Views\Report\Punches.cshtml" />
    <Content Include="Views\Account\PTOSettings.cshtml" />
    <Content Include="Views\Employee\EditPTO.cshtml" />
    <Content Include="Views\Report\InOutActivity.cshtml" />
    <Content Include="Views\Report\PayrollExport.cshtml" />
    <Content Include="Views\Report\DailyHours.cshtml" />
    <Content Include="Views\Report\PTOSummary.cshtml" />
    <Content Include="Views\Report\PTODetail.cshtml" />
    <Content Include="Views\Employee\ViewQrCode.cshtml" />
    <Content Include="Views\Home\QrCodeScanner.cshtml" />
    <Content Include="Views\Employee\AutoPunch.cshtml" />
    <Content Include="Views\Employee\AutoPunchConfirmation.cshtml" />
    <Content Include="Views\Account\QrCodeSettings.cshtml" />
    <Content Include="Views\Employee\ViewQrCodes.cshtml" />
    <Content Include="Views\BreakRule\Create.cshtml" />
    <Content Include="Views\BreakRule\Edit.cshtml" />
    <Content Include="Views\BreakRule\Index.cshtml" />
    <Content Include="Views\Employee\EditBreak.cshtml" />
    <Content Include="Views\Employee\PayAmountHistory.cshtml" />
    <Content Include="Views\Report\SummaryHours.cshtml" />
    <Content Include="Views\Report\InOutDetailReportPdf.cshtml" />
    <Content Include="Views\Report\InOutDetailReport.cshtml" />
    <Content Include="Views\Employee\DeleteShift.cshtml" />
    <Content Include="Views\Configuration\SwitchAccount.cshtml" />
    <Content Include="Views\IpAddressLock\Create.cshtml" />
    <Content Include="Views\IpAddressLock\Edit.cshtml" />
    <Content Include="Views\IpAddressLock\Index.cshtml" />
    <Content Include="Views\IpAddressLock\Delete.cshtml" />
    <Content Include="Views\CleanupOnDisconnect\Index.cshtml" />
    <Content Include="Views\DirectConnectToIntuit\Index.cshtml" />
    <Content Include="Views\Disconnect\Index.cshtml" />
    <Content Include="Views\MenuProxy\Index.cshtml" />
    <Content Include="Views\OauthResponse\Index.cshtml" />
    <Content Include="Views\OpenId\Index.cshtml" />
    <Content Include="Views\QuickBooksEmployees\Index.cshtml" />
    <Content Include="Views\QuickbooksOnline\SyncEmployees.cshtml" />
    <Content Include="Views\QuickbooksTime\OnlineExport.cshtml" />
    <Content Include="Views\Employee\DeleteEmployee.cshtml" />
    <Content Include="Views\QuickbooksOnline\Index.cshtml" />
    <Content Include="Views\QuickbooksOnline\ExportTime.cshtml" />
    <Content Include="Views\QuickbooksOnline\ExportHistory.cshtml" />
    <Content Include="Views\QuickbooksOnline\ViewTransaction.cshtml" />
    <Content Include="Views\Account\LogOn2.cshtml" />
    <Content Include="Views\PunchRounding\Index.cshtml" />
    <Content Include="Views\Account\GpsSettings.cshtml" />
    <Content Include="Views\Employee\PunchMap.cshtml" />
    <Content Include="Views\Account\AdminList.cshtml" />
    <Content Include="Views\Error\GeoLocationRequired.cshtml" />
    <Content Include="Views\Report\TimeCardMap.cshtml" />
    <Content Include="Views\Employee\ChangeUsername.cshtml" />
    <Content Include="Views\Employee\CreateVeeva.cshtml" />
    <Content Include="Views\Terminal\Index.cshtml" />
    <Content Include="Views\EmployeePin\Index.cshtml" />
    <Content Include="Views\EmployeePin\ChangePin.cshtml" />
    <Content Include="Views\Account\TerminalSettings.cshtml" />
    <Content Include="Views\Account\_ExternalLoginsListPartial.cshtml" />
    <Content Include="Views\Employee\CreateRedIron.cshtml" />
    <Content Include="Views\Shared\_LayoutHeader.cshtml" />
    <Content Include="Views\Employee\ChangePassword.cshtml" />
    <Content Include="Views\Integration\Index.cshtml" />
    <Content Include="Views\Quickbooks\ExportHistory.cshtml" />
    <Content Include="Views\Quickbooks\Index.cshtml" />
    <Content Include="Views\Quickbooks\ViewTransaction.cshtml" />
    <Content Include="Views\Quickbooks\MapEmployees.cshtml" />
    <Content Include="Views\Report\TimeCardReport.cshtml" />
    <Content Include="Views\Quickbooks\MapPayrollItems.cshtml" />
    <Content Include="Views\Quickbooks\ViewAvailableTime.cshtml" />
    <Content Include="Views\Quickbooks\ViewUnavailableTime.cshtml" />
    <Content Include="Views\Quickbooks\Edit.cshtml" />
    <Content Include="Views\Account\PaychexSettings.cshtml" />
    <Content Include="Views\Account\WebcamSettings.cshtml" />
    <Content Include="Views\Error\WebcamRequired.cshtml" />
    <Content Include="Views\Account\Cancel.cshtml" />
    <Content Include="Views\Account\TimesheetApprovalSettings.cshtml" />
    <Content Include="Views\TimeCard\Review.cshtml" />
    <Content Include="Views\TimeCard\PendingApproval.cshtml" />
    <Content Include="Views\PayPeriod\ScheduleEdit.cshtml" />
    <Content Include="Views\AccountPTOEarningCode\Create.cshtml" />
    <Content Include="Views\AccountPTOEarningCode\Delete.cshtml" />
    <Content Include="Views\AccountPTOEarningCode\Edit.cshtml" />
    <Content Include="Views\Account\MapSurePayrollEarningCodes.cshtml" />
    <Content Include="Views\Account\EmployeePTOBankSetup.cshtml" />
    <Content Include="Views\PTOBank\PTOBankTransaction.cshtml" />
    <Content Include="Views\PTOBank\PTOAccrualRules.cshtml" />
    <Content Include="Views\PTOBank\PTOAccrualRule.cshtml" />
    <Content Include="Views\PTOBank\EmployeePTOBanks.cshtml" />
    <Content Include="Views\PTOBank\EmployeePTOBankHistory.cshtml" />
    <Content Include="Views\Report\PTOTypeBankForEmployee.cshtml" />
    <Content Include="Views\Employee\EmployeePTOSummary.cshtml" />
    <Content Include="Views\PayPeriod\_ScheduleEditOccurencesPreivew.cshtml" />
    <Content Include="Views\Employee\CreateBulkStandard.cshtml" />
    <Content Include="Views\Report\Veeva.cshtml" />
    <Content Include="Views\Account\MapPayPlusEarningCodes.cshtml" />
    <Content Include="Views\Account\AdpSettings.cshtml" />
    <Content Include="Views\Diagnostic\CallService.cshtml" />
    <Content Include="Views\Face\Index.cshtml" />
    <Content Include="Views\Face\Login.cshtml" />
    <Content Include="Views\Face\ProfilePicture.cshtml" />
    <Content Include="Views\Face\Faces.cshtml" />
    <Content Include="Views\Report\DeleteReport.cshtml" />
    <Content Include="Views\Account\AdpRunSettings.cshtml" />
    <Content Include="Views\OvertimeAlertRule\Create.cshtml" />
    <Content Include="Views\OvertimeAlertRule\Delete.cshtml" />
    <Content Include="Views\OvertimeAlertRule\Edit.cshtml" />
    <Content Include="Views\OvertimeAlertRule\Index.cshtml" />
    <Content Include="Views\Account\Settings.cshtml" />
    <Content Include="Views\Shared\_ErrorSuccessPartial.cshtml" />
    <Content Include="Views\Account\PaylocitySettings.cshtml" />
    <Content Include="Views\Account\TimeEntryOptions.cshtml" />
    <Content Include="Views\Employee\EditTimeEntries.cshtml" />
    <Content Include="Views\Employee\CopyTimeEntries.cshtml" />
    <Content Include="Views\ApiManagement\Delegate.cshtml" />
    <Content Include="Views\Employee\ResendWelcomeEmail.cshtml" />
    <Content Include="Views\Shared\_GeneralSearch.cshtml" />
    <Content Include="Views\Employee\ExceededEmployeeLimit.cshtml" />
    <Content Include="Views\Employee\_PunchTimeCard.cshtml" />
    <Content Include="Views\Employee\_PunchOnTime.cshtml" />
    <Content Include="Views\Employee\_PunchOutLate.cshtml" />
    <Content Include="Views\Account\_SetupProgress.cshtml" />
    <Content Include="Views\Shared\Modals\_ConfirmDelete.cshtml" />
    <Content Include="Views\Shared\Modals\_ConfirmApprove.cshtml" />
    <Content Include="Views\Account\MapWorkdayCodes.cshtml" />
    <Content Include="src\scss\fonts.scss" />
    <Content Include="src\scss\light-table.scss" />
    <Content Include="Views\Account\MapGustoColumns.cshtml" />
    <Content Include="Views\Home\QrCodeScanner2.cshtml" />
    <Content Include="Views\Shared\_EmptyLayout.cshtml" />
    <Content Include="Views\Configuration\ReActivateAccount.cshtml" />
    <Content Include="Views\Configuration\CreateEmployeeTimesheet.cshtml" />
    <Content Include="Views\PTO\PTOCalendar.cshtml" />
    <Content Include="src\scss\ptocalendar.scss" />
    <Content Include="src\scripts\services\ptocalendar.service.js" />
    <Content Include="Views\PTO\EditPtoRequest.cshtml" />
    <Content Include="Views\Shared\_ValidationSummary.cshtml" />
    <Content Include="Views\Shared\_ErrorMessage.cshtml" />
    <Content Include="Views\Geofence\CreateOrEdit.cshtml" />
    <Content Include="Views\Geofence\Delete.cshtml" />
    <Content Include="Views\Geofence\Index.cshtml" />
    <Content Include="src\scripts\pages\geofence.form.js" />
    <Content Include="Views\Error\GeofenceEnabled.cshtml" />
    <Content Include="Views\Error\GeoLocationChromeNotSupported.cshtml" />
    <Content Include="Views\Error\GeoLocationEdgeNotSupported.cshtml" />
    <Content Include="Views\Employee\_PunchInButton.cshtml" />
    <Content Include="Views\Error\GeoLocationIENotSupported.cshtml" />
    <Content Include="Views\Position\Create.cshtml" />
    <Content Include="Views\Position\Edit.cshtml" />
    <Content Include="Views\Position\Index.cshtml" />
    <Content Include="Views\Shared\DisplayTemplates\Bootstrap4Pagination.cshtml" />
    <Content Include="Views\Schedule\Index.cshtml" />
    <Content Include="Views\Shift\Create.cshtml" />
    <Content Include="Views\Shift\Edit.cshtml" />
    <Content Include="Views\Schedule\Modals\_CreateShift.cshtml" />
    <Content Include="Views\Master\QuickSearch.cshtml" />
    <Content Include="Views\Employee\AutoPunchError.cshtml" />
    <Content Include="Views\Employee\_PunchInForm.cshtml" />
    <Content Include="Views\Configuration\AccountEdit.cshtml" />
    <Content Include="Views\Schedule\Modals\_EditShift.cshtml" />
    <Content Include="src\scss\schedule.scss" />
    <Content Include="Views\Schedule\Settings.cshtml" />
    <Content Include="Views\PunchLimitRule\Create.cshtml" />
    <Content Include="Views\PunchLimitRule\Delete.cshtml" />
    <Content Include="Views\PunchLimitRule\Edit.cshtml" />
    <Content Include="Views\PunchLimitRule\Index.cshtml" />
    <Content Include="Views\Schedule\Modals\_CreateShift2.cshtml" />
    <Content Include="Views\Schedule\Modals\_EditShift2.cshtml" />
    <Content Include="Views\Employee\_SwitchForm.cshtml" />
    <Content Include="Views\Schedule\Modals\_PublishSchedule.cshtml" />
    <Content Include="Views\Account\ChangePlan.cshtml" />
    <Content Include="Views\Report\_TimeCardTable.cshtml" />
    <Content Include="Views\Report\_ScheduleTimeCardTable.cshtml" />
    <Content Include="Views\TimeCard\Modals\_EditShiftForPunch.cshtml" />
    <Content Include="Views\Employee\EditPositions.cshtml" />
    <Content Include="Views\Employee\AddPunchWithShift.cshtml" />
    <Content Include="Views\EmployeeUpload\Index.cshtml" />
    <Content Include="Views\EmployeeUpload\SendWelcomeEmail.cshtml" />
    <Content Include="Views\DeviceLock\Create.cshtml" />
    <Content Include="Views\DeviceLock\Delete.cshtml" />
    <Content Include="Views\DeviceLock\Edit.cshtml" />
    <Content Include="Views\DeviceLock\Index.cshtml" />
    <Content Include="Views\Notification\SendMessage.cshtml" />
    <Content Include="Views\Notification\Index.cshtml" />
    <Content Include="Views\Shared\Modals\_SendMessageModal.cshtml" />
    <Content Include="Views\MessageLog\Index.cshtml" />
    <Content Include="Views\MessageLog\ViewMessage.cshtml" />
    <Content Include="Views\Employee\_Partials\_PTOAccrualRules.cshtml" />
    <Content Include="Views\Employee\_Partials\_DeviceLocks.cshtml" />
    <Content Include="Views\Employee\_Partials\_Geofences.cshtml" />
    <Content Include="Views\Employee\_Partials\_IpAddressLocks.cshtml" />
    <Content Include="Views\Employee\_Partials\_AlertRules.cshtml" />
    <Content Include="Views\Employee\_Partials\_PayRates.cshtml" />
    <Content Include="Views\Employee\PayRateAdd.cshtml" />
    <Content Include="Views\Employee\PayRateEdit.cshtml" />
    <Content Include="Views\Employee\PayRateHistory.cshtml" />
    <Content Include="Views\Employee\EditGeofences.cshtml" />
    <Content Include="Views\Employee\EditDeviceLocks.cshtml" />
    <Content Include="Views\Employee\EditIpAddressLocks.cshtml" />
    <Content Include="Views\Employee\EditAlertRules.cshtml" />
    <Content Include="Views\AlertRule\Create.cshtml" />
    <Content Include="Views\AlertRule\Delete.cshtml" />
    <Content Include="Views\AlertRule\Edit.cshtml" />
    <Content Include="Views\AlertRule\Index.cshtml" />
    <Content Include="Views\IntuitLogin\Callback.cshtml" />
    <Content Include="Views\IntuitLogin\Token.cshtml" />
    <Content Include="Views\Profile\Edit.cshtml" />
    <Content Include="Views\Schedule\Modals\_ApplyTemplate.cshtml" />
    <Content Include="Views\Schedule\Modals\_ManageTemplates.cshtml" />
    <Content Include="Views\Account\ChangeUsername.cshtml" />
    <Content Include="Views\StripeWebhook\GetPendingInvoiceItems.cshtml" />
    <Content Include="Views\Report\By.cshtml" />
    <Content Include="Views\Account\ForgotUsernameSuccess.cshtml" />
    <Content Include="Views\Account\ForgotUsernameFailure.cshtml" />
    <Content Include="Views\Account\ResetPassword.cshtml" />
    <Content Include="Views\Account\ForgotPasswordFailure.cshtml" />
    <Content Include="Views\Account\ForgotPasswordSuccess.cshtml" />
    <Content Include="Views\Account\KioskModeSettings.cshtml" />
    <Content Include="Views\Account\KioskLogOn.cshtml" />
    <Content Include="Views\Employee\EditManagerPermissions.cshtml" />
    <Content Include="Views\Quickbooks\MapObjects.cshtml" />
    <Content Include="Views\Employee\KioskPunch.cshtml" />
    <Content Include="Views\Employee\KioskPunchConfirmation.cshtml" />
    <Content Include="Views\Employee\KioskPunchError.cshtml" />
    <Content Include="Views\Employee\GroupPunch.cshtml" />
    <Content Include="Views\Account\EmployeeEditSettings.cshtml" />
    <Content Include="Views\Schedule\AvailabilityPermissions.cshtml" />
    <Content Include="Views\Schedule\Availability.cshtml" />
    <Content Include="Views\Schedule\Modals\_CreateAvailability.cshtml" />
    <Content Include="Views\Configuration\ManageSamlSso.cshtml" />
    <Content Include="Views\Configuration\CreateSamlSso.cshtml" />
    <Content Include="Views\Schedule\Modals\_EditAvailability.cshtml" />
    <Content Include="Views\Quickbooks\LogMessages.cshtml" />
    <Content Include="Views\Schedule\Log.cshtml" />
    <Content Include="Views\Account\AuditLog.cshtml" />
    <Content Include="Views\Account\ChangeBillingEmail.cshtml" />
    <Content Include="Views\StripeWebhook\GetCharges.cshtml" />
    <Content Include="Views\Account\PaymentConfig.cshtml" />
    <Content Include="Views\Account\PurchasePlan.cshtml" />
    <Content Include="Views\Configuration\ManageStripeSubscription.cshtml" />
    <Content Include="Views\Configuration\CustomPricing.cshtml" />
    <Content Include="Views\Account\SetupPayPeriods.cshtml" />
    <Content Include="Views\Account\CustomizeSetup.cshtml" />
    <Content Include="Views\Account\SelectFeatures.cshtml" />
    <Content Include="Views\Account\GettingStarted.cshtml" />
    <Content Include="Views\Account\_PayPeriodsListPartial.cshtml" />
    <Content Include="Views\Zapier\Index.cshtml" />
    <Content Include="Views\Report\EarlyLate.cshtml" />
    <Content Include="Views\Report\Absence.cshtml" />
    <Content Include="Views\Report\EmployeeErrorLog.cshtml" />
    <Content Include="Views\Account\PaycorSettings.cshtml" />
    <Content Include="Views\Configuration\ManageQuickBooksPayrollItems.cshtml" />
    <Content Include="Views\Account\Modals\_StripeElementsModal.cshtml" />
    <Content Include="Views\Employee\_BreakForm.cshtml" />
    <Content Include="Views\Employee\_EndBreakForm.cshtml" />
    <Content Include="Views\Shift\List.cshtml" />
    <Content Include="Views\ShiftTrade\Index.cshtml" />
    <Content Include="Views\ShiftTrade\View.cshtml" />
    <Content Include="Views\Schedule\TradeCoverPermissions.cshtml" />
    <Content Include="Views\Schedule\Modals\_TradeCoverRequestModal.cshtml" />
    <Content Include="Views\Employee\EditManualBreak.cshtml" />
    <Content Include="Views\Configuration\TrialingAccounts.cshtml" />
    <Content Include="Views\Employee\AddManualBreak.cshtml" />
    <Content Include="Views\Quickbooks\MapPayrollItemsList.cshtml" />
    <Content Include="Views\Configuration\DeleteEmployeeTimesheet.cshtml" />
    <Content Include="Views\Configuration\ManageFacialRecognition.cshtml" />
    <Content Include="Views\Employee\_Partials\_BreakRules.cshtml" />
    <Content Include="Views\Employee\EditManualBreakRules.cshtml" />
    <Content Include="Views\Employee\BreakMap.cshtml" />
    <Content Include="Views\Employee\_KioskPunchForm.cshtml" />
    <Content Include="Views\PunchRounding\_Form.cshtml" />
    <Content Include="Views\PunchRounding\_EditPunchRoundingRuleData.cshtml" />
    <Content Include="Views\PunchRounding\Create.cshtml" />
    <Content Include="Views\PunchRounding\Edit.cshtml" />
    <Content Include="Views\OvertimeType\Create.cshtml" />
    <Content Include="Views\OvertimeType\Edit.cshtml" />
    <Content Include="Views\OvertimeType\_Form.cshtml" />
    <Content Include="Views\OvertimeType\_DailyRule.cshtml" />
    <Content Include="Views\Account\ISolvedSettings.cshtml" />
    <Content Include="Views\Account\WagepointSettings.cshtml" />
    <Content Include="Views\Employee\ChangeOvertimeType.cshtml" />
    <Content Include="Views\Report\GPSActivity.cshtml" />
    <Content Include="Views\Account\JoinAccountRequestSent.cshtml" />
    <Content Include="Views\Account\JustworksSettings.cshtml" />
    <Content Include="Views\Schedule\Modals\_SendSchedule.cshtml" />
    <Content Include="Views\Account\PaymentEvolutionSettings.cshtml" />
    <Content Include="Views\PTOBank\PTOBlackoutDate.cshtml" />
    <Content Include="Views\Employee\_Partials\_PTOBlackoutDates.cshtml" />
    <Content Include="Views\PTOBank\PTOCompanyCalendar.cshtml" />
    <Content Include="Views\Account\RipplingSettings.cshtml" />
    <Content Include="Views\Payroll\PeopleList.cshtml" />
    <Content Include="Views\Payroll\Employee.cshtml" />
    <Content Include="Views\MyPayroll\PaystubsCheckComponent.cshtml" />
    <Content Include="Views\Payroll\RunPayroll.cshtml" />
    <Content Include="Views\Payroll\Company.cshtml" />
    <Content Include="Views\Payroll\PayrollSummary.cshtml" />
    <Content Include="Views\Payroll\PayrollJournal.cshtml" />
    <Content Include="Views\Payroll\FilingAuthorizationDocuments.cshtml" />
    <Content Include="Views\Payroll\CompanyTaxDocuments.cshtml" />
    <Content Include="Views\Payroll\CompanyOnboard.cshtml" />
    <Content Include="Views\Payroll\EditCompanyDetails.cshtml" />
    <Content Include="Views\Payroll\EditCompanyPayments.cshtml" />
    <Content Include="Views\Payroll\EditCompanyTaxSetup.cshtml" />
    <Content Include="Views\Payroll\EditCompanyFilingAuthorization.cshtml" />
    <Content Include="Views\Configuration\EmployeeSearch.cshtml" />
    <Content Include="Views\Payroll\Payrolls.cshtml" />
    <Content Include="Views\Payroll\PreparePayroll.cshtml" />
    <Content Include="Views\Payroll\AddPayroll.cshtml" />
    <Content Include="Views\Profile\SetupMFA.cshtml" />
    <Content Include="Views\Profile\VerifyMFA.cshtml" />
    <Content Include="Views\Payroll\CreateEmployee.cshtml" />
    <Content Include="Views\Payroll\EditEmployee.cshtml" />
    <Content Include="Views\Payroll\SignerInfo.cshtml" />
    <Content Include="Views\Account\SendCode.cshtml" />
    <Content Include="Views\Account\VerifyCode.cshtml" />
    <Content Include="Views\MyPayroll\Paystubs.cshtml" />
    <Content Include="Views\MyPayroll\OnboardCheckComponent.cshtml" />
    <Content Include="Views\Profile\TaxDocumentDeliveryOption.cshtml" />
    <Content Include="Views\MyPayroll\OnboardResult.cshtml" />
    <Content Include="Views\Configuration\PayrollSettings.cshtml" />
    <Content Include="Views\Payroll\StartDateEdit.cshtml" />
    <Content Include="Views\Payroll\_CreateOffCyclePayroll.cshtml" />
    <Content Include="Views\Payroll\_PostTaxDeductions.cshtml" />
    <Content Include="Views\Payroll\AddPostTaxDeduction.cshtml" />
    <Content Include="Views\Payroll\EditPostTaxDeduction.cshtml" />
    <Content Include="Views\Payroll\_Workplaces.cshtml" />
    <Content Include="Views\Payroll\AddWorkplace.cshtml" />
    <Content Include="Views\Payroll\EditWorkplace.cshtml" />
    <Content Include="Views\Payroll\_PayrollsTable.cshtml" />
    <Content Include="Views\Payroll\AddPayrollSignUpComplete.cshtml" />
    <Content Include="Views\Home\PayrollFeeSchedule.cshtml" />
    <Content Include="Views\Payroll\_CompanyBenefits.cshtml" />
    <Content Include="Views\Payroll\AddCompanyBenefit.cshtml" />
    <Content Include="Views\Payroll\EditCompanyBenefit.cshtml" />
    <Content Include="Views\Payroll\_EmployeeBenefits.cshtml" />
    <Content Include="Views\Payroll\AddEmployeeBenefit.cshtml" />
    <Content Include="Views\Payroll\EditEmployeeBenefit.cshtml" />
    <Content Include="Views\Payroll\CreateContractor.cshtml" />
    <Content Include="Views\Payroll\EditContractor.cshtml" />
    <Content Include="Views\Payroll\Contractor.cshtml" />
    <Content Include="Views\Payroll\Intro.cshtml" />
    <Content Include="Views\Account\SelectConfiguratorPhoneNumber.cshtml" />
    <Content Include="Views\Account\MapDeluxePayrollEarningCodes.cshtml" />
    <Content Include="Views\PayrollEmployeeUpload\Index.cshtml" />
    <Content Include="Views\Employee\PunchActivityMap.cshtml" />
    <Content Include="Views\Payroll\ContractorPayments.cshtml" />
    <Content Include="Views\Payroll\W2Preview.cshtml" />
    <Content Include="Views\Payroll\W4ExemptStatus.cshtml" />
    <Content Include="Views\Employee\_Partials\_PunchActivityMap.cshtml" />
    <Content Include="Views\Employee\_Partials\_PunchMap.cshtml" />
    <Content Include="Views\Payroll\RunPayrollLocal.cshtml" />
    <Content Include="Views\Account\MapTriNetPayrollEarningCodes.cshtml" />
    <Content Include="Views\Configuration\PayrollsReport.cshtml" />
    <Content Include="Views\Configuration\AdditionalFeatures.cshtml" />
    <Content Include="Views\QuickbooksOnline\ViewTransactionActivity.cshtml" />
    <Content Include="Views\Payroll\TaxDeposits.cshtml" />
    <Content Include="Views\Configuration\BillingHistory.cshtml" />
    <Content Include="Views\Configuration\TerminatedPayrollCompaniesReport.cshtml" />
    <Content Include="Views\Account\MapViewPointEarningCodes.cshtml" />
    <Content Include="Views\Shared\_ConsentModalPartial.cshtml" />
    <Content Include="Views\Configuration\StripePortal.cshtml" />
    <Content Include="Views\Account\MapDeltekEarningCodes.cshtml" />
    <Content Include="Views\Account\Signup.cshtml" />
    <Content Include="Views\Configuration\AddonFeatures.cshtml" />
    <Content Include="Views\Account\KioskIntro.cshtml" />
    <Content Include="Views\Payroll\PayrollCashRequirementReport.cshtml" />
    <Content Include="Views\Report\CustomReports.cshtml" />
    <Content Include="Views\Report\Modals\_AddPunchModal.cshtml" />
    <Content Include="Views\Report\Modals\_AddPunchWithShiftModal.cshtml" />
    <Content Include="src\scss\modals.scss" />
    <Content Include="Views\Report\Modals\_AddManualBreakModal.cshtml" />
    <Content Include="Views\Report\Modals\_EditManualBreakModal.cshtml" />
    <Content Include="Views\Report\Modals\_EditPunchModal.cshtml" />
    <Content Include="Views\Report\Modals\_DrawerContainer.cshtml" />
    <Content Include="Views\Error\_DataRetentionValidationError.cshtml" />
    <Content Include="Views\Group\Index.cshtml" />
    <Content Include="Views\Group\Create.cshtml" />
    <Content Include="Views\Group\Edit.cshtml" />
    <Content Include="Views\Report\CustomReportsIntro.cshtml" />
    <Content Include="Views\Account\HeartlandSettings.cshtml" />
    <Content Include="Views\Payroll\EditCompany401kIntegration.cshtml" />
    <Content Include="Views\Payroll\EditCompanySimplyInsuredIntegration.cshtml" />
    <Content Include="Views\Payroll\EditCompanyNextInsuranceIntegration.cshtml" />
    <Content Include="Views\Shared\metronic\Error.cshtml" />
    <Content Include="Views\Shared\metronic\PublicLayoutLogo.cshtml" />
    <Content Include="Views\Shared\metronic\_ConsentModalPartial.cshtml" />
    <Content Include="Views\Shared\metronic\_EmptyLayout.cshtml" />
    <Content Include="Views\Shared\metronic\_ErrorMessage.cshtml" />
    <Content Include="Views\Shared\metronic\_ErrorSuccessPartial.cshtml" />
    <Content Include="Views\Shared\metronic\_GeneralSearch.cshtml" />
    <Content Include="Views\Shared\metronic\_Layout.cshtml" />
    <Content Include="Views\Shared\metronic\_LayoutFooter.cshtml" />
    <Content Include="Views\Shared\metronic\_LayoutHeader.cshtml" />
    <Content Include="Views\Shared\metronic\_LayoutSideBar.cshtml" />
    <Content Include="Views\Shared\metronic\_LayoutTopNav.cshtml" />
    <Content Include="Views\Shared\metronic\_LoginPublicLayout.cshtml" />
    <Content Include="Views\Shared\metronic\_LogOnPartial.cshtml" />
    <Content Include="Views\Shared\metronic\_PublicLayout.cshtml" />
    <Content Include="Views\Shared\metronic\_RegistrationLayout.cshtml" />
    <Content Include="Views\Shared\metronic\_RegistrationWizardLayout.cshtml" />
    <Content Include="Views\Shared\metronic\_SignUpLayout.cshtml" />
    <Content Include="Views\Shared\metronic\_ValidationSummary.cshtml" />
    <None Include="Web.Demo.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
    <None Include="Web.QA.config">
      <DependentUpon>Web.config</DependentUpon>
      <SubType>Designer</SubType>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Content\themes\" />
    <Folder Include="ViewModel\Mapping\" />
    <Folder Include="Views\DataSetup\" />
    <Folder Include="Views\Export\" />
    <Folder Include="Views\iCal\" />
    <Folder Include="Views\Stripe\" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="packages.config">
      <SubType>Designer</SubType>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\BuddyPunch.Api.Models.NetStandard\BuddyPunch.Api.Models.NetStandard.csproj">
      <Project>{6a859e6e-71ab-488a-879b-2867a0565f97}</Project>
      <Name>BuddyPunch.Api.Models.NetStandard</Name>
    </ProjectReference>
    <ProjectReference Include="..\BuddyPunch.Check.NetStandard\BuddyPunch.Check.NetStandard.csproj">
      <Project>{b61548b5-854d-4edf-8580-13eb828cac30}</Project>
      <Name>BuddyPunch.Check.NetStandard</Name>
    </ProjectReference>
    <ProjectReference Include="..\BuddyPunch.Data.NetStandard\BuddyPunch.Data.NetStandard.csproj">
      <Project>{a9d51ce7-52f1-4a51-aa9d-276a29b5393c}</Project>
      <Name>BuddyPunch.Data.NetStandard</Name>
    </ProjectReference>
    <ProjectReference Include="..\BuddyPunch.Email.NetStandard\BuddyPunch.Email.NetStandard.csproj">
      <Project>{2b48e452-9497-43d6-8965-4910d72d6b44}</Project>
      <Name>BuddyPunch.Email.NetStandard</Name>
    </ProjectReference>
    <ProjectReference Include="..\BuddyPunch.Model.NetStandard\BuddyPunch.Model.NetStandard.csproj">
      <Project>{40e1e819-0d1a-44d5-af2f-d9234c333af8}</Project>
      <Name>BuddyPunch.Model.NetStandard</Name>
    </ProjectReference>
    <ProjectReference Include="..\BuddyPunch.Push.NetStandard\BuddyPunch.Push.NetStandard.csproj">
      <Project>{7c6cb37e-4651-4858-a6e3-da413e752cd5}</Project>
      <Name>BuddyPunch.Push.NetStandard</Name>
    </ProjectReference>
    <ProjectReference Include="..\BuddyPunch.Service.NetStandard\BuddyPunch.Service.NetStandard.csproj">
      <Project>{5f968e77-bd93-4051-a1b4-a7d99200bf0e}</Project>
      <Name>BuddyPunch.Service.NetStandard</Name>
    </ProjectReference>
    <ProjectReference Include="..\BuddyPunch.WebHooks.NetStandard\BuddyPunch.WebHooks.NetStandard.csproj">
      <Project>{c2ce8a5e-a650-479d-9e6c-8816b462ea18}</Project>
      <Name>BuddyPunch.WebHooks.NetStandard</Name>
    </ProjectReference>
    <ProjectReference Include="..\QbXml.NetStandard\QbXml.NetStandard.csproj">
      <Project>{5997a161-e1e3-4e0b-bf93-6079f991c3c8}</Project>
      <Name>QbXml.NetStandard</Name>
    </ProjectReference>
    <ProjectReference Include="..\WebConnector\WebConnector.csproj">
      <Project>{295c6b66-72d5-42de-9713-db8cfd3508bd}</Project>
      <Name>WebConnector</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\Shared\DisplayTemplates\AdminiumPagination.cshtml" />
    <Content Include="Views\Profile\Index.cshtml" />
    <Content Include="Views\Dropbox\Index.cshtml" />
    <Content Include="Views\Dropbox\UploadToDropbox.cshtml" />
    <Content Include="Views\Help\Index.cshtml" />
    <Content Include="Views\Help\GettingStarted.cshtml" />
    <Content Include="Views\Account\ViewPlan.cshtml" />
    <Content Include="Views\Account\Setup.cshtml" />
    <Content Include="Views\Error\Error.cshtml" />
    <Content Include="Views\Error\errorp.cshtml" />
    <Content Include="Views\Error\Expected.cshtml" />
    <Content Include="Views\Error\expectedp.cshtml" />
    <Content Include="Views\Error\HttpError404.cshtml" />
    <Content Include="Views\Error\HttpError505.cshtml" />
    <Content Include="Views\Dropbox\DownloadFromDropbox.cshtml" />
    <Content Include="Views\Diagnostic\SendTestEmail.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Service References\" />
  </ItemGroup>
  <ItemGroup>
    <Service Include="{4A0DDDB5-7A95-4FBF-97CC-616D07737A77}" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'QA|AnyCPU'">
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Demo|AnyCPU'">
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.targets" Condition="Exists('$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.targets')" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target> -->
  <PropertyGroup>
    <CopyAllFilesToSingleFolderForPackageDependsOn>
			CustomCollectFiles;
			$(CopyAllFilesToSingleFolderForPackageDependsOn);
		</CopyAllFilesToSingleFolderForPackageDependsOn>
    <CopyAllFilesToSingleFolderForMsdeployDependsOn>
			CustomCollectFiles;
			$(CopyAllFilesToSingleFolderForPackageDependsOn);
		</CopyAllFilesToSingleFolderForMsdeployDependsOn>
  </PropertyGroup>
  <Target Name="CustomCollectFiles">
    <ItemGroup>
      <_CustomFiles Include="dist\**\*" />
      <FilesForPackagingFromProject Include="%(_CustomFiles.Identity)">
        <DestinationRelativePath>dist\%(RecursiveDir)%(Filename)%(Extension)</DestinationRelativePath>
      </FilesForPackagingFromProject>
    </ItemGroup>
  </Target>
  <Target Name="MvcBuildViews" AfterTargets="AfterBuild" Condition="'$(MvcBuildViews)'=='true'">
    <AspNetCompiler VirtualPath="temp" PhysicalPath="$(WebProjectOutputDir)" />
  </Target>
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>49489</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>https://localhost:44300</IISUrl>
          <OverrideIISAppRootUrl>True</OverrideIISAppRootUrl>
          <IISAppRootUrl>https://localhost:44300/</IISAppRootUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Import Project="..\packages\Microsoft.Bcl.Build.1.0.14\tools\Microsoft.Bcl.Build.targets" Condition="Exists('..\packages\Microsoft.Bcl.Build.1.0.14\tools\Microsoft.Bcl.Build.targets')" />
  <Target Name="EnsureBclBuildImported" BeforeTargets="BeforeBuild" Condition="'$(BclBuildImported)' == ''">
    <Error Condition="!Exists('..\packages\Microsoft.Bcl.Build.1.0.14\tools\Microsoft.Bcl.Build.targets')" Text="This project references NuGet package(s) that are missing on this computer. Enable NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=317567." HelpKeyword="BCLBUILD2001" />
    <Error Condition="Exists('..\packages\Microsoft.Bcl.Build.1.0.14\tools\Microsoft.Bcl.Build.targets')" Text="The build restored NuGet packages. Build the project again to include these packages in the build. For more information, see http://go.microsoft.com/fwlink/?LinkID=317568." HelpKeyword="BCLBUILD2002" />
  </Target>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.Net.Compilers.3.3.1\build\Microsoft.Net.Compilers.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.Net.Compilers.3.3.1\build\Microsoft.Net.Compilers.props'))" />
    <Error Condition="!Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props'))" />
    <Error Condition="!Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.4.4\build\EntityFramework.props'))" />
    <Error Condition="!Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.4.4\build\EntityFramework.targets'))" />
    <Error Condition="!Exists('..\packages\Microsoft.ApplicationInsights.DependencyCollector.2.23.0\build\Microsoft.ApplicationInsights.DependencyCollector.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.ApplicationInsights.DependencyCollector.2.23.0\build\Microsoft.ApplicationInsights.DependencyCollector.targets'))" />
    <Error Condition="!Exists('..\packages\Microsoft.ApplicationInsights.PerfCounterCollector.2.23.0\build\Microsoft.ApplicationInsights.PerfCounterCollector.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.ApplicationInsights.PerfCounterCollector.2.23.0\build\Microsoft.ApplicationInsights.PerfCounterCollector.targets'))" />
    <Error Condition="!Exists('..\packages\Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.2.23.0\build\Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.2.23.0\build\Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.targets'))" />
    <Error Condition="!Exists('..\packages\Microsoft.ApplicationInsights.WindowsServer.2.23.0\build\Microsoft.ApplicationInsights.WindowsServer.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.ApplicationInsights.WindowsServer.2.23.0\build\Microsoft.ApplicationInsights.WindowsServer.targets'))" />
    <Error Condition="!Exists('..\packages\Microsoft.ApplicationInsights.Web.2.23.0\build\Microsoft.ApplicationInsights.Web.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.ApplicationInsights.Web.2.23.0\build\Microsoft.ApplicationInsights.Web.targets'))" />
  </Target>
  <Import Project="..\packages\EntityFramework.6.4.4\build\EntityFramework.targets" Condition="Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.targets')" />
  <Import Project="..\packages\Microsoft.ApplicationInsights.DependencyCollector.2.23.0\build\Microsoft.ApplicationInsights.DependencyCollector.targets" Condition="Exists('..\packages\Microsoft.ApplicationInsights.DependencyCollector.2.23.0\build\Microsoft.ApplicationInsights.DependencyCollector.targets')" />
  <Import Project="..\packages\Microsoft.ApplicationInsights.PerfCounterCollector.2.23.0\build\Microsoft.ApplicationInsights.PerfCounterCollector.targets" Condition="Exists('..\packages\Microsoft.ApplicationInsights.PerfCounterCollector.2.23.0\build\Microsoft.ApplicationInsights.PerfCounterCollector.targets')" />
  <Import Project="..\packages\Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.2.23.0\build\Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.targets" Condition="Exists('..\packages\Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.2.23.0\build\Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.targets')" />
  <Import Project="..\packages\Microsoft.ApplicationInsights.WindowsServer.2.23.0\build\Microsoft.ApplicationInsights.WindowsServer.targets" Condition="Exists('..\packages\Microsoft.ApplicationInsights.WindowsServer.2.23.0\build\Microsoft.ApplicationInsights.WindowsServer.targets')" />
  <Import Project="..\packages\Microsoft.ApplicationInsights.Web.2.23.0\build\Microsoft.ApplicationInsights.Web.targets" Condition="Exists('..\packages\Microsoft.ApplicationInsights.Web.2.23.0\build\Microsoft.ApplicationInsights.Web.targets')" />
</Project>