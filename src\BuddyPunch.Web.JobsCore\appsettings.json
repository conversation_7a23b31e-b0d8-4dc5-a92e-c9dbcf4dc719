{
  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "Microsoft.AspNetCore": "Debug"
    }
  },

  "ConnectionStrings": {
    "MembershipProviderConnectionString": "Data Source=localhost;Initial Catalog=buddypunch;Integrated Security=true;MultipleActiveResultSets=True",
    "BuddyPunchContextConnectionStringNew": "metadata=res://*/BuddyPunch.csdl|res://*/BuddyPunch.ssdl|res://*/BuddyPunch.msl;provider=System.Data.SqlClient;provider connection string=\"data source=localhost;Initial Catalog=buddypunch;Integrated Security=true;multipleactiveresultsets=True;App=EntityFramework\"",
    "WebhooksContextConnectionString": "Data Source=localhost;Initial Catalog=buddypunch;Integrated Security=true;MultipleActiveResultSets=True",
    "ApplicationInsightsConnectionString": "InstrumentationKey=c4d3c531-017e-4031-86ce-f451d4561650;IngestionEndpoint=https://northcentralus-0.in.applicationinsights.azure.com/;LiveEndpoint=https://northcentralus.livediagnostics.monitor.azure.com/;ApplicationId=7967ebb8-63ac-4c36-b9e7-aa58601aca29"
    //"BuddyPunchContextConnectionStringNew": "metadata=res://*/BuddyPunch.csdl|res://*/BuddyPunch.ssdl|res://*/BuddyPunch.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=localhost;Initial Catalog=BuddyPunch;User Id=BuddyPunch2;Password=********;multipleactiveresultsets=True;App=EntityFramework&quot;"
  },

  "RaygunSettings": {
    "ApiKey": "q/Jsl+HrpA5agHGA35aE2Q=="
  },
  "RaygunSettingsApiKey": "q/Jsl+HrpA5agHGA35aE2Q==",

  "AllowedHosts": "*",
  "Environment": "Dev",
  "SigningKey": "000102030405060708090a0b0c0d0e0f",
  "AzureStorageAccountName": "buddypunchappqa",
  "StorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=buddypunchappqa;AccountKey=****************************************************************************************",
  "EventGridTopicEndpoint": "https://buddypunch-qa-shift.northcentralus-1.eventgrid.azure.net/api/events",
  "EventGridTopicKey": "+y2NyCozC6z55kJC8rJ1HbR8CVcRbD4lX9TTjVbBxbA=",
  "OneSignalAppId": "************************************",
  "OneSignalRestApiKey": "************************************************",
  "OneSignalUrl": "https://onesignal.com/api/v1/notifications",
  "AuthServerBaseAddress": "https://localhost:44319/identity",
  "WebappBaseAddress": "https://localhost:44300/",
  "StripeApiKey": "sk_test_5oASvWoWzBTCQYTg3cSldcI7",
  "StripeApiPublishableKey": "pk_test_gJ4DU5RoVewiW7trjI1pYfOU",
  "OvertimeRulesHelper.AllocateHours.RetryAttempts": 3,
  "OvertimeRulesHelper.AllocateHours.PauseBetweenFailures": 2,
  "RecordUserActivityEnabled": true,
  "EnableQrCodeSettings": true,
  "EnableQboOpenId": true,
  "applicationToken": "1a0b4811b2b73b4dadb81ffb9e09a3db8e55",
  "consumerKey": "qyprdB1PP6ywlLEVRx4FZe9ZyulVvD",
  "consumerSecret": "1mwvMMxBc0ksZCAcDKqC9BMPrUsknp3wdUWaxYKS",

  "IntercomAppId": "y9a0fwwv",
  "IntercomAppKey": "************************************************************",

  "CheckApiBase": "https://sandbox.checkhq.com",
  "CheckApiKey": "36987257b08e6d29256ed6821393baf7bd0e5f01",
  "OwinLoggingEnabled": true,
  "HubSpotAccessKey": "--KeyVaultSecret--",

  "mailSettings": {
    "deliveryMethod": "SpecifiedPickupDirectory",
    "pickupDirectoryLocation": "C:\\temp"
  },
  "Churnkey": {
    "ApiBaseUrl": "https://api.churnkey.co/v1/api",
    "ApiKey": "test_t4B0TXBd8F2qAzmGURwsNNLfgUkbUKyY",
    "AppId": "bdux9ha4q"
  },

  "SwaggerEnabled": true,
  "InstrumentationKey": "d0168b18-ab6b-457f-9fce-6bfdb56c6def"
}
