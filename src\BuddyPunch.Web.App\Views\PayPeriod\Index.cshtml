﻿@using BuddyPunch.Model
@using BuddyPunch.Model.Extensions
@using BuddyPunch.Web.App.Helpers
@using RecurrencePeriod = BuddyPunch.Model.Enums.RecurrencePeriod
@model BuddyPunch.Web.App.ViewModel.PayPeriodsViewModel
@{
    ViewBag.Title = "Pay Periods";
}

@if (TempData["pay-period-info-message"] != null)
{
    <div class="alert alert-success alert-dismissible fade show">
        <button type="button" class="close" data-dismiss="alert" aria-hidden="true"></button>
        @if (TempData["pay-period-info-message"] != null)
        {
            <text>@TempData["pay-period-info-message"].ToString()</text>
        }
    </div>
}

<!-- BEGIN PAGE HEADER-->
<div class="m-subheader">
    <h3 class="m-subheader__title ">
        Pay Periods
    </h3>
</div>
<!-- END PAGE HEADER-->
<!-- BEGIN PAGE CONTENT-->

@using (Html.BeginMetronicEditPortlet("Settings", Url.Action("ScheduleEdit")))
{
    <dl>
        <dt>Pay Period Frequency</dt>
        <dd>@Model.PayPeriodSchedule.RecurrencePeriod.RecurrencePeriodName</dd>
    </dl>
    <div>
        <dl class="d-inline-block m--margin-right-50">
            <dt>1st Pay Period Start Date</dt>
            <dd>@Model.PayPeriodSchedule.RecurrenceStartDate.ToShortDateString()</dd>
            @if (Model.PayPeriodSchedule.RecurrencePeriodId == (int)RecurrencePeriod.SemiMonthly)
            {
                <dt>2nd Pay Period Start Date</dt>
                <dd>@Model.PayPeriodSchedule.SemiMonthlySecondDate.Value.ToShortDateString()</dd>
            }
        </dl>
        <dl class="d-inline-block">
            @if (Model.PayPeriodSchedule.FirstPayDate.HasValue)
            {
                <dt>1st Pay Date</dt>
                <dd>@Model.PayPeriodSchedule.FirstPayDate.Value.ToShortDateString()</dd>
                if (Model.PayPeriodSchedule.SecondPayDate.HasValue && Model.PayPeriodSchedule.RecurrencePeriodId == (int)RecurrencePeriod.SemiMonthly)
                {
                    <dt>2nd Pay Date</dt>
                    <dd>@Model.PayPeriodSchedule.SecondPayDate.Value.ToShortDateString()</dd>
                }
            }
        </dl>
    </div>
}

@if (Model.IsConfigurator)
{
    <div class="m--margin-bottom-20">
        @Html.ActionLink("Add New Pay Period", "Create", null, new { @class = "btn btn-primary" })
    </div>
}

@helper RenderTabHeader()
{
    <li class="nav-item m-tabs__item">
        <a href="#currentperiods" data-toggle="tab" class="nav-link m-tabs__link active" role="tab" aria-expanded="false">Current & Future</a>
    </li>
    <li class="nav-item m-tabs__item">
        <a href="#pastperiods" data-toggle="tab" class="nav-link m-tabs__link" role="tab" aria-expanded="false">Past</a>
    </li>
}

@helper RenderTable(IEnumerable<PayPeriod> payPeriods)
{
    <table class="table table-striped table-hover table-fixed-header">
        <thead>
            <tr>
                <th>Actions</th>
                <th>Start Date</th>
                <th>End Date</th>
                @if (Model.IsPayrollEnabled)
                {
                    <th>Pay Date</th>
                }
                <th>Locked for Employees</th>
                <th>Locked for Everyone</th>
                @if (Model.IsConfigurator)
                {
                    <th></th>
                }
            </tr>
        </thead>
        <tbody>
            @foreach (var item in payPeriods)
            {
            <tr>
                <td>
                    @Html.ActionLink("Edit", "Edit", new { id = item.Id }, new { @class = "btn btn-xs btn-sm btn-default" })
                </td>
                <td data-order="@(item.StartDate.ToSortableDateTimeString())">
                    @item.StartDate.ToShortDateString()
                </td>
                <td data-order="@(item.EndDate.ToSortableDateTimeString())">
                    @item.EndDate.ToShortDateString()
                </td>
                @if (Model.IsPayrollEnabled)
                {
                    <td data-order="@(item.PayDate.HasValue ? item.PayDate.Value.ToSortableDateTimeString() : "")">
                        @(item.PayDate?.ToShortDateString())
                    </td>
                }
                <td>
                    @(item.LockedForEmployees ? "Yes" : "No")
                </td>
                <td>
                    @(item.LockedForEveryone ? "Yes" : "No")
                </td>
                @if (Model.IsConfigurator)
                {
                    <td>
                        @Html.ActionLink("Calculate OT", "AllocateHours", new { id = item.Id }, new { @class = "btn btn-xs btn-sm btn-default" })
                    </td>
                }
            </tr>
            }
        </tbody>
    </table>
}

@using (Html.BeginMetronicTabPortlet(RenderTabHeader(), "Pay Periods"))
{
    <div class="tab-content">
        <div class="tab-pane active" id="currentperiods">
            @RenderTable(Model.PayPeriods.Where(p => p.EndDate >= Model.LocalNow.Date))
        </div>
        <div class="tab-pane" id="pastperiods">
            @RenderTable(Model.PayPeriods.Where(p => p.EndDate < Model.LocalNow.Date))
        </div>
    </div>
}

@section PageHeadSection
{

}

@section PageBeforeEndBodySection
{
    <script>
        jQuery(document).ready(function () {
            initDataTable('.table-striped');
        });

        // Javascript to enable link to tab
        var hash = location.hash.replace(/^#/, '');  // ^ means starting, meaning only match the first hash
        if (hash) {
            $('.nav-tabs a[href="#' + hash + '"]').tab('show');
        }

        // Change hash for page-reload
        $('.nav-tabs a').on('shown.bs.tab', function (e) {
            window.location.hash = e.target.hash;
        })
    </script>
}
