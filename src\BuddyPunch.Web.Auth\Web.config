﻿<?xml version="1.0" encoding="utf-8"?>
<!--
  For more information on how to configure your ASP.NET application, please visit
  http://go.microsoft.com/fwlink/?LinkId=301880
  -->
<configuration>
  <configSections>
    <section name="RaygunSettings" type="Mindscape.Raygun4Net.RaygunSettings, Mindscape.Raygun4Net" />
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
  </configSections>
  <RaygunSettings apikey="q/Jsl+HrpA5agHGA35aE2Q==" />
  <appSettings>
    <add key="Environment" value="Dev" />
    <add key="webpages:Version" value="*******" />
    <add key="webpages:Enabled" value="false" />
    <add key="ClientValidationEnabled" value="true" />
    <add key="UnobtrusiveJavaScriptEnabled" value="true" />
    <add key="owin:AppStartup" value="LocalStartup" />
    <add key="SigningCertificate" value="MIINPwIBAzCCDPsGCSqGSIb3DQEHAaCCDOwEggzoMIIM5DCCBg0GCSqGSIb3DQEH AaCCBf4EggX6MIIF9jCCBfIGCyqGSIb3DQEMCgECoIIE/jCCBPowHAYKKoZIhvcN AQwBAzAOBAjZC5ImPtgkwwICB9AEggTYwjXkTjQB9QMbCbjqnvGDG26H7Za+1Ozl uGbU1AfEhcQ7Eg97zA48XTofatX3FlQaUddR+EJvTs/hZny5uAoZfqYe7E6WIce6 j6sTE1se59BQVadZD2t1JNx9gF90RHk6e6baCVmsWrwUXee33cnI7YILybAcZ4Pm hciFgEIJtrEYG3YxyxTXlQu3LYV5ZuBLE4hCE3lLdq7XCGzuZfBiLQgVz6Yca2/U Nu7NKhtjnPQfTaV53UFB3FB8uPwVabfjJOIplHoRgnQBo+9PFEVcxikwAia7yxjf 4q/MzOBNqIc0j6Mcm6dPmOqDhBQapJ2gM1k9QBQ4TBbxQpe/YGfPXPbC5cBJlLGu HXA+qe/f7LbtpJmUYv0nUE6uOq6ehwBkAcMqu9ltjNe2+ib3xvxXSb3/m/6LL9v1 21RrL2B43lHPwAj2CAhbboR6NOwgajqxMUMX27OM2w5NObG+IaOLm42lKbzbMF59 QmJLgGUWVip8hmD55+ERjHFfed0DtaCZR7741og7Wt0VPBHqD/UKto4WOz3IqW2P RW4VRwrGfAJklCqFONGNI552KPeWh5e+c5Bjlnqgz4ZLnSv3C25kQGgGwcEkRk7J ZzZdASEpn6c75+q7qSD7fucAuCw87mo4+hiTtegnhvQO0sCf/P1R3Z163e0wb9BD PHaqzyNNJcaTXzKLSscMrQookDI2SAT+x1jxHJF2Zw4m3sqUca/Ul94+dsfhQ6UT lX+BOQ64O7+XaiRvuYst3hRGtbPqSEefYsGnmPx4Ho6JRUWDEHfq96DfDbRftknj GC/s5sKy2KHAfox8d1HANmJNjNmeq+90EBf0e/Lo3Ep2SnVmmtp6u/4jFoVt3vIy ESFjxJJ4O2UrOhEgr6QOCbYb7cAyc9YluLCTVpPQiGORkrw7YdcUeZKJNgYJRhhp lApWonzJAc13fLlgMZIrf6EUih/hU4t+6jBckdi94jLbgGZJsLE6bjZ40Fjdxo/W TiF0adtwRwF/X8QPFZdMjjU0xMLdBPwPxyhTnqfqSOYSz83a1gXP9hIXSGNI0J0y HCce2z7eJtOWC/PI7YJxHY9sHJ614OE4SvGnnSyjet4Pjc77B5LEbf9a3jFyLuFf ahGGtXqa8oKW+Pn49tLYAmJ16d+CbTCKyb6NNyVd7ddtX35/0KWvmwGxXmab05bi VNul9ucS7fY1iM8s4nPuI4oRJE3zC3ICWYR+dNqls+rCoYJp35QGipqpAqcd5SSs WKFt5VzVGR2l9wFkyqvI7SX9KFkWMJZODCfpl4jMkSmgvDQqtCBYZliw5VfBWfZi HCTNEkwB3670jpX2lPAamLclKQIg/fvlTcurkGjCYHIT6UaLv6P6NY0fRt0xmw9u qt9U92QHwJ61Jpmh6KxLFmDU3XDpsCwnTSuAV5O9ozn9sBqIzKqNup/RSvD2A0/6 +KknFotdw0/jh8q3c5G2cvXrS45D/1uaMDZdcD66wo0PT1f3EUsKiQXvNU+iwx6r q3qMXlPEUzbv7lGoVoWVIF0+n2Th6x6PphP5wQ7686mnIcU4OiA7rYuxw+tyGSbf MY1/UTAb1hxh+tZkImfsGk3AWFnmebFPntG43xUAu/PafK4fyHs9QLhRltcJQQBT Zne6FMeMXab2Lcd+jSD6nzGB4DANBgkrBgEEAYI3EQIxADATBgkqhkiG9w0BCRUx BgQEAQAAADBbBgkqhkiG9w0BCRQxTh5MAHsANQA4AEUAMgBBADgAOQBDAC0ANABB ADQAQgAtADQAMwA3ADIALQA5ADAAQQBDAC0ARAA2AEIAMwA0AEEAMQAyADkAQQBD AEUAfTBdBgkrBgEEAYI3EQExUB5OAE0AaQBjAHIAbwBzAG8AZgB0ACAAUwB0AHIA bwBuAGcAIABDAHIAeQBwAHQAbwBnAHIAYQBwAGgAaQBjACAAUAByAG8AdgBpAGQA ZQByMIIGzwYJKoZIhvcNAQcGoIIGwDCCBrwCAQAwgga1BgkqhkiG9w0BBwEwHAYK KoZIhvcNAQwBBjAOBAgzb9ATwdsRLwICB9CAggaIz7BmSfwpXDTOZ22aD2GwQlav mp/IcyQ378Y6NuzOthYEwpNcUL1mglp1Wrj6y9xC5VASq5J/EUy+SGrZPeD+0wbp d2dbQfu123dqc7OxtWp694QWjCAVRyFayZulqiSeK0W98hNH6jNsHTYs7k0iZWoP DFqONY9PEn1pZv0DwjoPWO8SpBtvv69VEao4P1XL0X+noxyvwbE3g54f6TDVP1LL zqeBiw4MWK6zKTzNnAFwSsGrYwO+Qnc2ITp0HF/AZBxuRpqeXTjV9Qu3sOXk2NS6 svMiIjAlBBxkg7Vleyv1OH4bRBfM6Qn6oFSzj602TvUHORkg3bsyAfUpUMW9fD9M pifYj7SDv2dbYv6djUifP8FVO8xQ9OhVo+COgc0Ip8KGuzesgJAs6Dq/hqR0qWQY YgTsJDyCs4XDIdJ6IrtCpusekVI0yQt79Mnp23vZ9r2AAs92fTdGMBmzqp3yJRfq yXLn9ABP1HpRtcvn/sHBmloELTEgxpnoDScR2okwJ7U1IZjP9a8QFkTRt1DnDLsI GmLHFa0bvnYvRcTFQEGkF57zXHN3oZjD4dJcmYNsQaNHYmGrY7cIf3xpCEwnt6D+ pjAzMQ23SIE9tBoCQR+3xccQ+O7zmCApnN+OT4HHollcm4At9oebxxrOmwOeYPoI 9I39YE8OJXBOe4FAL4/Yz067yVfvbzXpKTm2qLI5SNo6mtl5xKyX/ZBy49S58N12 KGOvTYGJEbMYMBWJbYvlL6+iNNZWG5PnBanMom+NFqqSf/1Auh97qgzjQMKXNuPj 4BmrIBoWidJ90sce58P8nqA8miYrB+rLEVVLudCPwmpd4cPbAqw29v7i62tV6wwl JWracTlUPNh/13HAVPCW57EUrny9YMqTLtRyvxbLcxXdduGiaGG39icXWa2OJwS9 skKRXgEgddwCEcH212n4riiazLT4ldZahGoUeXA64bKrKq/0UI5umtXjCioAY1qJ xv0wMw4DY1IbGrncF1ZIN/MeyAKzFNMxJc+LEPSTMSCa4WhpkVfgq/ZYxvKcKnbd yYtXrkqk4hQMzuuZ3/zfT09qDFbf3Le37JbS6C+HdoncsEfo3yrzOVMxS8qxb+u9 +/14hBcVxhrFQqG8ucjUcZ6v09Z2v7Eji+jg9Wa514pT+n8g3nVMeMGk9VEsGKsC gCrHq1L/gqx3xjZdppNfrVlI4DS6RsPrxf1+X3SdR9dCBR75ck0sskEenjeKuJEj PGZeBJJiQVlM4g6ECNR+RIRQ5J1mWO3uJLrIObxbh/1kz4RA3XoUiUB3/LuMxOhe waHT8BAMjzA4IHivWGlbFtR4uoObiMrtPEBq56ytqhUtgzIxd9rkYhGvYbagyIi0 yWRVjHr+qjNsgSRFz/1jhV17po0oshY0p8+3qFMkWujstRR01C67qDUTWlOPPqBw 2LpZ68hu879rc8W/VUECuKamhSSSEVXboqoGkpBeKkNpLIu12QBbNl9UmCCDb4IE qzTnTYv62/6fRp4U/E7N9uFw6VyEfgLr1C1nI4S2gRFh8vtFEFN6qoTCynH/Es4l n4clHQMmuKTI0ELoigU64183Nupm6JPwNk0N2BFagP8ZqvXJRs9d1OsxHHHDgoyj 9QkGsG3UgnsUIOpBT+3T+2pb8huywf9QOtET/yKMz3gIA+fGEEErBXhATkP1nWSa m8uvZjtefhNAFSvJnVumkzeQAlV5FxAEZteKCO86NzNDME9u+ESVHBe/CdozyqOl FOsquaIYj6ZemMyfsPjTRdyivvWMNAjVUrSpMHATm9fvirzIlPsomkAnXP+/DSQO iTkk8noZOkzBWQZg+cfwxpvpnyJHz9N8QSSMiFzUBiPYbbIJKl6HpeRMrsCiXTPz jgchLwMhuAKNHd5PAthwZvE3itzp57PVLKgDR0mSa3d3PuSDRLUuBp5anK/9y3ma JYAKjJfNM6NAMLasilPsRiIX3w5aECsc4musi1F1OxrJbj96dor2hB/yKYt6gYsC ee+hEbbIK9EkOrlFeqpEmbzYDcI+0x5HCIneSaCdqcC5YJMtK3lJvSLn05PoM7// NaL553RzC2oi0+QPS0pF9c1YU2mgk6NLRsDVhbJGZhDYzNQqqulw1HgbZTywPTmO dIeO9wwsP7j/ta/GjUU9HhLAdDWzUqscPlQaWbonkRZTL1RWDSo7s8fHrEdoB9UO 4M8e1PutRgLLeG2gkIpo3Rxjg602FTA7MB8wBwYFKw4DAhoEFDER5UKeOBAh4RBO O83/s4eE9UqWBBTYPw91q+QOWQb203T6YXzDn4yEzgICB9A= " />
    <add key="SigningCertificatePassword" value="idsrv3test" />
    <add key="RedisConnectionHost" value="buddypunch.redis.cache.windows.net" />
    <add key="RedisConnectionAccessKey" value="V9r+cZCmjGz7YFD5K3OQLMnwcsis90aHnx/zZ0ayQfc=" />
    <add key="CacheConnection" value="{0}:6380,password={1},ssl=True,abortConnect=False" />
    <add key="AppleTeamId" value="HVDZF65MSX" />
    <add key="AppleKey" value="MIGTAgEAMBMGByqGSM49AgEGCCqGSM49AwEHBHkwdwIBAQQgnzChq62dEMF4FoOno+n6TY8iyqSNLBVbG3e87WKH6RKgCgYIKoZIzj0DAQehRANCAASz5Nb8ymOno6iPE9iexoaGO5ZFIugeWSKQ/yhompikyUAOxP1cWCXBNzeyvepRNWIy+Dt8KjyRBibpJ3ao/q4Q" />
    <add key="AppleClientId" value="com.buddypunch.app" />
    <add key="AppleMobileClientId" value="com.BuddyPunch.Mobile" />
    <add key="HubSpotAccessKey" value="--KeyVaultSecret--" />
  </appSettings>
  <connectionStrings>
    <add name="MembershipProviderConnectionString" connectionString="Data Source=localhost;Initial Catalog=BuddyPunch;User Id=BuddyPunch2;Password=********;MultipleActiveResultSets=True" providerName="System.Data.SqlClient" />
    <add name="BuddyPunchContext" connectionString="metadata=res://*/BuddyPunch.csdl|res://*/BuddyPunch.ssdl|res://*/BuddyPunch.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=localhost;Initial Catalog=BuddyPunch;User Id=BuddyPunch2;Password=********;multipleactiveresultsets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
    <add name="BuddyPunch.IdSvr" connectionString="server=localhost;database=BuddyPunch;User Id=BuddyPunch2;Password=********;" providerName="System.Data.SqlClient" />
  </connectionStrings>
  <!--
    For a description of web.config changes see http://go.microsoft.com/fwlink/?LinkId=235367.

    The following attributes can be set on the <httpRuntime> tag.
      <system.Web>
        <httpRuntime targetFramework="4.6.1" />
      </system.Web>
  -->
  <system.web>
	  <compilation debug="true" targetFramework="4.7.2">
		  <assemblies>
			  <add assembly="netstandard, Version=*******, Culture=neutral,              PublicKeyToken=cc7b13ffcd2ddd51" />
		  </assemblies>
	  </compilation>
	  <httpRuntime targetFramework="4.7.2" />
  </system.web>
  <system.webServer>
    <modules runAllManagedModulesForAllRequests="true">
      <add name="RaygunErrorModule" type="Mindscape.Raygun4Net.RaygunHttpModule" />
    </modules>
    <handlers>
      <remove name="ExtensionlessUrlHandler-Integrated-4.0" />
      <remove name="OPTIONSVerbHandler" />
      <remove name="TRACEVerbHandler" />
      <add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="*" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />
    </handlers>
  </system.webServer>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" culture="neutral" publicKeyToken="30ad4fe6b2a6aeed" />
        <bindingRedirect oldVersion="0.0.0.0-1*******" newVersion="1*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Optimization" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-1.1.0.0" newVersion="1.1.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-1.6.5135.21930" newVersion="1.6.5135.21930" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Antlr3.Runtime" publicKeyToken="eb42632606e9261f" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.5.0.2" newVersion="3.5.0.2" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Http" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.7.0" newVersion="5.2.7.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Http.Formatting" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.7.0" newVersion="5.2.7.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Spatial" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.6.4.0" newVersion="5.0.2.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Data.OData" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.8.4.0" newVersion="5.8.4.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Primitives" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.1.1.0" newVersion="2.1.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.DependencyInjection.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.1.1.0" newVersion="2.1.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Options" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.1.1.0" newVersion="2.1.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.7.0" newVersion="5.2.7.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.6.0" newVersion="4.0.6.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.ValueTuple" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.IdentityModel.Tokens.Jwt" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.17.0.0" newVersion="6.17.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.0.0" newVersion="4.2.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.0.0" newVersion="4.2.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.IdentityModel.Logging" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.17.0.0" newVersion="6.17.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.IdentityModel.Tokens" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.17.0.0" newVersion="6.17.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="RestSharp" publicKeyToken="598062e77f915f75" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-106.15.0.0" newVersion="106.15.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Mindscape.Raygun4Net" publicKeyToken="d595e487e4f9f950" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-0.0.0.0" newVersion="0.0.0.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <!--<system.diagnostics>
    <trace autoflush="true" indentsize="4">
      <listeners>
        <add name="myListener" type="System.Diagnostics.TextWriterTraceListener" initializeData="Trace.log" />
        <remove name="Default" />
      </listeners>
    </trace>
  </system.diagnostics>-->
  <entityFramework codeConfigurationType="BuddyPunch.Model.MyConfiguration, BuddyPunch.Model.NetStandard">
    <defaultConnectionFactory type="System.Data.Entity.Infrastructure.LocalDbConnectionFactory, EntityFramework">
      <parameters>
        <parameter value="mssqllocaldb" />
      </parameters>
    </defaultConnectionFactory>
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
    </providers>
  </entityFramework>
</configuration>