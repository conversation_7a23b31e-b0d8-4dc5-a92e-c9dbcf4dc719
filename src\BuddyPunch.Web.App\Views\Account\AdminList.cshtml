﻿@using BuddyPunch.Web.App.Helpers
@using BuddyPunch.Web.App.ViewModel
@model BuddyPunch.Web.App.ViewModel.AdminListViewModel
@{
    ViewBag.Title = "Administrators";
}

<div class="m-alert alert alert-info">
    <p>
        Administrators have full access within Buddy Punch but are not allowed to punch in/out and do not count against your active employee count.<Br />
        <a class="alert-link" target="_blank" href="https://docs.buddypunch.com/faq/managers-what-are-they-and-how-do-they-work">Should I choose Manager or Administrator?</a>

    </p><br />
    <p>
        <b>Upgrade an Employee</b><br />
        The drop down list includes all of your active employees. If you would like to upgrade an employee into an administrator, select from the drop down menu and click the "Add" button.
    </p>
    <br />
    <p>
        <b>Remove an Administrator</b><br />
        Clicking Remove will downgrade the administrator to an inactive employee.
    </p>
</div>

<!-- BEGIN PAGE CONTENT-->
@using (Html.BeginMetronicPortlet("Administrators"))
{
    
    using (Html.BeginForm("AddAdmin", "Account", FormMethod.Post, new { @class = "form-horizontal" }))
    {
        <div>
            @Html.AntiForgeryToken()
            @Html.Partial("_ValidationSummary", ViewData.ModelState)

            <table class="m--margin-bottom-20">
                <tr>
                    <td style="padding-right: 5px;">
                        @Html.DropDownListFor(model => model.AdminId, Model.Employees.OrderBy(e => e.FullName).Select(e => new SelectListItem { Text = string.Format("{0} ({1})", e.FullName, e.Username), Value = e.Id.ToString() }), new { @class = "form-control input-large" })
                    </td>
                    <td>
                        <button type="submit" class="btn btn-primary add-admin"><i class="icon-ok"></i> Add</button>
                    </td>
                </tr>
            </table>
        </div>
    }

    <table class="table table-striped table-hover " id="employees_table">
        <thead>
            <tr>
                <th>Actions</th>
                <th>Username</th>
                <th>Email</th>
                <th>
                    First Name
                </th>
                <th>
                    Last Name
                </th>

                <th>
                    Active
                </th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in Model.Admins.OrderBy(e => e.FirstName).ThenBy(e => e.LastName))
            {
                <tr>
                    <td style="display: inline-flex;">
                        @if (Model.IsConfigurator)
                        {
                            <a class="btn btn-xs btn-sm btn-primary btn-admin-change-password" href="@Url.Action("ChangePassword", "Employee", new { id = item.Id, redirectTo = Context.Request.Path })">
                                <i class="fa fa-pencil"></i> Change Password
                            </a>
                        }

                        @if (item.Id != Model.UserId)
                        {
                            using (Html.BeginForm("RemoveAdmin", "Account", FormMethod.Post))
                            {
                                @Html.Hidden("id", item.Id)
                                <a href="#" class="btn btn-xs btn-sm btn-danger remove-admin"><i class="fa fa-check"></i> Remove</a>
                            }
                        }
                    </td>

                    <td>
                        @item.Username
                    </td>
                    <td>
                        @item.Email
                    </td>
                    <td>
                        @item.FirstName
                    </td>
                    <td>
                        @item.LastName
                    </td>

                    <td>
                        @(item.IsActive ? "Yes" : "No")
                    </td>
                </tr>
            }
        </tbody>
    </table>
}

@section PageHeadSection
{
}
@section PageBeforeEndBodySection
{
    <script type="text/javascript">
        jQuery(document).ready(function() {

            $('.remove-admin').click(function (event) {
                event.preventDefault();
               
                    swal({
                        title: "Are you sure?",
                        html: "The administrator will be downgraded to an <strong>inactive</strong> employee.",
                        type: "warning",
                        showCancelButton: !0,
                        confirmButtonText: "Yes, I understand"
                    }).then(function (e) {
                        if (e.value) {
                            $(event.target).parents('form').submit();
                        }
                    });
                
            });

            $('.add-admin').click(function(event) {
                event.preventDefault();
                if ($('#AdminId').val() == null) {
                    swal({
                        title: "Please add a new employee",
                        html: "You need to add a new employee to your employees list before you can upgrade the employee to an administrator",
                        type: "error"
                    });
                } else {
                    @{
                        var html = !Model.HasPayrollEnabled ? "Your new administrator will no longer be able to record their own time.<br/><br/><a href='https://docs.buddypunch.com/faq/how-can-admins-or-administrators-track-their-own-time' target='_blank'>Learn how administrators can track their own time.</a>" :
                            "<div class='row'>" +
                                "<div class='col-6 pr-4'>" +
                                    "Administrators cannot:" +
                                "</div>" +
                                "<div class='col-6 pl-0 pr-0'>" +
                                    "<div class='row'>" +
                                        "<div class='col-12 text-left pl-0 pr-0'>" +
                                            "<i class='fas fa-times-circle text-danger pr-1'></i>Track their own time" +
                                        "</div>" +
                                        "<div class='col-12 text-left pl-0 pr-0 mt-2'>" +
                                            "<i class='fas fa-times-circle text-danger pr-1'></i>Run Payroll for themselves" +
                                        "</div>" +
                                    "</div>" +
                                "</div>" +
                              "</div>" +
                              "<br/><br/><a href='https://docs.buddypunch.com/faq/how-can-admins-or-administrators-track-their-own-time' target='_blank'>Learn how administrators can track their own time.</a>";
                    }

                    swal({
                        title: "Are you sure?",
                        html: "@Html.Raw(html)",
                        type: "warning",
                        showCancelButton: !0,
                        confirmButtonText: "Yes, I understand"
                    }).then(function (e) {
                        if (e.value) {
                            $(event.target).parents('form').submit();
                        }
                    });
                }
            });
        });
    </script>
}