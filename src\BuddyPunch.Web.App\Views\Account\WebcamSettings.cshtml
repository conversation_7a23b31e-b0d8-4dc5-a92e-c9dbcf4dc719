﻿@using BuddyPunch.Web.App.Helpers
@model BuddyPunch.Web.App.ViewModel.AccountWebcamSettingsModel
@{
    ViewBag.Title = "Update Webcam Settings";
}
@if (!string.IsNullOrEmpty(Model.SuccessMessage))
{
    <div class="alert alert-success alert-dismissible fade show">
        <button type="button" class="close" data-dismiss="alert" aria-hidden="true"></button>
        @Model.SuccessMessage
    </div>
}
<!-- BEGIN PAGE CONTENT-->
@using (Html.BeginMetronicPortlet("Webcam Settings"))
{

    using (Html.BeginForm("WebcamSettings", "Account", FormMethod.Post, new { @class = "" }))
    {
        <div class="form-body">
            <div class="row">
                <div class="col-md-6">
                    @Html.AntiForgeryToken()
                    @Html.Partial("_ValidationSummary", ViewData.ModelState)

                    <div class="alert alert-block alert-info">
                        <p>Buddy Punch can take a photo when your employees punch in or out. </p>

                        <p>Note that not all devices have a webcam available.</p>

                        <span>Do not use <strong>Required</strong> if the employee's device does not have a webcam available.</span>
                    </div>
                    <div class="form-group">
                        @Html.MetronicCheckboxFor(model => model.WebcamEnabled, "Enable Webcam on Punches")
                    </div>
                    <div id="employee_list_area">
                        <table id="employees_table" class="table table-striped table-hover">
                            <thead>
                            <tr>
                                <th>Employee Name</th>
                                <th style="width: 25%">Off</th>
                                <th style="width: 25%">Optional</th>
                                <th style="width: 25%">Required</th>
                            </tr>
                            </thead>
                            <tbody>
                            @for (int i = 0; i < Model.EmployeeWebcamPermissions.Count; i++)
                            {
                                <tr>
                                    <td>
                                        @Model.EmployeeWebcamPermissions[i].FullName
                                        @Html.HiddenFor(m => m.EmployeeWebcamPermissions[i].Id)
                                    </td>
                                    <td>
                                        <label class="m-radio">
                                            @Html.RadioButtonFor(m => m.EmployeeWebcamPermissions[i].PermissionId, "1",
                                                Model.EmployeeWebcamPermissions[i].PermissionId == 1 ? new { @checked = "checked" } : null)

                                            <span></span>
                                            &nbsp;
                                        </label>
                                    </td>
                                    <td>
                                        <label class="m-radio">
                                            @Html.RadioButtonFor(m => m.EmployeeWebcamPermissions[i].PermissionId, "2",
                                                Model.EmployeeWebcamPermissions[i].PermissionId == 2 ? new { @checked = "checked" } : null)

                                            <span></span>
                                            &nbsp;
                                        </label>
                                    </td>
                                    <td>
                                        @*@Html.RadioButton(string.Format("SelectedPermissions[{0}]", employee.Id), "0", employee.PermissionId == 0)*@
                                        <label class="m-radio">
                                            @Html.RadioButtonFor(m => m.EmployeeWebcamPermissions[i].PermissionId, "3",
                                                Model.EmployeeWebcamPermissions[i].PermissionId == 0 ? new { @checked = "checked" } : null)

                                            <span></span>
                                            &nbsp;
                                        </label>
                                    </td>
                                </tr>
                            }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="form-actions">
                <button type="submit" class="btn btn-primary"><i class="icon-ok"></i> Save</button>
                @Html.ActionLink("Cancel", "Index", "Home", null, new { @class = "btn btn-default" })
            </div>
        </div>
    }

}


@section PageHeadSection
{
}

@section PageBeforeEndBodySection
{

    <script>
        jQuery(document).ready(function () {
            var table = $("#employee_list_area");

            if ($("[type=checkbox]").is(":checked")) {
                table.show();
            } else {
                table.hide();
            }

            var checkBox = $("[type=checkbox]").change(function () {
                var checked = jQuery(this).is(":checked");
                if (checked) {
                    table.show();
                } else {
                    table.hide();
                }
            }
            );

            $('#employees_table').DataTable( {
                fixedHeader: {
                    headerOffset: $('.m-header').outerHeight(),
                },
                searching: false,
                lengthChange: false,
                paging: false,
                ordering: false
            } );
        }
        );
    </script>
}