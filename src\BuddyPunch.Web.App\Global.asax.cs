﻿using Audit.Core;
using Audit.Mvc;
using BuddyPunch.Api.Models.Api;
using BuddyPunch.Check;
using BuddyPunch.Data.Infrastructure;
using BuddyPunch.Data.Repositories;
using BuddyPunch.Email;
using BuddyPunch.Helpers;
using BuddyPunch.Model;
using BuddyPunch.Model.Interfaces;
using BuddyPunch.Model.SoftDeletion;
using BuddyPunch.Push;
using BuddyPunch.Service;
using BuddyPunch.Service.Common.Options;
using BuddyPunch.Service.Interfaces;
using BuddyPunch.Service.Utilities;
using BuddyPunch.Web.App.ApiClient;
using BuddyPunch.Web.App.Dto;
using BuddyPunch.Web.App.Helpers;
using BuddyPunch.Web.App.Helpers.ModelBinder;
using BuddyPunch.Web.App.Helpers.QuickBooks;
using BuddyPunch.Web.App.IoC;
using BuddyPunch.Web.App.Log;
using BuddyPunch.Web.App.Managers;
using BuddyPunch.Web.App.Mappers;
using BuddyPunch.WebHooks;
using FluentValidation;
using FluentValidation.Mvc;
using Microsoft.Practices.Unity;
using Mindscape.Raygun4Net;
using Mindscape.Raygun4Net.Messages;
using MvcThrottle;
using QbSync.WebConnector.Synchronous;
using System;
using System.Configuration;
using System.Data.Entity.Infrastructure.Interception;
using System.Net;
using System.Net.Http;
using System.Reflection;
using System.Threading;
using System.Web;
using System.Web.Mvc;
using System.Web.Optimization;
using System.Web.Routing;
using System.Web.Security;
using PostTaxDeductionService = BuddyPunch.Check.PostTaxDeductionService;
using Microsoft.Extensions.DependencyInjection;

namespace BuddyPunch.Web.App
{
    // Note: For instructions on enabling IIS6 or IIS7 classic mode,
    // visit http://go.microsoft.com/?LinkId=9394801

    public class MvcApplication : HttpApplication, IRaygunApplication
    {
        public static void RegisterGlobalFilters(GlobalFilterCollection filters)
        {
            //register CoupleSessionAndFormsAuth Filter for all controllers
            //filters.Add(new CoupleSessionAndFormsAuth());
            filters.Add(new AjaxErrorAttribute());
            filters.Add(new HandleErrorAttribute());
            filters.Add(new AjaxAuthorizeAttribute());
            filters.Add(new RequireHttpsAttribute());
            filters.Add(new HandleHttpRequestValidationExceptionAttribute());
            filters.Add(new SetupMFAActionFilter());

            var throttleFilter = new MvcThrottleCustomFilter
            {
                Policy = new ThrottlePolicy(perSecond: 1, perMinute: 10, perHour: 60 * 10, perDay: 600 * 10)
                {
                    IpThrottling = true,
                },
                QuotaExceededMessage = "You have tried too many times. You are allowed {0} per {1}. Please try again later.",
                Repository = new CacheRepository()
            };

            filters.Add(throttleFilter);
            //filters.Add(new AuthContextFilter());
            //filters.Add(new AuditAttribute
            //{
            //    EventTypeName = "MVC:{verb}:{controller}:{action}",
            //    IncludeHeaders = false,
            //    IncludeModel = false,
            //    IncludeRequestBody = false,
            //    IncludeResponseBody = false
            //});
        }

        protected void Application_BeginRequest(Object sender, EventArgs e)
        {
            HttpContextBase ctx = new HttpContextWrapper(Context);
            foreach (Route rte in RouteTable.Routes)
            {
                if (rte.GetRouteData(ctx) != null)
                {
                    if (rte.RouteHandler.GetType().Name == "MvcRouteHandler")
                    {
                        Console.Write(string.Format("Following route: {1} for request: {0}", Context.Request.Url, rte.Url));
                    }
                    else
                    {
                        //{System.Web.Routing.StopRoutingHandler}
                        Console.Write(string.Format("Ignoring via route: {1} for request: {0}", Context.Request.Url, rte.Url));
                    }
                    break;
                }
            }
        }

        protected void Application_PostAcquireRequestState()
        {
            if (SessionManager.Profile.LoggedInUserViewModel != null)
            {
                var currentThreadCurrentCulture = new System.Globalization.CultureInfo("en-us");

                if (SessionManager.Profile.LoggedInUserViewModel.Account.DateLocale != "en-US")
                {
                    var french = new System.Globalization.CultureInfo("fr-FR");

                    currentThreadCurrentCulture.DateTimeFormat.ShortDatePattern = french.DateTimeFormat.ShortDatePattern;
                    currentThreadCurrentCulture.DateTimeFormat.LongDatePattern = french.DateTimeFormat.LongDatePattern;
                }
                if (SessionManager.Profile.LoggedInUserViewModel.Account.TimeFormat != 12)
                {
                    var french = new System.Globalization.CultureInfo("fr-FR");
                    currentThreadCurrentCulture.DateTimeFormat.ShortTimePattern = french.DateTimeFormat.ShortTimePattern;
                    currentThreadCurrentCulture.DateTimeFormat.LongTimePattern = french.DateTimeFormat.LongTimePattern;
                }
                Thread.CurrentThread.CurrentCulture = currentThreadCurrentCulture;
                Thread.CurrentThread.CurrentUICulture = Thread.CurrentThread.CurrentCulture;
            }
        }

        public static void RegisterRoutes(RouteCollection routes)
        {
            routes.IgnoreRoute("Scripts/{*pathInfo}");
            routes.IgnoreRoute("assets/{*pathInfo}");
            routes.IgnoreRoute("{resource}.axd/{*pathInfo}");
            routes.IgnoreRoute("elmah.axd");
            routes.IgnoreRoute("{*favicon}", new { favicon = @"(.*/)?favicon.ico(/.*)?" });


            routes.MapRoute(
                "QRScanner", // Route name
                "qrscan", // URL with parameters
                new { controller = "Home", action = "QRScan" }, // Parameter defaults
                new[] { "BuddyPunch.Web.App.Controllers" }
                );

            routes.MapRoute(
                "ICal", // Route name
                "ICal/{action}/{staffId}/{hash}/{seed}", // URL with parameters
                new { controller = "ICal", seed = UrlParameter.Optional }, // Parameter defaults
                new[] { "BuddyPunch.Web.App.Controllers" }
            );

            routes.MapRoute(
                "Default", // Route name
                "{controller}/{action}/{id}", // URL with parameters
                new { controller = "Home", action = "Index", id = UrlParameter.Optional }, // Parameter defaults
                 new[] { "BuddyPunch.Web.App.Controllers" }
                 );
        }

        protected void Application_Start()
        {
            ConfigurationHelper.Initialize(null, ConfigurationManager.AppSettings);
            MailHelper.Initialize(isNewApi: false);

            int defaultMinWorkerThreads;
            int defaultMinIoThreads;
            string strDIo = ConfigurationHelper.Configuration["DefaultMinWorkerThreads"];
            string strDWo = ConfigurationHelper.Configuration["DefaultMinIoThreads"];
            if (!int.TryParse(strDIo, out defaultMinIoThreads))
            {
                defaultMinIoThreads = 300;
            }

            if (!int.TryParse(strDWo, out defaultMinWorkerThreads))
            {
                defaultMinWorkerThreads = 300;
            }

            ThreadPool.SetMinThreads(defaultMinWorkerThreads, defaultMinIoThreads);

            MvcHandler.DisableMvcResponseHeader = true;

            AreaRegistration.RegisterAllAreas();

            RegisterGlobalFilters(GlobalFilters.Filters);
            RegisterRoutes(RouteTable.Routes);
            BundleConfig.RegisterBundles(BundleTable.Bundles);

            ModelBinders.Binders.Add(typeof(DateTime), new DateTimeModelBinder());
            ModelBinders.Binders.Add(typeof(DateTime?), new NullableDateTimeModelBinder());

            DbFilterRegister.AddDbFilters();

            ConfigureAuditLogDataProvider();

            IUnityContainer container = GetUnityContainer();
            //var serviceLocator = new UnityServiceLocator(container);
            //ServiceLocator.SetLocatorProvider(() => serviceLocator);

            RegisterHttpClientFactory(container);

            DependencyResolver.SetResolver(new UnityDependencyResolver(container));
            ControllerBuilder.Current.SetControllerFactory(new IoC.UnityControllerFactory(container));
            log4net.Config.XmlConfigurator.Configure();

            QBWebConnector.QbManager = (QBWebConnector qbConnectorSync) =>
            {

                return DependencyResolver.Current
                    .GetService(typeof(QbManager)) as QuickBooksManager;


                // return new QuickBooksManager(quickBooksAuthenticator);
            };

            FluentValidationModelValidatorProvider.Configure(provider =>
            {
                provider.ValidatorFactory = new FluentValidatorFactory(container);
            });

            DbInterception.Add(new LazyLoadingInterceptor((int)TimeSpan.FromDays(1).TotalMilliseconds, $"WebApp_{Settings.ApplicationSettings.Environment}"));

            Email.EmailService.InitiateLogging(ConfigurationManager.ConnectionStrings["ApplicationInsightsConnectionString"].ConnectionString);
        }

        private static void RegisterHttpClientFactory(IUnityContainer container)
        {
            var services = new Microsoft.Extensions.DependencyInjection.ServiceCollection();

            // Register IHttpClientFactory
            services.AddHttpClient();

            services.AddHttpClient(NamedHttpClients.RestClient)
                .ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler()
                {
                    UseCookies = false
                });

            // Build the service provider
            var serviceProvider = services.BuildServiceProvider();

            container.RegisterInstance(serviceProvider.GetService<IHttpClientFactory>());
        }

        public static class NamedHttpClients
        {
            public const string RestClient = "RestClient";
        }

        private static void ConfigureAuditLogDataProvider()
        {
            Audit.Core.Configuration.Setup()
                .UseSqlServer(config => config
                    .ConnectionString(ConfigurationManager.ConnectionStrings["MembershipProviderConnectionString"]
                        .ConnectionString)
                    .Schema("dbo")
                    .TableName("AuditEvent")
                    .IdColumnName("AuditEventId")
                    .JsonColumnName("Data")
                    .LastUpdatedColumnName("LastUpdatedDate")).WithCreationPolicy(EventCreationPolicy.InsertOnEnd);

            Audit.Core.Configuration.AddCustomAction(ActionType.OnScopeCreated, scope =>
            {
                var ev = scope.Event.GetMvcAuditAction();
                //if (ev.UserName.Equals("Anonymous"))
                //{
                //    scope.Discard();
                //}
                //else 
                if (ev.ControllerName.Equals("Master"))
                {
                    scope.Discard();
                }
                else if (ev.ActionName.Equals("KeepAlive"))
                {
                    scope.Discard();
                }
                else
                {
                    if (SessionManager.Profile.LoggedInUserViewModel != null)
                    {
                        if (ev != null)
                        {
                            ev.UserName = SessionManager.Profile.LoggedInUserViewModel.UserName;
                        }
                        scope.SetCustomField("AccountId", SessionManager.Profile.LoggedInUserViewModel.AccountId);
                    }
                }
            });
            Audit.Core.Configuration.AddCustomAction(ActionType.OnEventSaving, scope =>
            {
                var ev = scope.Event.GetMvcAuditAction();

                if (SessionManager.Profile.LoggedInUserViewModel != null)
                {
                    if (ev != null)
                    {
                        ev.UserName = SessionManager.Profile.LoggedInUserViewModel.UserName;
                    }

                    scope.SetCustomField("AccountId", SessionManager.Profile.LoggedInUserViewModel.AccountId);
                }
            });
        }

        private IUnityContainer GetUnityContainer()
        {
            //Create UnityContainer
            IUnityContainer container = new UnityContainer()
                    //.RegisterType<IControllerActivator, CustomControllerActivator>() // No nned to a controller activator

                    .RegisterInstance<MembershipProvider>(Membership.Provider)
                    .RegisterType<IHttpContextProvider, HttpContextProvider>(new HttpContextLifetimeManager<IHttpContextProvider>())
                    .RegisterType<IConfigurationProvider, ConfigurationProvider>(new HttpContextLifetimeManager<IConfigurationProvider>())

                    // Check Dependencies

                    .RegisterType<ICheckConfiguration, CheckConfiguration>(new HttpContextLifetimeManager<ICheckConfiguration>())

                    .RegisterType<IBankAccountService, BankAccountService>(new HttpContextLifetimeManager<IBankAccountService>())
                    .RegisterType<ICompanyService, CompanyService>(new HttpContextLifetimeManager<ICompanyService>())
                    .RegisterType<ICompanyBenefitService, CompanyBenefitService>(new HttpContextLifetimeManager<ICompanyBenefitService>())
                    .RegisterType<IContractorPaymentService, ContractorPaymentService>(new HttpContextLifetimeManager<IContractorPaymentService>())
                    .RegisterType<IContractorService, ContractorService>(new HttpContextLifetimeManager<IContractorService>())
                    .RegisterType<IDocumentService, DocumentService>(new HttpContextLifetimeManager<IDocumentService>())
                    .RegisterType<IEarningCodeService, EarningCodeService>(new HttpContextLifetimeManager<IEarningCodeService>())
                    .RegisterType<IEmployeeBenefitService, EmployeeBenefitService>(new HttpContextLifetimeManager<IEmployeeBenefitService>())
                    .RegisterType<IEmployeeService, EmployeeService>(new HttpContextLifetimeManager<IEmployeeService>())
                    .RegisterType<IPayrollItemService, PayrollItemService>(new HttpContextLifetimeManager<IPayrollItemService>())
                    .RegisterType<IPayrollService, PayrollService>(new HttpContextLifetimeManager<IPayrollService>())
                    .RegisterType<IPayScheduleService, PayScheduleService>(new HttpContextLifetimeManager<IPayScheduleService>())
                    .RegisterType<IPostTaxDeductionService, PostTaxDeductionService>(new HttpContextLifetimeManager<IPostTaxDeductionService>())
                    .RegisterType<ITaxParameterService, TaxParameterService>(new HttpContextLifetimeManager<ITaxParameterService>())
                    .RegisterType<IWorkplaceService, WorkplaceService>(new HttpContextLifetimeManager<IWorkplaceService>())

                    // End Check Dependencies

                    .RegisterType<IDatabaseFactory, DatabaseFactory>(new HttpContextLifetimeManager<IDatabaseFactory>())
                    .RegisterType<IUnitOfWork, UnitOfWork>(new HttpContextLifetimeManager<IUnitOfWork>())
                    .RegisterType<IStateRepository, StateRepository>(new HttpContextLifetimeManager<IStateRepository>())
                    .RegisterType<IStateService, StateService>(new HttpContextLifetimeManager<IStateService>())
                    .RegisterType<IPaymentPlanRepository, PaymentPlanRepository>(
                        new HttpContextLifetimeManager<IPaymentPlanRepository>())
                    .RegisterType<IPaymentPlanService, PaymentPlanService>(
                        new HttpContextLifetimeManager<IPaymentPlanService>())

                    .RegisterType<IPayrollPaymentPlanRepository, PayrollPaymentPlanRepository>(
                        new HttpContextLifetimeManager<IPayrollPaymentPlanRepository>())
                    .RegisterType<IPayrollPaymentPlanService, PayrollPaymentPlanService>(
                        new HttpContextLifetimeManager<IPayrollPaymentPlanService>())

                    .RegisterType<IStripePriceItemRepository, StripePriceItemRepository>(
                        new HttpContextLifetimeManager<IStripePriceItemRepository>())
                    .RegisterType<ICrudService<StripeInvoiceItemLog>, CrudService<StripeInvoiceItemLog>>(
                        new HttpContextLifetimeManager<CrudService<StripeInvoiceItemLog>>())
                    .RegisterType<IRepository<StripeInvoiceItemLog>, BaseRepository<StripeInvoiceItemLog>>(
                        new HttpContextLifetimeManager<BaseRepository<StripeInvoiceItemLog>>())

                    .RegisterType<IStaffRepository, StaffRepository>(new HttpContextLifetimeManager<IStaffRepository>())
                    .RegisterType<IStaffService, StaffService>(new HttpContextLifetimeManager<IStaffService>())
                    .RegisterType<ICsvService, CsvService>()

                    .RegisterType<IUserRepository, UserRepository>(new HttpContextLifetimeManager<IUserRepository>())
                    .RegisterType<IRoleRepository, RoleRepository>(new HttpContextLifetimeManager<IRoleRepository>())
                    .RegisterType<ILoggingService, LoggingService>(new HttpContextLifetimeManager<ILoggingService>())
                    .RegisterType<IMapper<Staff, StaffCreateInput>, Mapper<Staff, StaffCreateInput>>(
                        new HttpContextLifetimeManager<IMapper<Staff, StaffCreateInput>>())
                    .RegisterType<IMapper<Staff, StaffEditInput>, Mapper<Staff, StaffEditInput>>(
                        new HttpContextLifetimeManager<IMapper<Staff, StaffEditInput>>())

                    .RegisterType<IMapper<User, UserEditInput>, Mapper<User, UserEditInput>>(
                        new HttpContextLifetimeManager<IMapper<User, UserEditInput>>())
                    .RegisterType<IMapper<User, ProfileEditInput>, Mapper<User, ProfileEditInput>>(
                        new HttpContextLifetimeManager<IMapper<User, ProfileEditInput>>())

                    .RegisterType<ISecurityService, SecurityService>(new HttpContextLifetimeManager<ISecurityService>())

                    .RegisterType<IJobCodeRepository, JobCodeRepository>(
                        new HttpContextLifetimeManager<IJobCodeRepository>())
                    .RegisterType<IJobCodeService, JobCodeService>(
                        new HttpContextLifetimeManager<IJobCodeService>())

                    .RegisterType<INotificationRepository, NotificationRepository>(
                        new HttpContextLifetimeManager<INotificationRepository>())
                    .RegisterType<INotificationService, NotificationService>(
                        new HttpContextLifetimeManager<INotificationService>())

                    .RegisterType<IPayAmountTimeTypeRepository, PayAmountTimeTypeRepository>(
                        new HttpContextLifetimeManager<IPayAmountTimeTypeRepository>())
                    .RegisterType<IPayAmountTimeTypeService, PayAmountTimeTypeService>(
                        new HttpContextLifetimeManager<IPayAmountTimeTypeService>())

                    .RegisterType<IPayTypeRepository, PayTypeRepository>(
                        new HttpContextLifetimeManager<IPayTypeRepository>())
                    .RegisterType<IPayTypeService, PayTypeService>(
                        new HttpContextLifetimeManager<IPayTypeService>())

                    .RegisterType<IBreakRuleRepository, BreakRuleRepository>(
                        new HttpContextLifetimeManager<IBreakRuleRepository>())
                    .RegisterType<IBreakRuleService, BreakRuleService>(
                        new HttpContextLifetimeManager<IBreakRuleService>())

                    .RegisterType<ILocationRepository, LocationRepository>(
                        new HttpContextLifetimeManager<ILocationRepository>())
                    .RegisterType<ILocationService, LocationService>(
                        new HttpContextLifetimeManager<ILocationService>())

                    .RegisterType<IIpAddressLockRepository, IpAddressLockRepository>(
                        new HttpContextLifetimeManager<IIpAddressLockRepository>())
                    .RegisterType<IIpAddressLockService, IpAddressLockService>(
                        new HttpContextLifetimeManager<IIpAddressLockService>())

                    .RegisterType<IDeviceLockRepository, DeviceLockRepository>(
                        new HttpContextLifetimeManager<IDeviceLockRepository>())
                    .RegisterType<IDeviceLockService, DeviceLockService>(
                        new HttpContextLifetimeManager<IDeviceLockService>())

                    .RegisterType<IOvertimeAlertRuleRepository, OvertimeAlertRuleRepository>(
                        new HttpContextLifetimeManager<IOvertimeAlertRuleRepository>())
                    .RegisterType<IOvertimeAlertRuleService, OvertimeAlertRuleService>(
                        new HttpContextLifetimeManager<IOvertimeAlertRuleService>())
                    .RegisterType<IStaffOvertimeAlertRuleRepository, StaffOvertimeAlertRuleRepository>(
                        new HttpContextLifetimeManager<IStaffOvertimeAlertRuleRepository>())
                    .RegisterType<IGenericRepository<OvertimeAlertRuleType>, GenericRepository<OvertimeAlertRuleType>>(
                        new HttpContextLifetimeManager<IGenericRepository<OvertimeAlertRuleType>>())

                    .RegisterType<IAlertRuleRepository, AlertRuleRepository>(
                        new HttpContextLifetimeManager<IAlertRuleRepository>())
                    .RegisterType<IAlertRuleService, AlertRuleService>(
                        new HttpContextLifetimeManager<IAlertRuleService>())
                    .RegisterType<IStaffAlertRuleRepository, StaffAlertRuleRepository>(
                        new HttpContextLifetimeManager<IStaffAlertRuleRepository>())
                    .RegisterType<IGenericRepository<AlertRuleType>, GenericRepository<AlertRuleType>>(
                        new HttpContextLifetimeManager<IGenericRepository<AlertRuleType>>())
                    .RegisterType<IAlertRuleBreakRuleRepository, AlertRuleBreakRuleRepository>(
                        new HttpContextLifetimeManager<IAlertRuleBreakRuleRepository>())

                    .RegisterType<IGenericRepository<UserPin>, GenericRepository<UserPin>>(
                        new HttpContextLifetimeManager<IGenericRepository<UserPin>>())
                    .RegisterType<IUserPinService, UserPinService>(
                        new HttpContextLifetimeManager<IUserPinService>())

                    .RegisterType<IPunchLimitRuleRepository, PunchLimitRuleRepository>(
                        new HttpContextLifetimeManager<IPunchLimitRuleRepository>())
                    .RegisterType<IPunchLimitRuleService, PunchLimitRuleService>(
                        new HttpContextLifetimeManager<IPunchLimitRuleService>())

                    .RegisterType<IAccountPTOEarningCodeRepository, AccountPTOEarningCodeRepository>(
                        new HttpContextLifetimeManager<IAccountPTOEarningCodeRepository>())
                    .RegisterType<ISeedPTOEarningCodeRepository, SeedPTOEarningCodeRepository>(
                        new HttpContextLifetimeManager<ISeedPTOEarningCodeRepository>())
                    .RegisterType<IAccountPTOEarningCodeService, AccountPTOEarningCodeService>(
                        new HttpContextLifetimeManager<IAccountPTOEarningCodeService>())

                    .RegisterType<INexmoRepository, NexmoRepository>(
                        new HttpContextLifetimeManager<INexmoRepository>())
                    .RegisterType<INexmoService, NexmoService>(
                        new HttpContextLifetimeManager<INexmoService>())

                    .RegisterType<IAuthenticator, QuickBooksAuthenticator>(
                        new HttpContextLifetimeManager<IAuthenticator>())
                    .RegisterType<QbManager, QuickBooksManager>(
                        new HttpContextLifetimeManager<QbManager>())
                    .RegisterType<IQuickBooksService, QuickBooksService>(
                        new HttpContextLifetimeManager<QuickBooksService>())
                    .RegisterType<IGenericRepository<QB_AuthenticationTicket>,
                        GenericRepository<QB_AuthenticationTicket>>(
                        new HttpContextLifetimeManager<GenericRepository<QB_AuthenticationTicket>>())
                    .RegisterType<IGenericRepository<QB_Login>, GenericRepository<QB_Login>>(
                        new HttpContextLifetimeManager<GenericRepository<QB_Login>>())
                    .RegisterType<IGenericRepository<QB_Employee>, GenericRepository<QB_Employee>>(
                        new HttpContextLifetimeManager<GenericRepository<QB_Employee>>())
                    .RegisterType<IGenericRepository<QB_Transaction>, GenericRepository<QB_Transaction>>(
                        new HttpContextLifetimeManager<GenericRepository<QB_Transaction>>())
                    .RegisterType<IGenericRepository<QB_PayrollItemWage>, GenericRepository<QB_PayrollItemWage>>(
                        new HttpContextLifetimeManager<GenericRepository<QB_PayrollItemWage>>())
                    .RegisterType<IGenericRepository<QB_LogMessage>, GenericRepository<QB_LogMessage>>(
                        new HttpContextLifetimeManager<GenericRepository<QB_LogMessage>>())
                    .RegisterType<IGenericRepository<QB_TimeTracking>, GenericRepository<QB_TimeTracking>>(
                        new HttpContextLifetimeManager<GenericRepository<QB_TimeTracking>>())
                    .RegisterType<IGenericRepository<QB_PunchAllocationQueue>,
                        GenericRepository<QB_PunchAllocationQueue>>(
                        new HttpContextLifetimeManager<GenericRepository<QB_PunchAllocationQueue>>())
                    .RegisterType<IGenericRepository<QB_Object>, GenericRepository<QB_Object>>(
                        new HttpContextLifetimeManager<GenericRepository<QB_Object>>())
                    .RegisterType<IGenericRepository<QB_KeyValuePair>, GenericRepository<QB_KeyValuePair>>(
                        new HttpContextLifetimeManager<GenericRepository<QB_KeyValuePair>>())
                    .RegisterType<IGenericRepository<QB_Employee_EarningCode_PayrollItemWage>, GenericRepository<QB_Employee_EarningCode_PayrollItemWage>>(
                        new HttpContextLifetimeManager<GenericRepository<QB_Employee_EarningCode_PayrollItemWage>>())

                    .RegisterType<IGenericRepository<TimesheetPunchAllocation>,
                        GenericRepository<TimesheetPunchAllocation>>(
                        new HttpContextLifetimeManager<GenericRepository<TimesheetPunchAllocation>>())
                    .RegisterType<IGenericRepository<TimesheetPunchLog>, GenericRepository<TimesheetPunchLog>>(
                        new HttpContextLifetimeManager<GenericRepository<TimesheetPunchLog>>())
                    .RegisterType<IGenericRepository<TimesheetApprovalHistory>, GenericRepository<TimesheetApprovalHistory>>(
                        new HttpContextLifetimeManager<IGenericRepository<TimesheetApprovalHistory>>())

                    .RegisterType<ICrudService<EarningCode>, CrudService<EarningCode>>(
                        new HttpContextLifetimeManager<CrudService<EarningCode>>())
                    .RegisterType<IRepository<EarningCode>, BaseRepository<EarningCode>>(
                        new HttpContextLifetimeManager<BaseRepository<EarningCode>>())

                    .RegisterType<ICrudService<Industry>, CrudService<Industry>>(
                        new HttpContextLifetimeManager<CrudService<Industry>>())
                    .RegisterType<IRepository<Industry>, BaseRepository<Industry>>(
                        new HttpContextLifetimeManager<BaseRepository<Industry>>())

                    .RegisterType<ICrudService<DiscoveryMethod>, CrudService<DiscoveryMethod>>(
                        new HttpContextLifetimeManager<CrudService<DiscoveryMethod>>())
                    .RegisterType<IRepository<DiscoveryMethod>, BaseRepository<DiscoveryMethod>>(
                        new HttpContextLifetimeManager<BaseRepository<DiscoveryMethod>>())

                    .RegisterType<ICrudService<JobRole>, CrudService<JobRole>>(
                        new HttpContextLifetimeManager<CrudService<JobRole>>())
                    .RegisterType<IRepository<JobRole>, BaseRepository<JobRole>>(
                        new HttpContextLifetimeManager<BaseRepository<JobRole>>())

                    .RegisterType<ICrudService<AuditLog>, CrudService<AuditLog>>(
                        new HttpContextLifetimeManager<CrudService<AuditLog>>())
                    .RegisterType<IRepository<AuditLog>, BaseRepository<AuditLog>>(
                        new HttpContextLifetimeManager<BaseRepository<AuditLog>>())

                    .RegisterType<IBaseService<OnboardingFeature>, BaseService<OnboardingFeature>>(
                        new HttpContextLifetimeManager<BaseService<OnboardingFeature>>())
                    .RegisterType<IGenericRepository<OnboardingFeature>, GenericRepository<OnboardingFeature>>(
                        new HttpContextLifetimeManager<GenericRepository<OnboardingFeature>>())

                    .RegisterType<IRepository<ShiftColor>, BaseRepository<ShiftColor>>(
                        new HttpContextLifetimeManager<BaseRepository<ShiftColor>>())

                    .RegisterType<IOvertimeTypeRepository, OvertimeTypeRepository>(
                        new HttpContextLifetimeManager<IOvertimeTypeRepository>())
                    .RegisterType<IOvertimeTypeService, OvertimeTypeService>(
                        new HttpContextLifetimeManager<IOvertimeTypeService>())
                    .RegisterType<IAccountRepository, AccountRepository>(
                        new HttpContextLifetimeManager<IAccountRepository>())
                    .RegisterType<IAccountService, AccountService>(new HttpContextLifetimeManager<IAccountService>())
                    .RegisterType<IRecurrencePeriodRepository, RecurrencePeriodRepository>(
                        new HttpContextLifetimeManager<IRecurrencePeriodRepository>())
                    .RegisterType<IRecurrencePeriodService, RecurrencePeriodService>(
                        new HttpContextLifetimeManager<IRecurrencePeriodService>())
                    .RegisterType<IPayPeriodScheduleRepository, PayPeriodScheduleRepository>(
                        new HttpContextLifetimeManager<IPayPeriodScheduleRepository>())
                    .RegisterType<IPayPeriodScheduleService, PayPeriodScheduleService>(
                        new HttpContextLifetimeManager<IPayPeriodScheduleService>())
                    .RegisterType<IPunchService, PunchService>(new HttpContextLifetimeManager<IPunchService>())
                    .RegisterType<ITimesheetRepository, TimesheetRepository>(
                        new HttpContextLifetimeManager<ITimesheetRepository>())
                    .RegisterType<ITimesheetPunchRepository, TimesheetPunchRepository>(
                        new HttpContextLifetimeManager<ITimesheetPunchRepository>())
                    .RegisterType<IPayPeriodRepository, PayPeriodRepository>(
                        new HttpContextLifetimeManager<IPayPeriodRepository>())
                    .RegisterType<IPayPeriodService, PayPeriodService>(
                        new HttpContextLifetimeManager<IPayPeriodService>())
                    .RegisterType<ITimesheetRepository, TimesheetRepository>(
                        new HttpContextLifetimeManager<ITimesheetRepository>())
                    .RegisterType<ITimesheetService, TimesheetService>(
                        new HttpContextLifetimeManager<ITimesheetService>())
                    .RegisterType<IImageRepository, ImageRepository>(
                        new HttpContextLifetimeManager<IImageRepository>())
                    .RegisterType<IImageService, ImageService>(new HttpContextLifetimeManager<IImageService>())
                    .RegisterType<IPunchRoundingRuleService, PunchRoundingRuleService>(
                        new HttpContextLifetimeManager<IPunchRoundingRuleService>())
                    .RegisterType<IPunchRoundingRuleRepository, PunchRoundingRuleRepository>(
                        new HttpContextLifetimeManager<IPunchRoundingRuleRepository>())
                    .RegisterType<IMapper<PayPeriod, PayPeriodEditInput>, Mapper<PayPeriod, PayPeriodEditInput>>(
                        new HttpContextLifetimeManager<IMapper<PayPeriod, PayPeriodEditInput>>())
                    .RegisterType<IPTOAccrualRuleRepository, PTOAccrualRuleRepository>(
                        new HttpContextLifetimeManager<IPTOAccrualRuleRepository>())
                    .RegisterType<IPTOAccrualRuleService, PTOAccrualRuleService>(
                        new HttpContextLifetimeManager<BaseRepository<PTOAccrualRule>>())
                    .RegisterType<IPTOBlackoutDateRepository, PTOBlackoutDateRepository>(
                        new HttpContextLifetimeManager<IPTOBlackoutDateRepository>())
                    .RegisterType<IPTOBlackoutDateService, PTOBlackoutDateService>(
                        new HttpContextLifetimeManager<BaseRepository<PTOBlackoutDate>>())
                    .RegisterType<IPTOCompanyCalendarRepository, PTOCompanyCalendarRepository>(
                        new HttpContextLifetimeManager<IPTOCompanyCalendarRepository>())
                    .RegisterType<IPTOCompanyCalendarService, PTOCompanyCalendarService>(
                        new HttpContextLifetimeManager<BaseRepository<PTOCompanyCalendar>>())
                    .RegisterType<IPTOBankTransactionRepository, PTOBankTransactionRepository>(
                        new HttpContextLifetimeManager<IPTOBankTransactionRepository>())
                    .RegisterType<IPTOBankTransactionService, PTOBankTransactionService>(
                        new HttpContextLifetimeManager<BaseRepository<PTOBankTransaction>>())

                    .RegisterType<ICrudService<TimesheetPunch>, CrudService<TimesheetPunch>>(
                        new HttpContextLifetimeManager<CrudService<TimesheetPunch>>())
                    .RegisterType<IRepository<TimesheetPunch>, BaseRepository<TimesheetPunch>>(
                        new HttpContextLifetimeManager<BaseRepository<TimesheetPunch>>())

                    .RegisterType<IPayPlusMappingRepository, PayPlusMappingRepository>(
                        new HttpContextLifetimeManager<IPayPlusMappingRepository>())
                    .RegisterType<IPayPlusMappingService, PayPlusMappingService>(
                        new HttpContextLifetimeManager<IPayPlusMappingService>())

                    .RegisterType<IAdpMappingRepository, AdpMappingRepository>(
                        new HttpContextLifetimeManager<IAdpMappingRepository>())
                    .RegisterType<IAdpMappingService, AdpMappingService>(
                        new HttpContextLifetimeManager<IAdpMappingService>())

                    .RegisterType<IStaffStatusRepository, StaffStatusRepository>(
                        new HttpContextLifetimeManager<IStaffStatusRepository>())
                    .RegisterType<IStaffStatusService, StaffStatusService>(
                        new HttpContextLifetimeManager<IStaffStatusService>())

                    .RegisterType<IWebHookSender, WebHookSender>(
                        new HttpContextLifetimeManager<IWebHookSender>())

                    .RegisterType<RestClient>(new HttpContextLifetimeManager<RestClient>())
                    .RegisterType<IIdentityApi, IdentityApiClient>(new HttpContextLifetimeManager<IdentityApiClient>())
                    .RegisterType<IFaceApi, FaceApiClient>(new HttpContextLifetimeManager<FaceApiClient>())
                    .RegisterType<ITimeCardApi, TimeCardApiClient>(new HttpContextLifetimeManager<TimeCardApiClient>())
                    .RegisterType<IPayPeriodApi, PayPeriodApiClient>(new HttpContextLifetimeManager<PayPeriodApiClient>())
                    .RegisterType<ILocationApi, LocationApiClient>(new HttpContextLifetimeManager<LocationApiClient>())
                    .RegisterType<IJobCodeApi, JobCodeApiClient>(new HttpContextLifetimeManager<JobCodeApiClient>())
                    .RegisterType<IPositionApi, PositionApiClient>(new HttpContextLifetimeManager<PositionApiClient>())
                    .RegisterType<IGeofenceApi, GeofenceApiClient>(new HttpContextLifetimeManager<GeofenceApiClient>())
                    .RegisterType<IPunchApi, PunchApiClient>(new HttpContextLifetimeManager<PunchApiClient>())
                    .RegisterType<IPTOApi, PTOApiClient>(new HttpContextLifetimeManager<PTOApiClient>())

                    .RegisterType<IAdpRunMappingRepository, AdpRunMappingRepository>(
                        new HttpContextLifetimeManager<IAdpRunMappingRepository>())
                    .RegisterType<IAdpRunMappingService, AdpRunMappingService>(
                        new HttpContextLifetimeManager<IAdpRunMappingService>())

                    .RegisterType<IPaylocityMappingRepository, PaylocityMappingRepository>(
                        new HttpContextLifetimeManager<IPaylocityMappingRepository>())
                    .RegisterType<IPaylocityMappingService, PaylocityMappingService>(
                        new HttpContextLifetimeManager<IPaylocityMappingService>())

                    .RegisterType<IWebHookSender, WebHookSender>(
                        new HttpContextLifetimeManager<IWebHookSender>())
                    .RegisterType<IPtoRequestRepository, PtoRequestRepository>(
                        new HttpContextLifetimeManager<IPtoRequestRepository>())
                    .RegisterType<IPtoRequestService, PtoRequestService>(
                        new HttpContextLifetimeManager<IPtoRequestService>())
                    .RegisterType<IPositionRepository, PositionRepository>(
                        new HttpContextLifetimeManager<IPositionRepository>())
                    .RegisterType<IPositionService, PositionService>(
                        new HttpContextLifetimeManager<IPositionService>())

                    .RegisterType<IShiftRepository, ShiftRepository>(
                        new HttpContextLifetimeManager<IShiftRepository>())
                    .RegisterType<IShiftLogRepository, ShiftLogRepository>(
                        new HttpContextLifetimeManager<IShiftLogRepository>())
                    .RegisterType<IShiftService, ShiftService>(
                        new HttpContextLifetimeManager<IShiftService>())

                    .RegisterType<IGeofenceRepository, GeofenceRepository>(
                        new HttpContextLifetimeManager<IGeofenceRepository>())
                    .RegisterType<IGeofenceService, GeofenceService>(
                        new HttpContextLifetimeManager<IGeofenceService>())
                    .RegisterType<IPtoService, PtoService>(new HttpContextLifetimeManager<IPtoService>())
                    .RegisterType<IPtoEmailService, PtoEmailService>(new HttpContextLifetimeManager<IPtoEmailService>())
                    .RegisterType<ISamlIdentityProviderService, SamlIdentityProviderService>(
                        new HttpContextLifetimeManager<ISamlIdentityProviderService>())

                    .RegisterType<IFeatureRepository, FeatureRepository>(
                        new HttpContextLifetimeManager<IFeatureRepository>())
                    .RegisterType<IFeatureService, FeatureService>(
                        new HttpContextLifetimeManager<IFeatureService>())

                    .RegisterType<IAddonPlanRepository, AddonPlanRepository>(
                        new HttpContextLifetimeManager<IAddonPlanRepository>())
                    .RegisterType<IAddonPlanService, AddonPlanService>(
                        new HttpContextLifetimeManager<IAddonPlanService>())

                    .RegisterType<IAddonFeatureRepository, AddonFeatureRepository>(
                        new HttpContextLifetimeManager<IAddonFeatureRepository>())
                    .RegisterType<IAddonFeatureService, AddonFeatureService>(
                        new HttpContextLifetimeManager<IAddonFeatureService>())

                    .RegisterType<IHttpProxy, HttpProxy>(new HttpContextLifetimeManager<IHttpProxy>())
                    .RegisterInstance(new ExploSettings()
                    {
                        AuthToken = Settings.ApplicationSettings.EXPLO_AUTH_TOKEN,
                        BaseUrl = Settings.ApplicationSettings.EXPLO_SERVICE_URL,
                        DashboardId = Settings.ApplicationSettings.EXPLO_DASHBOAR_ID,
                        Environment = Settings.ApplicationSettings.Environment
                    })
                    .RegisterType<IExploService, ExploService>(new HttpContextLifetimeManager<IExploService>())

                    .RegisterType<ICheck_CompanyService, Check_CompanyService>(
                        new HttpContextLifetimeManager<ICheck_CompanyService>())
                    .RegisterType<ICheck_CompanyRepository, Check_CompanyRepository>(
                        new HttpContextLifetimeManager<ICheck_CompanyRepository>())
                    .RegisterType<ICheck_EmployeeService, Check_EmployeeService>(
                        new HttpContextLifetimeManager<ICheck_EmployeeService>())
                    .RegisterType<ICheck_EmployeeRepository, Check_EmployeeRepository>(
                        new HttpContextLifetimeManager<ICheck_EmployeeRepository>())
                    .RegisterType<ICheck_ContractorService, Check_ContractorService>(
                        new HttpContextLifetimeManager<ICheck_ContractorService>())
                    .RegisterType<ICheck_ContractorRepository, Check_ContractorRepository>(
                        new HttpContextLifetimeManager<ICheck_ContractorRepository>())
                    .RegisterType<ICheck_WebhookEventService, Check_WebhookEventService>(
                        new HttpContextLifetimeManager<ICheck_WebhookEventService>())
                    .RegisterType<ICheck_WebhookEventRepository, Check_WebhookEventRepository>(
                        new HttpContextLifetimeManager<ICheck_WebhookEventRepository>())
                    .RegisterType<ICheck_EarningCodeTypeService, Check_EarningCodeTypeService>(
                        new HttpContextLifetimeManager<ICheck_EarningCodeTypeService>())
                    .RegisterType<ICheck_EarningCodeTypeRepository, Check_EarningCodeTypeRepository>(
                        new HttpContextLifetimeManager<ICheck_EarningCodeTypeRepository>())

                    .RegisterType<IMessageService, MessageService>(
                        new HttpContextLifetimeManager<IMessageService>())
                    .RegisterType<IMessageRepository, MessageRepository>(
                        new HttpContextLifetimeManager<IMessageRepository>())
                    .RegisterType<IEmailLogService, EmailLogService>(
                        new HttpContextLifetimeManager<IEmailLogService>())
                    .RegisterType<IPushLogService, PushLogService>(
                        new HttpContextLifetimeManager<IPushLogService>())
                    .RegisterType<IEventGridPublisherService, EventGridPublisherService>(
                        new ContainerControlledLifetimeManager())

                    .RegisterType<IGenericRepository<PayRateEarningCode>, GenericRepository<PayRateEarningCode>>(
                        new HttpContextLifetimeManager<IGenericRepository<PayRateEarningCode>>())
                    .RegisterType<IRepository<PayRate>, BaseRepository<PayRate>>(
                        new HttpContextLifetimeManager<IRepository<PayRate>>())
                    .RegisterType<IRepository<PayRateHistory>, BaseRepository<PayRateHistory>>(
                        new HttpContextLifetimeManager<IRepository<PayRateHistory>>())
                    .RegisterType<IPayRateService, PayRateService>(
                        new HttpContextLifetimeManager<IPayRateService>())
                    .RegisterType<IPayRateHistoryService, PayRateHistoryService>(
                        new HttpContextLifetimeManager<IPayRateHistoryService>())

                    .RegisterType<IRepository<Availability>, BaseRepository<Availability>>(
                        new HttpContextLifetimeManager<IRepository<Availability>>())
                    .RegisterType<IRepository<AvailabilityRecurrence>, BaseRepository<AvailabilityRecurrence>>(
                        new HttpContextLifetimeManager<IRepository<AvailabilityRecurrence>>())
                    .RegisterType<IAvailabilityService, AvailabilityService>(
                        new HttpContextLifetimeManager<IAvailabilityService>())

                    .RegisterType<IRepository<ShiftRecurrence>, BaseRepository<ShiftRecurrence>>(
                        new HttpContextLifetimeManager<IRepository<ShiftRecurrence>>())

                    .RegisterType<IGenericRepository<ValidationError>, GenericRepository<ValidationError>>(
                        new HttpContextLifetimeManager<IGenericRepository<ValidationError>>())
                    .RegisterType<IBaseService<ValidationError>, BaseService<ValidationError>>(
                        new HttpContextLifetimeManager<IBaseService<ValidationError>>())

                      .RegisterType<IRepository<ShiftTrade>, BaseRepository<ShiftTrade>>(
                    new HttpContextLifetimeManager<IRepository<ShiftTrade>>())
                .RegisterType<IShiftTradeService, ShiftTradeService>(
                    new HttpContextLifetimeManager<IShiftTradeService>())
                .RegisterType<IShiftNotificationService, ShiftNotificationService>(
                    new HttpContextLifetimeManager<IShiftNotificationService>())
                .RegisterType<IShiftTradeTargetRepository, ShiftTradeTargetRepository>(
                    new HttpContextLifetimeManager<IShiftTradeTargetRepository>())
                .RegisterType<IGenericRepository<Break>, GenericRepository<Break>>(
                        new HttpContextLifetimeManager<GenericRepository<Break>>())
                .RegisterType<ILemlistService, LemlistService>(
                    new HttpContextLifetimeManager<ILemlistService>())
                .RegisterType<IGenericRepository<FaceIdentifyOperation>, GenericRepository<FaceIdentifyOperation>>(
                    new HttpContextLifetimeManager<GenericRepository<FaceIdentifyOperation>>())
                .RegisterType<ILaborCostService, LaborCostService>(new HttpContextLifetimeManager<ILaborCostService>())
                .RegisterType<IGenericRepository<StripePriceMigration>, GenericRepository<StripePriceMigration>>(
                        new HttpContextLifetimeManager<GenericRepository<StripePriceMigration>>())
                .RegisterType<IGroupApiClient, GroupApiClient>(new HttpContextLifetimeManager<IGroupApiClient>())
            ;

            var validators = AssemblyScanner.FindValidatorsInAssembly(Assembly.GetExecutingAssembly());
            validators.ForEach(validator => container.RegisterType(validator.InterfaceType, validator.ValidatorType));

            WebHooksConfiguration.ConfigureServicesForSending(container);

            return container;
        }

        protected void Application_Error(object sender, EventArgs e)
        {
            var exception = Server.GetLastError();
            // Log the exception.
            Response.Clear();

            var httpException = exception as HttpException;

            var routeData = new RouteData();
            routeData.Values.Add("controller", "Error");

            if (httpException == null)
            {
                routeData.Values.Add("action", "Index");

                Response.StatusCode = (int)HttpStatusCode.InternalServerError;
            }
            else //It's an Http Exception, Let's handle it.
            {
                var responseStatusCode = httpException.GetHttpCode();

                Response.StatusCode = responseStatusCode;

                switch (responseStatusCode)
                {
                    case 404:
                        // Page not found.
                        routeData.Values.Add("action", "HttpError404");
                        break;
                    //case 505:
                    //    // Server error.
                    //    routeData.Values.Add("action", "HttpError505");
                    //    break;

                    // Here you can handle Views to other error codes.
                    // I choose a General error template  
                    default:
                        routeData.Values.Add("action", "Index");
                        break;
                }
            }

            // Pass exception details to the target error View.
            routeData.Values.Add("error", exception);

            // Clear the error on server.
            Server.ClearError();
            Response.TrySkipIisCustomErrors = true;
            // Call target Controller and pass the routeData.
            IController errorController = new Controllers.ErrorController();
            errorController.Execute(new RequestContext(
                new HttpContextWrapper(Context), routeData));
        }

        protected void Application_PreSendRequestHeaders()
        {
            Response.Headers.Remove("X-Powered-By");
            Response.Headers.Remove("Server");
            if (Request.Url.ToString().EndsWith(".html"))
            {

            }
            else
            {
                Response.Headers["X-FRAME-OPTIONS"] = "SAMEORIGIN";
            }

        }

        public RaygunClient GenerateRaygunClient()
        {
            var raygunClient = new RaygunClient(ConfigurationHelper.Configuration["RaygunSettingsApiKey"]);
            try
            {
                var loggedInUser = SessionManager.Profile.LoggedInUserViewModel;
                if (loggedInUser != null)
                {
                    raygunClient.UserInfo = new RaygunIdentifierMessage(loggedInUser.Id.ToString())
                    {
                        IsAnonymous = false,
                        FullName = loggedInUser.FullName,
                        FirstName = loggedInUser.FirstName,
                        Email = loggedInUser.Email
                    };
                }
            }
            catch
            {
                //swallow
            }
            return raygunClient;
        }
    }
}